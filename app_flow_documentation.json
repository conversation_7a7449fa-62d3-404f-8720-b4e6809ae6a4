{"app_name": "Salon Booking App", "version": "1.0.0", "last_updated": "2025-01-19", "architecture": "MVC with GetX", "theme": "Material 3 Design with Grey/Black/White + Gradients", "typography": "GoogleFonts.poppins", "authentication_flow": {"description": "Role-based authentication system with email/phone registration", "steps": [{"step": 1, "screen": "SocialLoginScreen", "route": "/social-login", "description": "Entry point with social login options", "actions": ["Navigate to authentication flow"]}, {"step": 2, "screen": "AuthenticationScreen", "route": "/authentication", "description": "Email/phone registration with role selection", "api_endpoint": "/auth/register", "request_body": {"email": "string", "password": "string", "role": "user | owner"}, "actions": ["Register user", "Send OTP", "Navigate to OTP verification"]}, {"step": 3, "screen": "OtpVerificationScreen", "route": "/otp-verification", "description": "4-digit OTP verification", "api_endpoint": "/auth/verify-otp", "request_body": {"email": "string", "otp": "string"}, "actions": ["Verify OTP", "Save token to SharedPreferences", "Navigate based on role"]}], "role_based_navigation": {"user": {"next_screen": "UserDetailFormScreen", "route": "/user-detail-form"}, "owner": {"next_screen": "OwnerDetailFormScreen", "route": "/owner-detail-form"}}}, "profile_creation_flow": {"user_registration": {"screen": "UserDetailFormScreen", "route": "/user-detail-form", "api_endpoint": "/auth/profile/user", "service": "RegistrationService.registerUser()", "model": "UserRegistrationRequest", "request_schema": {"firstName": "string (required)", "lastName": "string (required)", "phone": "string (min 10 digits)", "email": "string (email format)", "gender": "string (optional)", "profileImage": "string (Firebase URL, optional)", "pincode": "number (6 digits, optional)", "area": "string (required)", "city": "string (required)", "state": "string (required)", "country": "string (required)"}, "firebase_upload": {"method": "FirebaseStorageService.uploadUserProfileFile()", "storage_path": "user_profiles/", "compression": true, "max_size": "1024x1024"}, "success_navigation": {"route": "/user-bottom-navigation", "screen": "UserBottomNavigation"}}, "owner_registration": {"screen": "OwnerDetailFormScreen", "route": "/owner-detail-form", "api_endpoint": "/auth/profile/owner", "service": "RegistrationService.registerOwner()", "model": "OwnerRegistrationRequest", "request_schema": {"firstName": "string (required)", "lastName": "string (required)", "salonName": "string (required)", "salonDescription": "string (required)", "salonPhone": "string (required)", "salonEmail": "string (email format)", "area": "string (required)", "city": "string (required)", "state": "string (required)", "country": "string (required)", "pinCode": "string (required)", "salonRegId": "string (required)", "shopNo": "string (required)", "logoUrl": "string (Firebase URL, optional)"}, "firebase_upload": {"method": "FirebaseStorageService.uploadSalonLogo()", "storage_path": "salon_logos/", "compression": true, "max_size": "1024x1024", "progress_tracking": true}, "success_navigation": {"route": "/owner-bottom-navigation", "screen": "OwnerBottomNavigation"}}}, "main_app_navigation": {"user_flow": {"bottom_navigation": "UserBottomNavigation", "route": "/user-bottom-navigation", "tabs": [{"tab": "Home", "screen": "UserHomeScreen", "route": "/home", "features": ["Nearby salons", "Popular salons", "Search", "Booking"]}, {"tab": "Bookings", "screen": "UserBookingsScreen", "route": "/bookings", "features": ["Appointment history", "Upcoming bookings", "Cancel/Reschedule"]}, {"tab": "Profile", "screen": "UserProfileScreen", "route": "/profile", "features": ["Edit profile", "Settings", "Logout"]}]}, "owner_flow": {"bottom_navigation": "OwnerBottomNavigation", "route": "/owner-bottom-navigation", "tabs": [{"tab": "Dashboard", "screen": "OwnerDashboardScreen", "route": "/owner-dashboard", "features": ["Earnings", "Bookings", "Statistics", "Charts"]}, {"tab": "Services", "screen": "OwnerServicesScreen", "route": "/owner-services", "features": ["Add/Edit services", "Service management", "Pricing"]}, {"tab": "Barbers", "screen": "OwnerBarbersScreen", "route": "/owner-barbers", "features": ["Add/Edit barbers", "Barber management", "Availability"]}, {"tab": "Profile", "screen": "OwnerProfileScreen", "route": "/owner-profile", "features": ["Edit salon profile", "Settings", "Bank details", "Logout"]}]}}, "api_endpoints": {"authentication": {"register": {"method": "POST", "endpoint": "/auth/register", "description": "Register user with email/password and role"}, "verify_otp": {"method": "POST", "endpoint": "/auth/verify-otp", "description": "Verify 4-digit OTP"}, "login": {"method": "POST", "endpoint": "/auth/login", "description": "Login with email/password/role"}}, "profile_creation": {"user_profile": {"method": "POST", "endpoint": "/auth/profile/user", "description": "Create user profile after registration"}, "owner_profile": {"method": "POST", "endpoint": "/auth/profile/owner", "description": "Create owner profile after registration"}}, "user_apis": {"dashboard": {"method": "GET", "endpoint": "/user/dashboard", "description": "Get user dashboard data with nearby/popular salons"}, "salon_details": {"method": "GET", "endpoint": "/saloon/{id}", "description": "Get salon details, services, barbers, reviews"}}, "owner_apis": {"dashboard": {"method": "GET", "endpoint": "/owner/dashboard", "description": "Get owner dashboard with earnings, bookings"}, "profile": {"method": "GET", "endpoint": "/owner/profile", "description": "Get owner profile data"}, "update_profile": {"method": "PUT", "endpoint": "/owner/profile", "description": "Update owner profile"}, "services": {"method": "GET", "endpoint": "/owner/services", "description": "Get owner services"}, "add_service": {"method": "POST", "endpoint": "/owner/services", "description": "Add new service"}, "barbers": {"method": "GET", "endpoint": "/owner/barbers", "description": "Get salon barbers"}, "appointments": {"method": "GET", "endpoint": "/owner/appointments", "description": "Get salon appointments"}}}, "firebase_integration": {"project_id": "avtopia-6b597", "storage_structure": {"user_profiles/": "User profile photos", "salon_logos/": "Salon logo images", "service_images/": "Service photos", "barber_images/": "<PERSON> profile photos", "salon_gallery/": "Salon gallery images"}, "upload_methods": {"uploadUserProfileFile": "Upload user profile photo", "uploadSalonLogo": "Upload salon logo with progress tracking", "uploadServiceImage": "Upload service image", "uploadBarberImage": "Upload barber profile photo", "uploadSalonGalleryImage": "Upload salon gallery image"}}, "state_management": {"pattern": "GetX", "controllers": {"AuthController": "Handle authentication state", "UserController": "Manage user data and state", "OwnerController": "Manage owner data and state", "DashboardController": "Handle dashboard data", "ServiceController": "Manage services", "BarberController": "Manage barbers", "AppointmentController": "Handle appointments"}, "dependency_injection": "Get.lazyPut for controller injection"}, "data_persistence": {"shared_preferences": {"auth_token": "JWT authentication token", "user_role": "user | owner", "user_email": "Registered email", "is_logged_in": "Authentication status"}}, "ui_components": {"custom_widgets": {"CustomButton": "Reusable button with app theme", "CustomTextField": "Themed text input field", "CustomPhoneField": "Phone number input with validation", "ProfileImagePicker": "Image picker with camera/gallery options", "LoadingWidget": "App-themed loading indicator"}, "design_system": {"colors": "Grey, Black, White + Gradients", "typography": "GoogleFonts.poppins", "spacing": "AppConstants for consistent padding/margins", "animations": "Smooth transitions and micro-interactions"}}, "error_handling": {"network_errors": "Graceful handling with user-friendly messages", "validation_errors": "Real-time form validation", "api_errors": "Proper error parsing and display", "firebase_errors": "Upload failure handling with retry options"}, "current_issues_fixed": {"role_based_navigation": "Fixed owner registration navigating to user dashboard", "api_endpoints": "Separated user and owner registration endpoints", "service_architecture": "Created dedicated RegistrationService", "model_separation": "Created UserRegistrationRequest and OwnerRegistrationRequest models", "firebase_upload": "Simplified logo upload using existing ProfileImagePicker"}, "pending_work": {"high_priority": ["Complete owner dashboard implementation", "Implement appointment booking flow", "Add service management screens", "Implement barber management", "Add payment integration"], "medium_priority": ["Add search functionality", "Implement reviews and ratings", "Add notification system", "Implement chat feature", "Add analytics tracking"], "low_priority": ["Add dark mode support", "Implement offline mode", "Add multi-language support", "Performance optimizations", "Add unit tests"]}}