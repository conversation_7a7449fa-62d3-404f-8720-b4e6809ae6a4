---
type: "always_apply"
---

# 💈 Saloon Booking App – Project Rules

A cross-platform saloon appointment booking system with two modules: **User Side** and **Owner Side**. Built using Flutter with **MVC architecture** and **GetX** for state management.

---

## 📐 Architecture & Design

### ✅ Project Structure (MVC Pattern)
lib/
├── controller/ # All GetX controllers (UI logic)
├── model/ # All model classes
├── usersideview/ # All user-side UI screens
├── ownersideview/ # All owner-side UI screens
├── service/ # API integration & service classes
├── widget/ # Reusable custom widgets
├── theme/ # App themes, colors, text styles
├── bindings/ # Lazy loading bindings (Get.lazyPut)
└── main.dart # Entry point

---

## 🧠 Logic Layer

- Use **GetX controllers** to handle all business logic and state.
- Register controllers using **Get.lazyPut()** for memory efficiency.
- Separate controller for **each screen** and **each text field** to manage and validate data accurately.

---

## 🎯 UI Guidelines

- Use **Material 3 design** principles.
- All UI must be:
  - Modern, minimal, professional.
  - Responsive on all screen sizes.
  - Accessible and clean.

### 💡 Animations & UX
- Use smooth **page transitions** via `Get.to()` with custom transitions.
- Use animated **modal bottom sheets** and **dialog popups**.
- Avoid overuse of `Snackbar`. Use modals/dialogs where appropriate.
- Animate screen loads, popups, and interactions gracefully.

---

## 🎨 Theme & Design

- **Primary Colors:** Grey, Black, White
- **Accent:** Use gradient combinations from these colors.
- **Fonts:** Use `GoogleFonts.poppins` throughout.
- **Image Rendering:** Always use `CachedNetworkImage` to load images from the network.

---

## 🧱 Widget Reuse Rules

- All UI elements like buttons, text fields, containers, tiles, etc. must be:
  - Modularized into **custom widgets**
  - Reusable across the app
  - Handle `null`, `empty`, and `loading` states gracefully

---

## 🚫 Null Safety & Code Quality

- **Strictly follow null safety**.
- Avoid any UI crash due to missing or incorrect data.
- All widgets must:
  - Check for null or empty data
  - Render fallback content when data is unavailable

---

## 🚀 Optimization & Production Standards

- Follow **clean code principles**.
- Optimize widgets with `const` wherever possible.
- Use lazy initialization for better memory usage.
- Ensure efficient build methods to avoid unnecessary UI redraws.

---

## ✅ API & Service Rules

- Keep API calls and business logic in **service classes**.
- Parse all API responses into model classes.

---

## 🧪 Testing

- All components and custom widgets should be written in a testable way.
- Follow separation of concerns strictly for better maintainability.

---

