import 'lib/app/services/auth_service.dart';
import 'lib/app/services/shared_preferences_service.dart';

void main() async {
  print('🧪 Testing Logout Flow...\n');

  // Initialize SharedPreferences
  await SharedPreferencesService.init();

  // Simulate user being logged in
  print('1. Simulating user login...');
  await SharedPreferencesService.saveToken('test_token_123');
  await SharedPreferencesService.saveRole('user');
  await SharedPreferencesService.saveEmail('<EMAIL>');
  await SharedPreferencesService.saveUserId('user_123');
  await SharedPreferencesService.saveUserName('Test User');

  // Check if user is logged in
  final isLoggedInBefore = await AuthService.isLoggedIn();
  print('   ✅ User logged in: $isLoggedInBefore');

  // Get user data before logout
  final tokenBefore = await SharedPreferencesService.getToken();
  final roleBefore = await SharedPreferencesService.getRole();
  final emailBefore = await SharedPreferencesService.getEmail();
  print('   📋 Token: $tokenBefore');
  print('   📋 Role: $roleBefore');
  print('   📋 Email: $emailBefore\n');

  // Perform logout
  print('2. Performing logout...');
  final logoutSuccess = await AuthService.logout();
  print('   ✅ Logout successful: $logoutSuccess\n');

  // Check if user data is cleared
  print('3. Verifying data clearance...');
  final isLoggedInAfter = await AuthService.isLoggedIn();
  final tokenAfter = await SharedPreferencesService.getToken();
  final roleAfter = await SharedPreferencesService.getRole();
  final emailAfter = await SharedPreferencesService.getEmail();
  final userIdAfter = await SharedPreferencesService.getUserId();
  final userNameAfter = await SharedPreferencesService.getUserName();

  print('   ✅ User logged in: $isLoggedInAfter');
  print('   📋 Token: $tokenAfter');
  print('   📋 Role: $roleAfter');
  print('   📋 Email: $emailAfter');
  print('   📋 User ID: $userIdAfter');
  print('   📋 User Name: $userNameAfter\n');

  // Verify all data is cleared
  final allDataCleared = tokenAfter == null && 
                        roleAfter == null && 
                        emailAfter == null && 
                        userIdAfter == null && 
                        userNameAfter == null;

  if (allDataCleared && !isLoggedInAfter) {
    print('🎉 Logout flow test PASSED! All user data cleared successfully.');
  } else {
    print('❌ Logout flow test FAILED! Some data was not cleared.');
  }
}
