import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../app/models/booking_models.dart';
import '../app/services/booking_service.dart';
import '../model/reschedule_request.dart';
import '../model/reschedule_response.dart';
import '../service/reschedule_service.dart';

/// Controller for reschedule appointment functionality
class RescheduleController extends GetxController {
  final RescheduleService _rescheduleService = RescheduleService();

  // Appointment data
  late String appointmentId;
  late String saloonId;
  late String barberId;
  late List<String> serviceIds;

  // Date and time selection
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  final Rx<TimeSlot?> selectedTimeSlot = Rx<TimeSlot?>(null);
  final RxList<TimeSlot> availableTimeSlots = <TimeSlot>[].obs;

  // Loading states
  final RxBool isLoadingSlots = false.obs;
  final RxBool isRescheduling = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  // UI state
  final RxBool canReschedule = true.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeFromArguments();
  }

  /// Initialize controller with appointment data from navigation arguments
  void _initializeFromArguments() {
    final arguments = Get.arguments as Map<String, dynamic>?;

    if (arguments != null) {
      appointmentId = arguments['appointmentId'] as String? ?? '';
      saloonId = arguments['saloonId'] as String? ?? '';
      barberId = arguments['barberId'] as String? ?? '';

      // Handle serviceIds as list of strings
      final serviceIdsArg = arguments['serviceIds'];
      if (serviceIdsArg is List) {
        serviceIds = serviceIdsArg.map((id) => id.toString()).toList();
      } else {
        serviceIds = [];
      }

      log(
        'RescheduleController: Initialized with appointmentId: $appointmentId',
      );
      log('RescheduleController: SaloonId: $saloonId, BarberId: $barberId');
      log(
        'RescheduleController: ServiceIds: $serviceIds (${serviceIds.length} services)',
      );

      // Validate required parameters
      if (!_validateRequiredParameters()) {
        return;
      }

      // Debug: Print parameters after initialization
      debugPrintParameters();

      // Check if appointment can be rescheduled
      _checkRescheduleEligibility();
    } else {
      log('RescheduleController: No arguments provided');
      hasError.value = true;
      errorMessage.value = 'Invalid appointment data';
    }
  }

  /// Validate that all required parameters are present
  bool _validateRequiredParameters() {
    if (appointmentId.isEmpty) {
      log('RescheduleController: Missing appointmentId');
      hasError.value = true;
      errorMessage.value = 'Invalid appointment ID';
      return false;
    }

    if (saloonId.isEmpty) {
      log('RescheduleController: Missing saloonId');
      hasError.value = true;
      errorMessage.value = 'Invalid salon information';
      return false;
    }

    if (barberId.isEmpty) {
      log('RescheduleController: Missing barberId');
      hasError.value = true;
      errorMessage.value = 'Invalid barber information';
      return false;
    }

    if (serviceIds.isEmpty) {
      log('RescheduleController: Missing serviceIds');
      hasError.value = true;
      errorMessage.value = 'Invalid service information';
      return false;
    }

    log('RescheduleController: All required parameters validated successfully');
    return true;
  }

  /// Check if appointment can be rescheduled
  Future<void> _checkRescheduleEligibility() async {
    try {
      final eligible = await _rescheduleService.canRescheduleAppointment(
        appointmentId,
      );
      canReschedule.value = eligible;

      if (!eligible) {
        hasError.value = true;
        errorMessage.value = 'This appointment cannot be rescheduled';
      }
    } catch (e) {
      log('RescheduleController: Error checking reschedule eligibility: $e');
      canReschedule.value = false;
    }
  }

  /// Select date and fetch available slots
  void selectDate(DateTime date) {
    selectedDate.value = date;
    selectedTimeSlot.value = null; // Clear selected time slot
    log('RescheduleController: Selected date: ${date.toIso8601String()}');

    // Automatically fetch available slots when date is selected
    if (serviceIds.isNotEmpty) {
      fetchAvailableSlots();
    }
  }

  /// Select time slot
  void selectTimeSlot(TimeSlot timeSlot) {
    selectedTimeSlot.value = timeSlot;
    log(
      'RescheduleController: Selected time slot: ${timeSlot.displayTime12Hour}',
    );
  }

  /// Check if time slot is selected
  bool isTimeSlotSelected(TimeSlot timeSlot) {
    return selectedTimeSlot.value == timeSlot;
  }

  /// Fetch available time slots for selected date
  Future<void> fetchAvailableSlots() async {
    if (selectedDate.value == null) {
      log('RescheduleController: Cannot fetch slots - no date selected');
      _showErrorDialog('Please select a date first');
      return;
    }

    if (serviceIds.isEmpty) {
      log('RescheduleController: Cannot fetch slots - no services available');
      _showErrorDialog('No services found for this appointment');
      return;
    }

    if (saloonId.isEmpty || barberId.isEmpty) {
      log(
        'RescheduleController: Cannot fetch slots - missing salon or barber info',
      );
      _showErrorDialog('Invalid appointment information');
      return;
    }

    try {
      isLoadingSlots.value = true;
      hasError.value = false;
      errorMessage.value = '';

      final request = AvailableSlotsRequest(
        saloonId: saloonId,
        barberId: barberId,
        serviceIds: serviceIds,
        date: _formatDateForApi(selectedDate.value!),
      );

      log('RescheduleController: Fetching available slots with request:');
      log('  - SaloonId: $saloonId');
      log('  - BarberId: $barberId');
      log('  - ServiceIds: $serviceIds');
      log('  - Date: ${_formatDateForApi(selectedDate.value!)}');

      final response = await BookingService.getAvailableSlots(request);

      if (response.success && response.data != null) {
        availableTimeSlots.value = response.data!.availableSlots;
        log(
          'RescheduleController: Loaded ${availableTimeSlots.length} available slots',
        );
      } else {
        hasError.value = true;
        errorMessage.value = response.message.isNotEmpty
            ? response.message
            : 'Failed to load available time slots';
        availableTimeSlots.clear();
      }
    } catch (e) {
      log('RescheduleController: Error fetching slots: $e');
      hasError.value = true;
      errorMessage.value =
          'Failed to load available time slots. Please try again.';
      availableTimeSlots.clear();
    } finally {
      isLoadingSlots.value = false;
    }
  }

  /// Reschedule appointment
  Future<void> rescheduleAppointment() async {
    if (!_validateSelection()) {
      return;
    }

    try {
      isRescheduling.value = true;
      hasError.value = false;
      errorMessage.value = '';

      final request = RescheduleRequest.fromTimeSlot(
        appointmentId: appointmentId,
        date: selectedDate.value!,
        startTime: selectedTimeSlot.value!.startTime,
        endTime: selectedTimeSlot.value!.endTime,
      );

      log(
        'RescheduleController: Rescheduling appointment with request: $request',
      );

      final response = await _rescheduleService.rescheduleAppointment(request);

      if (response.isSuccess) {
        log('RescheduleController: Successfully rescheduled appointment');
        _showSuccessDialog(response);
      } else {
        log(
          'RescheduleController: Failed to reschedule appointment: ${response.message}',
        );
        _showErrorDialog(response.userFriendlyMessage);
      }
    } catch (e) {
      log('RescheduleController: Error rescheduling appointment: $e');
      _showErrorDialog('An unexpected error occurred. Please try again.');
    } finally {
      isRescheduling.value = false;
    }
  }

  /// Validate date and time selection
  bool _validateSelection() {
    if (selectedDate.value == null) {
      _showErrorDialog('Please select a date');
      return false;
    }

    if (selectedTimeSlot.value == null) {
      _showErrorDialog('Please select a time slot');
      return false;
    }

    return true;
  }

  /// Format date for API (YYYY-MM-DD)
  String _formatDateForApi(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Show success dialog
  void _showSuccessDialog(RescheduleResponse response) {
    Get.dialog(
      AlertDialog(
        title: const Icon(Icons.check_circle, color: Colors.green, size: 48),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Appointment Rescheduled Successfully!',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (response.data != null) ...[
              Text(
                'New Date: ${response.data!.displayDate}',
                style: const TextStyle(fontSize: 14),
              ),
              Text(
                'New Time: ${response.data!.displayTime12Hour}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              Get.back(); // Go back to appointments screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    Get.dialog(
      AlertDialog(
        title: const Icon(Icons.error, color: Colors.red, size: 48),
        content: Text(
          message,
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('OK')),
        ],
      ),
    );
  }

  /// Check if reschedule button should be enabled
  bool get canProceedWithReschedule {
    return selectedDate.value != null &&
        selectedTimeSlot.value != null &&
        !isRescheduling.value &&
        canReschedule.value;
  }

  /// Get reschedule rules for display
  Map<String, dynamic> get rescheduleRules =>
      _rescheduleService.getRescheduleRules();

  /// Getter methods for accessing appointment data
  String get currentAppointmentId => appointmentId;
  String get currentSaloonId => saloonId;
  String get currentBarberId => barberId;
  List<String> get currentServiceIds => List.unmodifiable(serviceIds);

  /// Debug method to print all current parameters
  void debugPrintParameters() {
    log('=== RescheduleController Debug Info ===');
    log('AppointmentId: $appointmentId');
    log('SaloonId: $saloonId');
    log('BarberId: $barberId');
    log('ServiceIds: $serviceIds (${serviceIds.length} services)');
    log('Selected Date: ${selectedDate.value}');
    log('Selected Time Slot: ${selectedTimeSlot.value?.displayTime12Hour}');
    log('Can Reschedule: ${canReschedule.value}');
    log('Is Loading Slots: ${isLoadingSlots.value}');
    log('Has Error: ${hasError.value}');
    if (hasError.value) {
      log('Error Message: ${errorMessage.value}');
    }
    log('Available Slots Count: ${availableTimeSlots.length}');
    log('=====================================');
  }

  /// Validate all parameters are properly set
  bool get hasValidParameters {
    return appointmentId.isNotEmpty &&
        saloonId.isNotEmpty &&
        barberId.isNotEmpty &&
        serviceIds.isNotEmpty;
  }
}
