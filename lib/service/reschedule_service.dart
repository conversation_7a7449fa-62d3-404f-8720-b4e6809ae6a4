import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../app/constants/api_endpoints.dart';
import '../model/reschedule_request.dart';
import '../model/reschedule_response.dart';
import '../app/services/shared_preferences_service.dart';

/// Service class for reschedule appointment API operations
class RescheduleService {
  static final RescheduleService _instance = RescheduleService._internal();
  factory RescheduleService() => _instance;
  RescheduleService._internal();

  /// Get authorization headers with token
  Future<Map<String, String>> _getHeaders() async {
    final token = await SharedPreferencesService.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Reschedule appointment
  Future<RescheduleResponse> rescheduleAppointment(
    RescheduleRequest request,
  ) async {
    try {
      log('RescheduleService: Starting reschedule request for appointment: ${request.appointmentId}');
      log('RescheduleService: Request data: ${request.toString()}');

      // Validate request
      if (!request.isValid) {
        log('RescheduleService: Invalid request data');
        return RescheduleResponse(
          code: 400,
          success: false,
          message: 'Invalid reschedule request data',
        );
      }

      final headers = await _getHeaders();
      log('RescheduleService: Making API call to: ${ApiEndpoints.rescheduleAppointment}');

      final response = await http.post(
        Uri.parse(ApiEndpoints.rescheduleAppointment),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      log('RescheduleService: API response status: ${response.statusCode}');
      log('RescheduleService: API response body: ${response.body}');

      // Parse response
      final jsonData = json.decode(response.body) as Map<String, dynamic>;
      final rescheduleResponse = RescheduleResponse.fromJson(jsonData);

      if (response.statusCode == 200) {
        log('RescheduleService: Successfully rescheduled appointment');
        return rescheduleResponse;
      } else {
        log('RescheduleService: Failed to reschedule appointment - Status: ${response.statusCode}');
        return RescheduleResponse(
          code: response.statusCode,
          success: false,
          message: rescheduleResponse.message.isNotEmpty 
              ? rescheduleResponse.message 
              : 'Failed to reschedule appointment',
        );
      }
    } on FormatException catch (e) {
      log('RescheduleService: JSON parsing error: $e');
      return RescheduleResponse(
        code: 500,
        success: false,
        message: 'Invalid response format from server',
      );
    } on http.ClientException catch (e) {
      log('RescheduleService: Network error: $e');
      return RescheduleResponse(
        code: 500,
        success: false,
        message: 'Network connection error. Please check your internet connection.',
      );
    } catch (e) {
      log('RescheduleService: Unexpected error: $e');
      return RescheduleResponse(
        code: 500,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Check if appointment can be rescheduled
  Future<bool> canRescheduleAppointment(String appointmentId) async {
    try {
      log('RescheduleService: Checking if appointment can be rescheduled: $appointmentId');

      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('${ApiEndpoints.getAppointmentDetails}/$appointmentId'),
        headers: headers,
      );

      log('RescheduleService: Check reschedule eligibility response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body) as Map<String, dynamic>;
        
        // Check if appointment status allows rescheduling
        final status = jsonData['data']?['status'] as String?;
        final canReschedule = status == 'pending' || status == 'confirmed';
        
        log('RescheduleService: Appointment status: $status, Can reschedule: $canReschedule');
        return canReschedule;
      } else {
        log('RescheduleService: Failed to check reschedule eligibility');
        return false;
      }
    } catch (e) {
      log('RescheduleService: Error checking reschedule eligibility: $e');
      return false;
    }
  }

  /// Get reschedule restrictions/rules
  Map<String, dynamic> getRescheduleRules() {
    return {
      'maxReschedules': 1,
      'minHoursBeforeAppointment': 24,
      'allowedStatuses': ['pending', 'confirmed'],
      'warningMessage': 'After rescheduling, you won\'t be able to reschedule this appointment again',
    };
  }
}
