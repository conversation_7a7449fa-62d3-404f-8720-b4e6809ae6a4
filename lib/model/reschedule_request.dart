/// Reschedule appointment request model
class RescheduleRequest {
  final String appointmentId;
  final String appointmentDate;
  final String startTime;
  final String endTime;

  RescheduleRequest({
    required this.appointmentId,
    required this.appointmentDate,
    required this.startTime,
    required this.endTime,
  });

  /// Create request from appointment ID and time slot
  factory RescheduleRequest.fromTimeSlot({
    required String appointmentId,
    required DateTime date,
    required String startTime,
    required String endTime,
  }) {
    return RescheduleRequest(
      appointmentId: appointmentId,
      appointmentDate: _formatDateForApi(date),
      startTime: startTime,
      endTime: endTime,
    );
  }

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'appointmentId': appointmentId,
      'appointmentDate': appointmentDate,
      'startTime': startTime,
      'endTime': endTime,
    };
  }

  /// Format date for API (YYYY-MM-DD)
  static String _formatDateForApi(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Validate request data
  bool get isValid {
    return appointmentId.isNotEmpty &&
        appointmentDate.isNotEmpty &&
        startTime.isNotEmpty &&
        endTime.isNotEmpty;
  }

  @override
  String toString() {
    return 'RescheduleRequest(appointmentId: $appointmentId, appointmentDate: $appointmentDate, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RescheduleRequest &&
        other.appointmentId == appointmentId &&
        other.appointmentDate == appointmentDate &&
        other.startTime == startTime &&
        other.endTime == endTime;
  }

  @override
  int get hashCode {
    return appointmentId.hashCode ^
        appointmentDate.hashCode ^
        startTime.hashCode ^
        endTime.hashCode;
  }
}
