/// Reschedule appointment response model
class RescheduleResponse {
  final int code;
  final bool success;
  final String message;
  final RescheduleData? data;

  RescheduleResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  /// Create from JSON response
  factory RescheduleResponse.fromJson(Map<String, dynamic> json) {
    return RescheduleResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null 
          ? RescheduleData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }

  /// Check if the reschedule was successful
  bool get isSuccess => success && code == 200;

  /// Get user-friendly error message
  String get userFriendlyMessage {
    if (isSuccess) return message;
    
    // Handle common error cases
    switch (code) {
      case 400:
        return 'Invalid reschedule request. Please check your selection.';
      case 401:
        return 'You are not authorized to reschedule this appointment.';
      case 404:
        return 'Appointment not found or no longer available.';
      case 409:
        return 'Selected time slot is no longer available. Please choose another time.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return message.isNotEmpty ? message : 'Failed to reschedule appointment.';
    }
  }

  @override
  String toString() {
    return 'RescheduleResponse(code: $code, success: $success, message: $message, data: $data)';
  }
}

/// Reschedule appointment data model
class RescheduleData {
  final String appointmentId;
  final String appointmentDate;
  final String startTime;
  final String endTime;
  final String status;

  RescheduleData({
    required this.appointmentId,
    required this.appointmentDate,
    required this.startTime,
    required this.endTime,
    required this.status,
  });

  /// Create from JSON
  factory RescheduleData.fromJson(Map<String, dynamic> json) {
    return RescheduleData(
      appointmentId: json['appointmentId'] as String? ?? '',
      appointmentDate: json['appointmentDate'] as String? ?? '',
      startTime: json['startTime'] as String? ?? '',
      endTime: json['endTime'] as String? ?? '',
      status: json['status'] as String? ?? '',
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'appointmentId': appointmentId,
      'appointmentDate': appointmentDate,
      'startTime': startTime,
      'endTime': endTime,
      'status': status,
    };
  }

  /// Get formatted date display
  String get displayDate {
    if (appointmentDate.isEmpty) return 'Date not available';
    try {
      final date = DateTime.parse(appointmentDate);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return appointmentDate;
    }
  }

  /// Get formatted time display
  String get displayTime {
    if (startTime.isEmpty || endTime.isEmpty) return 'Time not available';
    return '$startTime - $endTime';
  }

  /// Get formatted time display in 12-hour format
  String get displayTime12Hour {
    if (startTime.isEmpty || endTime.isEmpty) return 'Time not available';
    return '${_formatTimeTo12Hour(startTime)} - ${_formatTimeTo12Hour(endTime)}';
  }

  /// Helper method to convert 24-hour time to 12-hour format
  String _formatTimeTo12Hour(String time24) {
    final parts = time24.split(':');
    if (parts.length != 2) return time24;

    final hour = int.tryParse(parts[0]);
    final minute = parts[1];

    if (hour == null) return time24;

    if (hour == 0) {
      return '12:$minute AM';
    } else if (hour < 12) {
      return '$hour:$minute AM';
    } else if (hour == 12) {
      return '12:$minute PM';
    } else {
      return '${hour - 12}:$minute PM';
    }
  }

  @override
  String toString() {
    return 'RescheduleData(appointmentId: $appointmentId, appointmentDate: $appointmentDate, startTime: $startTime, endTime: $endTime, status: $status)';
  }
}
