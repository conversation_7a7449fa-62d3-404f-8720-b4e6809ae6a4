import 'dart:developer';
import 'package:get/get.dart';
import '../models/auth_models.dart';
import '../services/auth_service.dart';
import '../services/shared_preferences_service.dart';
import '../routes/app_routes.dart';

class AuthController extends GetxController {
  // Observable variables
  final isLoading = false.obs;
  final isAuthenticated = false.obs;
  final userRole = ''.obs;
  final userEmail = ''.obs;
  final userPhone = ''.obs;
  final authToken = ''.obs;
  final isVerified = false.obs;
  final isProfileCompleted = false.obs;

  // Messages
  final errorMessage = ''.obs;
  final successMessage = ''.obs;

  // Don't check authentication status immediately in onInit()
  // Let the splash screen handle the initial authentication flow

  // Clear messages
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }

  // Check authentication status on app start
  Future<void> checkAuthenticationStatus() async {
    try {
      log('AuthController: Checking authentication status...');

      final token = await SharedPreferencesService.getToken();
      final role = await SharedPreferencesService.getRole();
      final email = await SharedPreferencesService.getEmail();
      final phone = await SharedPreferencesService.getPhone();
      final verified = await SharedPreferencesService.getVerificationStatus();
      final profileCompleted =
          await SharedPreferencesService.getProfileCompletionStatus();

      if (token != null &&
          token.isNotEmpty &&
          role != null &&
          role.isNotEmpty) {
        // User has valid token and role
        isAuthenticated.value = true;
        userRole.value = role;
        authToken.value = token;
        userEmail.value = email ?? '';
        userPhone.value = phone ?? '';
        isVerified.value = verified;
        isProfileCompleted.value = profileCompleted;

        log(
          'AuthController: User is authenticated - Role: $role, Verified: $verified, ProfileCompleted: $profileCompleted',
        );
      } else {
        // User is not authenticated
        isAuthenticated.value = false;
        log('AuthController: User is not authenticated');
      }
    } catch (e) {
      log('AuthController: Error checking auth status: $e');
      isAuthenticated.value = false;
    }
  }

  // Navigate based on authentication status (called by splash screen)
  void navigateBasedOnAuthStatus() {
    if (!isAuthenticated.value) {
      log('AuthController: User not authenticated, navigating to social login');
      Get.offAllNamed(AppRoutes.socialLogin);
      return;
    }

    _navigateBasedOnStatus();
  }

  // Navigate based on user status
  void _navigateBasedOnStatus() {
    log('AuthController: _navigateBasedOnStatus called');
    log('AuthController: isVerified: ${isVerified.value}');
    log('AuthController: isProfileCompleted: ${isProfileCompleted.value}');
    log('AuthController: userRole: ${userRole.value}');

    if (!isVerified.value) {
      // User is not verified, navigate to OTP verification
      log('AuthController: User not verified, navigating to OTP verification');
      Get.offAllNamed(
        AppRoutes.otpVerification,
        arguments: {
          'identifier': userEmail.value.isNotEmpty
              ? userEmail.value
              : userPhone.value,
          'fromLogin': true,
        },
      );
      // Auto-trigger resend OTP
      _autoResendOtp();
    } else if (!isProfileCompleted.value) {
      // User is verified but profile not completed
      log('AuthController: Profile not completed, navigating to profile form');
      if (userRole.value == 'owner') {
        log('AuthController: Navigating to owner detail form');
        Get.offAllNamed(AppRoutes.ownerDetailForm);
      } else {
        log('AuthController: Navigating to user detail form');
        Get.offAllNamed(AppRoutes.userDetailForm);
      }
    } else {
      // User is fully authenticated and profile completed
      log('AuthController: User fully authenticated, navigating to dashboard');
      if (userRole.value == 'owner') {
        log('AuthController: Navigating to owner bottom navigation');
        Get.offAllNamed(AppRoutes.ownerBottomNavigation);
      } else {
        log('AuthController: Navigating to user bottom navigation');
        Get.offAllNamed(AppRoutes.userBottomNavigation);
      }
    }
  }

  // Auto resend OTP when user is not verified
  Future<void> _autoResendOtp() async {
    try {
      final identifier = userEmail.value.isNotEmpty
          ? userEmail.value
          : userPhone.value;
      if (identifier.isNotEmpty) {
        log('AuthController: Auto-resending OTP for: $identifier');
        final request = ResendOtpRequest(identifier: identifier);
        await AuthService.resendOtp(request);
        log('AuthController: OTP resent successfully');
      }
    } catch (e) {
      log('AuthController: Error auto-resending OTP: $e');
    }
  }

  // Email Registration
  Future<void> registerWithEmail({
    required String email,
    required String password,
    required String role,
  }) async {
    try {
      clearMessages();
      isLoading.value = true;

      log(
        'AuthController: Starting email registration for $email with role $role',
      );

      final request =
          await AuthService.createEmailRegisterRequestWithDeviceInfo(
            email: email,
            password: password,
            role: role,
          );

      final response = await AuthService.registerWithEmail(request);

      if (response.success) {
        successMessage.value = response.message;
        userEmail.value = email;
        userRole.value = role;

        // Save email and role for OTP verification
        await SharedPreferencesService.saveEmail(email);
        await SharedPreferencesService.saveRole(role);

        log(
          'AuthController: Registration successful, navigating to OTP verification',
        );

        // Navigate to OTP verification
        Get.toNamed(
          AppRoutes.otpVerification,
          arguments: {'identifier': email, 'fromRegistration': true},
        );
      } else {
        errorMessage.value = response.message;
      }
    } catch (e) {
      log('AuthController: Registration error: $e');
      if (e is ApiError) {
        errorMessage.value = e.message;
      } else {
        errorMessage.value = 'Registration failed. Please try again.';
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Email Login
  Future<void> loginWithEmail({
    required String email,
    required String password,
    required String role,
  }) async {
    try {
      clearMessages();
      isLoading.value = true;

      log('AuthController: Starting email login for $email with role $role');

      final request = await AuthService.createLoginRequestWithDeviceInfo(
        email: email,
        password: password,
        role: role,
      );

      final response = await AuthService.loginWithEmail(request);

      if (response.success && response.token != null) {
        // Debug logging for response parsing
        log(
          'AuthController: Raw response - Token: ${response.token != null ? "Present" : "Null"}',
        );
        log('AuthController: Raw response - Role: ${response.role}');
        log('AuthController: Raw response - Data: ${response.data}');
        log(
          'AuthController: Raw response - isVerified: ${response.data?.isVerified}',
        );
        log(
          'AuthController: Raw response - isProfileCompleted: ${response.data?.isProfileCompleted}',
        );

        // Update observable variables
        isAuthenticated.value = true;
        userRole.value = response.role ?? role;
        userEmail.value = email;
        authToken.value = response.token!;
        isVerified.value = response.data?.isVerified ?? false;
        isProfileCompleted.value = response.data?.isProfileCompleted ?? false;

        // Save authentication data to SharedPreferences
        await SharedPreferencesService.saveToken(response.token!);
        await SharedPreferencesService.saveRole(response.role ?? role);
        await SharedPreferencesService.saveEmail(email);
        await SharedPreferencesService.saveVerificationStatus(isVerified.value);
        await SharedPreferencesService.saveProfileCompletionStatus(
          isProfileCompleted.value,
        );

        log(
          'AuthController: Login successful - Verified: ${isVerified.value}, ProfileCompleted: ${isProfileCompleted.value}, Role: ${userRole.value}',
        );

        // Clear any previous error messages
        clearMessages();

        // Navigate based on status
        _navigateBasedOnStatus();
      } else {
        errorMessage.value = response.message;
      }
    } catch (e) {
      log('AuthController: Login error: $e');
      if (e is ApiError) {
        // Check for specific email verification error
        if (e.message.toLowerCase().contains(
          'please verify your email before logging in',
        )) {
          log(
            'AuthController: Email verification required, navigating to OTP verification',
          );

          // Save email and role for OTP verification
          userEmail.value = email;
          userRole.value = role;
          await SharedPreferencesService.saveEmail(email);
          await SharedPreferencesService.saveRole(role);

          // Navigate to OTP verification with login context
          Get.toNamed(
            AppRoutes.otpVerification,
            arguments: {
              'identifier': email,
              'fromLogin': true,
              'needsVerification': true,
            },
          );

          // Clear the error message since we're handling it with navigation
          errorMessage.value = '';
          return;
        }
        errorMessage.value = e.message;
      } else {
        errorMessage.value = 'Login failed. Please try again.';
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Verify OTP
  Future<void> verifyOtp({
    required String identifier,
    required String otp,
  }) async {
    try {
      clearMessages();
      isLoading.value = true;

      log('AuthController: Verifying OTP for $identifier');

      final request = VerifyOtpRequest(identifier: identifier, otp: otp);
      final response = await AuthService.verifyOtp(request);

      if (response.success && response.token != null) {
        successMessage.value = response.message;

        // Debug logging for response data
        log('AuthController: Response data: ${response.data}');
        log('AuthController: Response role: ${response.role}');
        log(
          'AuthController: Response token: ${response.token != null ? "Present" : "Null"}',
        );

        // Update authentication status
        isAuthenticated.value = true;
        authToken.value = response.token!;
        isVerified.value = response.data?.isVerified ?? true;
        isProfileCompleted.value = response.data?.isProfileCompleted ?? false;

        if (response.role != null) {
          userRole.value = response.role!;
        }

        // Save authentication data to SharedPreferences
        await SharedPreferencesService.saveToken(response.token!);
        await SharedPreferencesService.saveRole(userRole.value);
        await SharedPreferencesService.saveVerificationStatus(isVerified.value);
        await SharedPreferencesService.saveProfileCompletionStatus(
          isProfileCompleted.value,
        );

        log(
          'AuthController: OTP verification successful - Role: ${userRole.value}, Verified: ${isVerified.value}, ProfileCompleted: ${isProfileCompleted.value}',
        );

        // Navigate based on profile completion status
        if (!isProfileCompleted.value) {
          log(
            'AuthController: Profile not completed, navigating to profile form',
          );
          if (userRole.value == 'owner') {
            Get.offAllNamed(AppRoutes.ownerDetailForm);
          } else {
            Get.offAllNamed(AppRoutes.userDetailForm);
          }
        } else {
          log('AuthController: Profile completed, navigating to dashboard');
          if (userRole.value == 'owner') {
            Get.offAllNamed(AppRoutes.ownerBottomNavigation);
          } else {
            Get.offAllNamed(AppRoutes.userBottomNavigation);
          }
        }
      } else {
        errorMessage.value = response.message;
      }
    } catch (e) {
      log('AuthController: OTP verification error: $e');
      if (e is ApiError) {
        errorMessage.value = e.message;
      } else {
        errorMessage.value = 'OTP verification failed. Please try again.';
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Resend OTP
  Future<void> resendOtp(String identifier) async {
    try {
      clearMessages();
      isLoading.value = true;

      log('AuthController: Resending OTP for $identifier');

      final request = ResendOtpRequest(identifier: identifier);
      final response = await AuthService.resendOtp(request);

      if (response.success) {
        successMessage.value = response.message;
      } else {
        errorMessage.value = response.message;
      }
    } catch (e) {
      log('AuthController: Resend OTP error: $e');
      if (e is ApiError) {
        errorMessage.value = e.message;
      } else {
        errorMessage.value = 'Failed to resend OTP. Please try again.';
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      isLoading.value = true;

      log('AuthController: Starting logout process...');

      // Clear SharedPreferences
      await SharedPreferencesService.clearUserData();

      // Reset observable variables
      isAuthenticated.value = false;
      userRole.value = '';
      userEmail.value = '';
      userPhone.value = '';
      authToken.value = '';
      isVerified.value = false;
      isProfileCompleted.value = false;
      clearMessages();

      log('AuthController: Logout completed, navigating to social login');

      // Navigate to social login
      Get.offAllNamed(AppRoutes.socialLogin);
    } catch (e) {
      log('AuthController: Logout error: $e');
      errorMessage.value = 'Logout failed. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  // Profile completion callback
  void onProfileCompleted() {
    isProfileCompleted.value = true;
    log('AuthController: Profile completed, navigating to dashboard');

    if (userRole.value == 'owner') {
      Get.offAllNamed(AppRoutes.ownerBottomNavigation);
    } else {
      Get.offAllNamed(AppRoutes.userBottomNavigation);
    }
  }
}
