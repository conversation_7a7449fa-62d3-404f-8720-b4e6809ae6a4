import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../models/booking_models.dart';
import '../models/salon_detail_models.dart';
import '../models/appointment_booking_models.dart';
import '../services/booking_service.dart';
import '../services/appointment_booking_service.dart';
import '../services/shared_preferences_service.dart';
import '../services/razorpay_payment_service.dart';
import '../constants/app_constants.dart';
import '../config/razorpay_config.dart';
import '../views/booking/booking_payment_screen.dart';

/// Controller for managing booking flow and state
class BookingController extends GetxController
    with GetTickerProviderStateMixin {
  // Navigation parameters
  late final String saloonId;
  String? barberId;

  // Booking flow state
  final RxInt currentStep =
      0.obs; // 0: Service & Slot, 1: Summary & Payment, 2: Confirmation
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  // Service selection
  final RxList<SalonService> availableServices = <SalonService>[].obs;
  final RxList<String> selectedServiceIds = <String>[].obs;
  final RxList<SalonService> selectedServices = <SalonService>[].obs;

  // Date and time selection
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  final Rx<TimeSlot?> selectedTimeSlot = Rx<TimeSlot?>(null);
  final Rx<AvailableSlotsData?> availableSlotsData = Rx<AvailableSlotsData?>(
    null,
  );
  final RxList<TimeSlot> availableTimeSlots = <TimeSlot>[].obs;

  // Loading states
  final RxBool isLoadingSlots = false.obs;
  final RxBool isDatePickerOpen = false.obs;
  final RxBool isBookingAppointment = false.obs;
  final RxBool isProcessingPayment = false.obs;

  // Payment and booking
  final Rx<PaymentMode> selectedPaymentMode = PaymentMode.fullPayment.obs;
  final Rx<AppointmentBookingData?> bookingData = Rx<AppointmentBookingData?>(
    null,
  );
  final Rx<Map<String, dynamic>?> paymentConfirmationData =
      Rx<Map<String, dynamic>?>(null);
  final Rx<BookingSummary?> bookingSummary = Rx<BookingSummary?>(null);

  // Razorpay service
  final RazorpayPaymentService _razorpayService =
      RazorpayPaymentService.instance;

  // Barber data
  final RxString selectedBarberName = ''.obs;

  // Animation controllers
  late AnimationController stepAnimationController;
  late AnimationController slideAnimationController;
  late Animation<double> stepAnimation;
  late Animation<Offset> slideAnimation;

  BookingController({this.barberId});

  @override
  void onInit() {
    super.onInit();

    // Get navigation parameters
    final arguments = Get.arguments as Map<String, dynamic>? ?? {};
    saloonId = arguments['saloonId'] as String? ?? '';
    barberId = arguments['barberId'] as String?;

    // Set barber name from arguments
    final barberName = arguments['barberName'] as String?;
    if (barberName != null && barberName.isNotEmpty) {
      selectedBarberName.value = barberName;
    }

    log(
      'BookingController: Initializing with saloonId: $saloonId, barberId: $barberId',
    );
    log('BookingController: Barber name: ${selectedBarberName.value}');

    if (saloonId.isEmpty) {
      _handleError('Salon ID is required for booking');
      return;
    }

    // Initialize animations
    _initializeAnimations();

    // Load initial data
    _loadInitialData();
  }

  void _initializeAnimations() {
    stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    stepAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: stepAnimationController, curve: Curves.easeInOut),
    );

    slideAnimation =
        Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero).animate(
          CurvedAnimation(
            parent: slideAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );

    // Start initial animation
    stepAnimationController.forward();
    slideAnimationController.forward();
  }

  void _loadInitialData() {
    // Get services from previous page arguments
    final arguments = Get.arguments as Map<String, dynamic>? ?? {};
    final services = arguments['services'] as List<SalonService>? ?? [];

    if (services.isNotEmpty) {
      availableServices.value = services;
      log('BookingController: Loaded ${services.length} services');
    } else {
      log('BookingController: No services provided in arguments');
    }
  }

  // Service selection methods
  void toggleServiceSelection(String serviceId) {
    if (selectedServiceIds.contains(serviceId)) {
      selectedServiceIds.remove(serviceId);
      selectedServices.removeWhere((service) => service.id == serviceId);
    } else {
      selectedServiceIds.add(serviceId);
      final service = availableServices.firstWhereOrNull(
        (s) => s.id == serviceId,
      );
      if (service != null) {
        selectedServices.add(service);
      }
    }

    log('BookingController: Selected services: ${selectedServiceIds.length}');

    // Clear time slot selection when services change
    if (selectedTimeSlot.value != null) {
      selectedTimeSlot.value = null;
      availableTimeSlots.clear();
      availableSlotsData.value = null;
    }
  }

  bool isServiceSelected(String serviceId) {
    return selectedServiceIds.contains(serviceId);
  }

  // Date selection methods
  void selectDate(DateTime date) {
    selectedDate.value = date;
    selectedTimeSlot.value = null; // Clear selected time slot
    log('BookingController: Selected date: ${date.toIso8601String()}');

    // Automatically fetch available slots when date is selected
    if (selectedServiceIds.isNotEmpty) {
      fetchAvailableSlots();
    }
  }

  void openDatePicker() {
    isDatePickerOpen.value = true;
  }

  void closeDatePicker() {
    isDatePickerOpen.value = false;
  }

  // Time slot methods
  void selectTimeSlot(TimeSlot timeSlot) {
    selectedTimeSlot.value = timeSlot;
    log('BookingController: Selected time slot: ${timeSlot.displayTime12Hour}');
  }

  bool isTimeSlotSelected(TimeSlot timeSlot) {
    return selectedTimeSlot.value == timeSlot;
  }

  // Available slots fetching
  Future<void> fetchAvailableSlots() async {
    if (selectedDate.value == null || selectedServiceIds.isEmpty) {
      log('BookingController: Cannot fetch slots - missing date or services');
      return;
    }

    try {
      isLoadingSlots.value = true;
      hasError.value = false;
      errorMessage.value = '';

      final request = AvailableSlotsRequest(
        saloonId: saloonId,
        barberId: barberId,
        serviceIds: selectedServiceIds.toList(),
        date: _formatDateForApi(selectedDate.value ?? DateTime.now()),
      );

      log('BookingController: Fetching available slots with request: $request');

      final response = await BookingService.getAvailableSlots(request);

      if (response.success && response.data != null) {
        availableSlotsData.value = response.data;
        availableTimeSlots.value = response.data!.availableSlots;

        log(
          'BookingController: Loaded ${response.data!.availableSlots.length} available slots',
        );

        // Show success message if slots are available
        if (response.data!.hasAvailableSlots) {
          Get.snackbar(
            'Success',
            'Found ${response.data!.availableSlots.length} available time slots',
            duration: const Duration(seconds: 2),
          );
        } else {
          Get.snackbar(
            'No Slots Available',
            'No available time slots for the selected date. Please try another date.',
            duration: const Duration(seconds: 3),
          );
        }
      } else {
        _handleError(response.userFriendlyMessage);
      }
    } on BookingServiceException catch (e) {
      log('BookingController: Service exception: $e');
      _handleError(e.userFriendlyMessage);
    } catch (e) {
      log('BookingController: Unexpected error: $e');
      _handleError('Failed to load available time slots. Please try again.');
    } finally {
      isLoadingSlots.value = false;
    }
  }

  String _formatDateForApi(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Step navigation
  void nextStep() {
    if (currentStep.value < 2) {
      if (_validateCurrentStep()) {
        currentStep.value++;
        _animateStepTransition();
        log('BookingController: Moved to step ${currentStep.value + 1}');
      }
    }
  }

  void previousStep() {
    if (currentStep.value > 0 && currentStep.value < 2) {
      // Don't allow going back from confirmation step
      currentStep.value--;
      _animateStepTransition();
      log('BookingController: Moved to step ${currentStep.value + 1}');
    }
  }

  void goToStep(int step) {
    if (step >= 0 && step <= 2) {
      currentStep.value = step;
      _animateStepTransition();
      log('BookingController: Jumped to step ${step + 1}');
    }
  }

  void _animateStepTransition() {
    slideAnimationController.reset();
    slideAnimationController.forward();
  }

  bool _validateCurrentStep() {
    switch (currentStep.value) {
      case 0: // Service & Slot selection
        if (selectedServiceIds.isEmpty) {
          Get.snackbar('Error', 'Please select at least one service');
          return false;
        }
        if (selectedDate.value == null) {
          Get.snackbar('Error', 'Please select a date');
          return false;
        }
        if (selectedTimeSlot.value == null) {
          Get.snackbar('Error', 'Please select a time slot');
          return false;
        }
        return true;
      case 1: // Summary & Payment
        return true; // Summary step doesn't need validation
      case 2: // Confirmation
        return true; // Confirmation step doesn't need validation
      default:
        return false;
    }
  }

  // Error handling
  void _handleError(String message) {
    hasError.value = true;
    errorMessage.value = message;

    Get.snackbar(
      'Error',
      message,
      duration: const Duration(seconds: 4),
      backgroundColor: Colors.red.withValues(alpha: 0.1),
      colorText: Colors.red,
    );
  }

  void clearError() {
    hasError.value = false;
    errorMessage.value = '';
  }

  // Getters for UI
  bool get canProceedToNextStep {
    switch (currentStep.value) {
      case 0:
        return selectedServiceIds.isNotEmpty &&
            selectedDate.value != null &&
            selectedTimeSlot.value != null;
      case 1:
        return true; // Summary & Payment step - allow proceeding to payment
      case 2:
        return true; // Confirmation step - allow proceeding to done
      default:
        return false;
    }
  }

  bool get canGoToPreviousStep {
    return currentStep.value > 0 &&
        currentStep.value < 2; // Can't go back from confirmation
  }

  String get currentStepTitle {
    switch (currentStep.value) {
      case 0:
        return 'Select Services & Time';
      case 1:
        return 'Booking Summary';
      case 2:
        return 'Confirmation';
      default:
        return 'Booking';
    }
  }

  double get progressPercentage {
    return (currentStep.value + 1) / 3;
  }

  // Calculated values
  int get totalDuration {
    return selectedServices.fold(0, (sum, service) => sum + service.length);
  }

  double get totalPrice {
    return selectedServices.fold(0.0, (sum, service) => sum + service.price);
  }

  String get formattedTotalPrice {
    return '₹${totalPrice.toStringAsFixed(0)}';
  }

  String get formattedTotalDuration {
    final hours = totalDuration ~/ 60;
    final minutes = totalDuration % 60;

    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}m';
    }
  }

  // Initialize Razorpay
  Future<void> _initializeRazorpay() async {
    try {
      log('BookingController: Initializing Razorpay service...');

      // Set up callbacks
      _razorpayService.onPaymentSuccess = (paymentDetails) {
        _handlePaymentSuccess(
          PaymentSuccessResponse(
            paymentDetails.paymentId,
            paymentDetails.orderId,
            paymentDetails.signature,
            null,
          ),
        );
      };

      _razorpayService.onPaymentError = (errorMessage) {
        _handlePaymentError(
          PaymentFailureResponse(Razorpay.UNKNOWN_ERROR, errorMessage, null),
        );
      };

      _razorpayService.onExternalWallet = (walletName) {
        _handleExternalWallet(ExternalWalletResponse(walletName));
      };

      final success = await _razorpayService.initialize();
      if (success) {
        log('BookingController: Razorpay service initialized successfully');
      } else {
        log('BookingController: Failed to initialize Razorpay service');
      }
    } catch (e) {
      log('BookingController: Error initializing Razorpay service: $e');
    }
  }

  // Change payment mode
  void changePaymentMode(PaymentMode mode) {
    selectedPaymentMode.value = mode;
    _updateBookingSummary();
    log('BookingController: Payment mode changed to: ${mode.displayName}');
  }

  // Update booking summary
  void _updateBookingSummary() {
    if (selectedServices.isEmpty ||
        selectedDate.value == null ||
        selectedTimeSlot.value == null) {
      return;
    }

    final services = selectedServices
        .map(
          (service) => ServiceSummary(
            id: service.id,
            name: service.name,
            price: service.price,
            duration: service.length,
          ),
        )
        .toList();

    final payableAmount = AppointmentBookingService.calculatePaymentAmount(
      totalPrice,
      selectedPaymentMode.value,
    );

    bookingSummary.value = BookingSummary(
      barberName: selectedBarberName.value.isNotEmpty
          ? selectedBarberName.value
          : 'Selected Barber',
      services: services,
      appointmentDate: _formatDate(selectedDate.value!),
      appointmentTime: selectedTimeSlot.value!.displayTime12Hour,
      totalAmount: totalPrice,
      paymentMode: selectedPaymentMode.value,
      payableAmount: payableAmount,
    );

    log(
      'BookingController: Booking summary updated - Total: ₹${totalPrice}, Payable: ₹${payableAmount}',
    );
  }

  // Proceed to payment
  Future<void> proceedToPayment() async {
    log('BookingController: proceedToPayment called');

    if (!_validateBookingData()) {
      log('BookingController: Booking data validation failed');
      return;
    }

    try {
      log('BookingController: Setting loading state to true');
      isBookingAppointment.value = true;
      errorMessage.value = '';

      // Create appointment booking request
      final appointmentDate = _formatDateForApi(selectedDate.value!);
      log('BookingController: Formatted appointment date: $appointmentDate');
      log('BookingController: Selected services: ${selectedServiceIds.length}');
      log('BookingController: Barber ID: $barberId');
      log('BookingController: Salon ID: $saloonId');
      log(
        'BookingController: Payment type: ${selectedPaymentMode.value.value}',
      );

      final request = AppointmentBookingRequest(
        appointmentDate: appointmentDate,
        startTime: selectedTimeSlot.value!.startTime,
        endTime: selectedTimeSlot.value!.endTime,
        serviceIds: selectedServiceIds,
        barberId: barberId ?? '',
        saloonId: saloonId,
        paymentType: selectedPaymentMode.value.value,
      );

      log(
        'BookingController: Creating appointment booking with request: ${request.toString()}',
      );

      final response = await AppointmentBookingService.createAppointment(
        request,
      );

      log(
        'BookingController: API response received - Success: ${response.success}',
      );
      log('BookingController: API response code: ${response.code}');
      log('BookingController: API response message: ${response.message}');

      if (response.success && response.data != null) {
        bookingData.value = response.data;

        log('BookingController: Appointment created successfully');
        log(
          'BookingController: Appointment ID: ${response.data!.appointmentId}',
        );
        log(
          'BookingController: Razorpay Order ID: ${response.data!.razorpayOrderId}',
        );
        log('BookingController: Amount: ${response.data!.amount}');

        if (selectedPaymentMode.value == PaymentMode.payAtSalon) {
          log(
            'BookingController: Pay at salon mode - confirming without payment',
          );
          await _confirmAppointmentWithoutPayment();
        } else {
          log('BookingController: Opening Razorpay payment gateway');
          await _openRazorpayPayment(response.data!);
        }
      } else {
        log('BookingController: API call failed - ${response.message}');
        _handleError(response.message ?? 'Failed to create appointment');
      }
    } catch (e, stackTrace) {
      log('BookingController: Exception in proceedToPayment: $e');
      log('BookingController: Stack trace: $stackTrace');
      _handleError('Failed to proceed with booking. Please try again.');
    } finally {
      log('BookingController: Setting loading state to false');
      isBookingAppointment.value = false;
    }
  }

  // Open Razorpay payment gateway
  Future<void> _openRazorpayPayment(AppointmentBookingData data) async {
    if (!data.requiresPayment) {
      log('BookingController: No payment required');
      await _confirmAppointmentWithoutPayment();
      return;
    }

    try {
      isProcessingPayment.value = true;

      // Initialize Razorpay if not already done
      await _initializeRazorpay();

      // Validate required data
      if (data.razorpayOrderId.isEmpty) {
        throw Exception('Razorpay Order ID is missing');
      }

      if (data.amount <= 0) {
        throw Exception('Invalid payment amount');
      }

      // Get user details for prefill (with null safety)
      final userEmail = await SharedPreferencesService.getEmail() ?? '';
      final userPhone = await SharedPreferencesService.getPhone() ?? '';
      final userName = await SharedPreferencesService.getUserName() ?? '';

      log('BookingController: Opening Razorpay with amount: ₹${data.amount}');
      log('BookingController: Order ID: ${data.razorpayOrderId}');
      log('BookingController: Appointment ID: ${data.appointmentId}');

      // Use the Razorpay service to open payment
      await _razorpayService.openPaymentGateway(
        bookingData: data,
        userEmail: userEmail.isNotEmpty ? userEmail : null,
        userPhone: userPhone.isNotEmpty ? userPhone : null,
        userName: userName.isNotEmpty ? userName : null,
      );
    } catch (e) {
      log('BookingController: Error opening Razorpay: $e');
      _handleError('Failed to open payment gateway: ${e.toString()}');
      isProcessingPayment.value = false;
    }
  }

  // Razorpay payment success handler
  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    log('BookingController: Payment successful - ${response.paymentId}');
    log('BookingController: Order ID - ${response.orderId}');
    log('BookingController: Signature - ${response.signature}');

    // Validate payment response
    if (!RazorpayConfig.validatePaymentResponse({
      'razorpay_payment_id': response.paymentId,
      'razorpay_order_id': response.orderId,
      'razorpay_signature': response.signature,
    })) {
      log('BookingController: Invalid payment response');
      _handleError('Invalid payment response. Please try again.');
      isProcessingPayment.value = false;
      return;
    }

    final paymentDetails = RazorpayPaymentDetails(
      paymentId: response.paymentId ?? '',
      orderId: response.orderId ?? '',
      signature: response.signature ?? '',
    );

    _confirmPaymentWithBackend(paymentDetails);
  }

  // Razorpay payment error handler
  void _handlePaymentError(PaymentFailureResponse response) {
    log('BookingController: Payment failed - ${response.message}');
    isProcessingPayment.value = false;
    _handleError('Payment failed: ${response.message ?? 'Unknown error'}');
  }

  // Razorpay external wallet handler
  void _handleExternalWallet(ExternalWalletResponse response) {
    log('BookingController: External wallet selected - ${response.walletName}');
    isProcessingPayment.value = false;
    _handleError('External wallet payments are not supported');
  }

  // Confirm payment with backend
  Future<void> _confirmPaymentWithBackend(
    RazorpayPaymentDetails paymentDetails,
  ) async {
    if (bookingData.value == null) {
      _handleError('Booking data not found');
      return;
    }

    try {
      isProcessingPayment.value = true;

      final request = PaymentConfirmationRequest(
        appointmentId: bookingData.value!.appointmentId,
        razorpayOrderId: paymentDetails.orderId,
        razorpayPaymentId: paymentDetails.paymentId,
        razorpaySignature: paymentDetails.signature,
      );

      log('BookingController: Confirming payment with backend');
      log(
        'BookingController: Payment confirmation request: ${request.toString()}',
      );

      final response = await AppointmentBookingService.confirmPayment(request);

      if (response.success) {
        log('BookingController: Payment confirmed successfully');

        // Store payment confirmation data
        paymentConfirmationData.value = {
          'otp': response.data?['otp'] as String?,
          'paymentId': paymentDetails.paymentId,
          'orderId': paymentDetails.orderId,
          'signature': paymentDetails.signature,
          'status': 'completed',
        };

        // Extract OTP if available
        final otp = response.data?['otp'] as String?;
        if (otp != null && otp.isNotEmpty) {
          log('BookingController: Appointment OTP: $otp');
        }

        // Move to confirmation step
        currentStep.value = 2;
        _animateStepTransition();
      } else {
        _handleError(response.message ?? 'Payment confirmation failed');
      }
    } catch (e) {
      log('BookingController: Error confirming payment: $e');
      _handleError('Failed to confirm payment. Please contact support.');
    } finally {
      isProcessingPayment.value = false;
    }
  }

  // Confirm appointment without payment (for pay at salon)
  Future<void> _confirmAppointmentWithoutPayment() async {
    try {
      log('BookingController: Confirming appointment without payment');
      _showSuccessAndNavigate();
    } catch (e) {
      log('BookingController: Error confirming appointment: $e');
      _handleError('Failed to confirm appointment. Please try again.');
    }
  }

  // Show success and navigate
  void _showSuccessAndNavigate({String? otp}) {
    String successMessage = 'Appointment booked successfully!';
    if (otp != null && otp.isNotEmpty) {
      successMessage += '\nYour appointment OTP: $otp';
    }

    Get.snackbar(
      'Success',
      successMessage,
      backgroundColor: AppConstants.primaryBlack,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 5),
      maxWidth: Get.width * 0.9,
    );

    // Navigate to success screen or back to home
    Future.delayed(const Duration(seconds: 3), () {
      Get.offAllNamed('/user-home'); // Adjust route as needed
    });
  }

  // Validate booking data
  bool _validateBookingData() {
    log('BookingController: Validating booking data...');
    log(
      'BookingController: Selected services count: ${selectedServices.length}',
    );
    log('BookingController: Selected date: ${selectedDate.value}');
    log('BookingController: Selected time slot: ${selectedTimeSlot.value}');
    log('BookingController: Barber ID: $barberId');
    log('BookingController: Salon ID: $saloonId');

    if (selectedServices.isEmpty) {
      log('BookingController: Validation failed - No services selected');
      _handleError('Please select at least one service');
      return false;
    }

    if (selectedDate.value == null) {
      log('BookingController: Validation failed - No date selected');
      _handleError('Please select appointment date');
      return false;
    }

    if (selectedTimeSlot.value == null) {
      log('BookingController: Validation failed - No time slot selected');
      _handleError('Please select appointment time');
      return false;
    }

    if (barberId == null || barberId!.isEmpty) {
      log('BookingController: Validation failed - No barber ID');
      _handleError('Barber information is missing');
      return false;
    }

    if (saloonId.isEmpty) {
      log('BookingController: Validation failed - No salon ID');
      _handleError('Salon information is missing');
      return false;
    }

    log('BookingController: Validation passed successfully');
    return true;
  }

  // Format date for display
  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  // Public method to format date
  String formatDate(DateTime date) {
    return _formatDate(date);
  }

  // Open Razorpay payment gateway
  Future<void> openRazorpayPayment(AppointmentBookingData data) async {
    await _openRazorpayPayment(data);
  }

  // Handle proceed to payment from summary step
  Future<void> handleProceedToPayment() async {
    log('BookingController: handleProceedToPayment called');
    await proceedToPayment();
  }

  // Navigate to payment summary screen
  void navigateToPaymentSummary() {
    if (bookingData.value != null) {
      Get.to(
        () => const BookingPaymentScreen(),
        transition: Transition.rightToLeft,
        duration: const Duration(milliseconds: 300),
      );
    } else {
      _handleError('Booking data not available');
    }
  }

  // Test Razorpay initialization
  Future<void> testRazorpayInitialization() async {
    try {
      log('BookingController: Testing Razorpay initialization...');
      await _initializeRazorpay();

      Get.snackbar(
        'Success',
        'Razorpay service initialized successfully',
        backgroundColor: AppConstants.primaryBlack,
        colorText: AppConstants.primaryWhite,
      );
    } catch (e) {
      log('BookingController: Razorpay test error: $e');
      Get.snackbar(
        'Error',
        'Razorpay initialization error: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  void onClose() {
    stepAnimationController.dispose();
    slideAnimationController.dispose();
    _razorpayService.dispose();
    log('BookingController: Disposing...');
    super.onClose();
  }
}
