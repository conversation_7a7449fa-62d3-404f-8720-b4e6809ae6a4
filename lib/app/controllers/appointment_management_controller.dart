import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/appointment_management_models.dart';
import '../services/appointment_management_service.dart';

/// Controller for appointment management on owner side
class AppointmentManagementController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final AppointmentManagementService _service = AppointmentManagementService();

  // Tab controller
  late TabController tabController;

  // Appointment lists
  final RxList<OwnerAppointment> pendingAppointments = <OwnerAppointment>[].obs;
  final RxList<OwnerAppointment> approvedAppointments =
      <OwnerAppointment>[].obs;
  final RxList<OwnerAppointment> ongoingAppointments = <OwnerAppointment>[].obs;
  final RxList<OwnerAppointment> completedAppointments =
      <OwnerAppointment>[].obs;

  // Loading states
  final RxBool isLoadingMain = false.obs;
  final RxBool isLoadingCompleted = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxMap<String, bool> appointmentLoadingStates = <String, bool>{}.obs;

  // Error states
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  // Last refresh time
  final Rx<DateTime?> lastRefreshTime = Rx<DateTime?>(null);

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);

    // Load initial data
    loadMainAppointments();
    loadCompletedAppointments();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// Load main appointments (pending and confirmed)
  Future<void> loadMainAppointments() async {
    try {
      isLoadingMain.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('AppointmentManagementController: Loading main appointments');

      final response = await _service.getMainAppointments();

      if (response.isSuccess && response.data != null) {
        // Update pending appointments
        pendingAppointments.value = response.data!.pending;

        // Update approved appointments (confirmed status)
        approvedAppointments.value = response.data!.confirmed;

        // Update ongoing appointments (in_progress status)
        ongoingAppointments.value = response.data!.inProgress;

        log(
          'AppointmentManagementController: Loaded ${pendingAppointments.length} pending appointments',
        );
        log(
          'AppointmentManagementController: Loaded ${approvedAppointments.length} approved appointments',
        );
        log(
          'AppointmentManagementController: Loaded ${ongoingAppointments.length} ongoing appointments',
        );
      } else {
        hasError.value = true;
        errorMessage.value = response.message;
        log(
          'AppointmentManagementController: Failed to load main appointments: ${response.message}',
        );
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to load appointments. Please try again.';
      log(
        'AppointmentManagementController: Error loading main appointments: $e',
      );
    } finally {
      isLoadingMain.value = false;
    }
  }

  /// Load completed appointments
  Future<void> loadCompletedAppointments() async {
    try {
      isLoadingCompleted.value = true;

      log('AppointmentManagementController: Loading completed appointments');

      final response = await _service.getCompletedAppointments();

      if (response.isSuccess) {
        completedAppointments.value = response.data;
        log(
          'AppointmentManagementController: Loaded ${completedAppointments.length} completed appointments',
        );
      } else {
        log(
          'AppointmentManagementController: Failed to load completed appointments: ${response.message}',
        );
      }
    } catch (e) {
      log(
        'AppointmentManagementController: Error loading completed appointments: $e',
      );
    } finally {
      isLoadingCompleted.value = false;
    }
  }

  /// Accept appointment
  Future<void> acceptAppointment(String appointmentId) async {
    await _makeAppointmentDecision(appointmentId, 'Accepted');
  }

  /// Reject appointment
  Future<void> rejectAppointment(String appointmentId) async {
    await _makeAppointmentDecision(appointmentId, 'Rejected');
  }

  /// Make appointment decision (accept/reject)
  Future<void> _makeAppointmentDecision(
    String appointmentId,
    String decision,
  ) async {
    try {
      _setAppointmentLoading(appointmentId, true);

      log(
        'AppointmentManagementController: Making decision $decision for appointment $appointmentId',
      );

      final request = AppointmentDecisionRequest(
        appointmentId: appointmentId,
        decision: decision,
      );

      final response = await _service.makeAppointmentDecision(request);

      if (response.isSuccess) {
        log(
          'AppointmentManagementController: Successfully made decision: $decision',
        );

        // Show success message
        _showSuccessDialog(
          'Appointment ${decision.toLowerCase()} successfully!',
        );

        // Refresh data
        await loadMainAppointments();
      } else {
        log(
          'AppointmentManagementController: Failed to make decision: ${response.message}',
        );
        _showErrorDialog(response.userFriendlyMessage);
      }
    } catch (e) {
      log('AppointmentManagementController: Error making decision: $e');
      _showErrorDialog(
        'Failed to process appointment decision. Please try again.',
      );
    } finally {
      _setAppointmentLoading(appointmentId, false);
    }
  }

  /// Start service with OTP
  Future<void> startService(String appointmentId, String otp) async {
    try {
      _setAppointmentLoading(appointmentId, true);

      log('AppointmentManagementController: ===== START SERVICE CALLED =====');
      log('AppointmentManagementController: Appointment ID: $appointmentId');
      log('AppointmentManagementController: OTP: $otp');
      log('AppointmentManagementController: OTP Length: ${otp.length}');

      final request = StartServiceRequest(
        appointmentId: appointmentId,
        otp: otp,
      );

      log(
        'AppointmentManagementController: Request created: ${request.toString()}',
      );

      final response = await _service.startService(request);

      log('AppointmentManagementController: ===== SERVICE RESPONSE =====');
      log(
        'AppointmentManagementController: Response success: ${response.isSuccess}',
      );
      log('AppointmentManagementController: Response code: ${response.code}');
      log(
        'AppointmentManagementController: Response message: ${response.message}',
      );

      if (response.isSuccess) {
        log('AppointmentManagementController: Successfully started service');

        // Show success message
        _showSuccessDialog('Service started successfully!');

        // Refresh data
        await loadMainAppointments();
      } else {
        log('AppointmentManagementController: ===== SERVICE FAILED =====');
        log(
          'AppointmentManagementController: Failed to start service: ${response.message}',
        );
        log(
          'AppointmentManagementController: User friendly message: ${response.userFriendlyMessage}',
        );

        // Show detailed error message for debugging
        _showErrorDialog(
          'Failed to start service.\n\n'
          'Error Code: ${response.code}\n'
          'Message: ${response.message}\n\n'
          'Please check the OTP and try again.',
        );
      }
    } catch (e) {
      log('AppointmentManagementController: Error starting service: $e');
      _showErrorDialog('Failed to start service. Please try again.');
    } finally {
      _setAppointmentLoading(appointmentId, false);
    }
  }

  /// Complete service
  Future<void> completeService(String appointmentId) async {
    try {
      _setAppointmentLoading(appointmentId, true);

      log(
        'AppointmentManagementController: Completing service for appointment $appointmentId',
      );

      final request = CompleteServiceRequest(appointmentId: appointmentId);

      final response = await _service.completeService(request);

      if (response.isSuccess) {
        log('AppointmentManagementController: Successfully completed service');

        // Show success message
        _showSuccessDialog('Service completed successfully!');

        // Refresh data
        await loadMainAppointments();
        await loadCompletedAppointments();
      } else {
        log(
          'AppointmentManagementController: Failed to complete service: ${response.message}',
        );
        _showErrorDialog(response.userFriendlyMessage);
      }
    } catch (e) {
      log('AppointmentManagementController: Error completing service: $e');
      _showErrorDialog('Failed to complete service. Please try again.');
    } finally {
      _setAppointmentLoading(appointmentId, false);
    }
  }

  /// Show OTP input dialog for starting service
  void showOtpDialog(String appointmentId) {
    final otpController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Enter OTP'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Please enter the OTP provided by the customer to start the service.',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: otpController,
              keyboardType: TextInputType.number,
              maxLength: 4,
              decoration: const InputDecoration(
                labelText: 'OTP',
                hintText: 'Enter 4-digit OTP',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              final otp = otpController.text.trim();
              log(
                'AppointmentManagementController: OTP Dialog - Entered OTP: "$otp"',
              );
              log(
                'AppointmentManagementController: OTP Dialog - OTP Length: ${otp.length}',
              );

              if (otp.isEmpty) {
                _showErrorDialog('Please enter an OTP');
                return;
              }

              // Allow both 4 and 6 digit OTPs for now to debug
              if (otp.length != 4 && otp.length != 6) {
                _showErrorDialog('Please enter a valid OTP (4 or 6 digits)');
                return;
              }

              Get.back();
              startService(appointmentId, otp);
            },
            child: const Text('Start Service'),
          ),
        ],
      ),
    );
  }

  /// Set loading state for specific appointment
  void _setAppointmentLoading(String appointmentId, bool isLoading) {
    appointmentLoadingStates[appointmentId] = isLoading;
  }

  /// Check if appointment is loading
  bool isAppointmentLoading(String appointmentId) {
    return appointmentLoadingStates[appointmentId] ?? false;
  }

  /// Show success dialog
  void _showSuccessDialog(String message) {
    Get.dialog(
      AlertDialog(
        title: const Icon(Icons.check_circle, color: Colors.green, size: 48),
        content: Text(
          message,
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('OK')),
        ],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    Get.dialog(
      AlertDialog(
        title: const Icon(Icons.error, color: Colors.red, size: 48),
        content: Text(
          message,
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('OK')),
        ],
      ),
    );
  }

  /// Refresh all data
  Future<void> refreshAllData() async {
    try {
      isRefreshing.value = true;
      hasError.value = false;
      errorMessage.value = '';

      await Future.wait([loadMainAppointments(), loadCompletedAppointments()]);

      lastRefreshTime.value = DateTime.now();
      log('All appointment data refreshed successfully');
    } catch (e) {
      log('Error refreshing appointment data: $e');
      hasError.value = true;
      errorMessage.value = 'Failed to refresh appointments';
      rethrow;
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Get appointments for specific tab
  List<OwnerAppointment> getAppointmentsForTab(int tabIndex) {
    switch (tabIndex) {
      case 0:
        return pendingAppointments;
      case 1:
        return approvedAppointments;
      case 2:
        return ongoingAppointments;
      case 3:
        return completedAppointments;
      default:
        return [];
    }
  }

  /// Get tab names
  List<String> get tabNames => ['Pending', 'Approved', 'Ongoing', 'Completed'];
}
