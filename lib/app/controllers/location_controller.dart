import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

import 'package:latlong2/latlong.dart';
import '../services/shared_preferences_service.dart';
import '../controllers/user/home_controller.dart';
import '../controllers/owner/owner_home_controller.dart';

class LocationController extends GetxController {
  // Map and location state
  final Rx<LatLng?> currentLocation = Rx<LatLng?>(null);
  final Rx<LatLng?> selectedLocation = Rx<LatLng?>(null);
  final RxString selectedAddress = ''.obs;
  final RxString currentAddress = ''.obs;

  // Loading states
  final RxBool isLoadingLocation = false.obs;
  final RxBool isLoadingAddress = false.obs;
  final RxBool isSearching = false.obs;

  // Permission states
  final RxBool hasLocationPermission = false.obs;
  final RxBool isLocationServiceEnabled = false.obs;

  // Search functionality
  final TextEditingController searchController = TextEditingController();
  final RxList<Placemark> searchResults = <Placemark>[].obs;
  final RxBool showSearchResults = false.obs;
  Timer? _searchDebounceTimer;

  // Error handling
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  @override
  void onInit() {
    super.onInit();
    log('LocationController: Initializing...');
    _initializeLocation();
  }

  @override
  void onClose() {
    searchController.dispose();
    _searchDebounceTimer?.cancel();
    super.onClose();
  }

  /// Initialize location services and permissions
  Future<void> _initializeLocation() async {
    try {
      await _checkLocationPermissions();
      await _checkLocationServices();

      if (hasLocationPermission.value && isLocationServiceEnabled.value) {
        await getCurrentLocation();
      }
    } catch (e) {
      log('LocationController: Error initializing location: $e');
      _setError('Failed to initialize location services');
    }
  }

  /// Check and request location permissions using Geolocator
  Future<void> _checkLocationPermissions() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      log('LocationController: Current permission status: $permission');

      if (permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse) {
        hasLocationPermission.value = true;
        log('LocationController: Location permission already granted');
        return;
      }

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        log(
          'LocationController: Location permission requested, result: $permission',
        );

        if (permission == LocationPermission.always ||
            permission == LocationPermission.whileInUse) {
          hasLocationPermission.value = true;
          log('LocationController: Location permission granted after request');
        } else {
          hasLocationPermission.value = false;
          log('LocationController: Location permission denied after request');
        }
      } else if (permission == LocationPermission.deniedForever) {
        hasLocationPermission.value = false;
        log('LocationController: Location permission permanently denied');
        _setError(
          'Location permission is permanently denied. Please enable it in device settings.',
        );
      } else {
        hasLocationPermission.value = false;
        log('LocationController: Location permission status: $permission');
      }
    } catch (e) {
      log('LocationController: Error checking location permissions: $e');
      hasLocationPermission.value = false;
      _setError('Failed to check location permissions: ${e.toString()}');
    }
  }

  /// Check if location services are enabled
  Future<void> _checkLocationServices() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      isLocationServiceEnabled.value = serviceEnabled;

      if (!serviceEnabled) {
        log('LocationController: Location services are disabled');
        _setError(
          'Location services are disabled. Please enable them in settings.',
        );
      }
    } catch (e) {
      log('LocationController: Error checking location services: $e');
      isLocationServiceEnabled.value = false;
    }
  }

  /// Get current GPS location
  Future<void> getCurrentLocation() async {
    try {
      isLoadingLocation.value = true;
      hasError.value = false;
      errorMessage.value = '';

      if (!hasLocationPermission.value) {
        await _checkLocationPermissions();
        if (!hasLocationPermission.value) {
          throw Exception('Location permission not granted');
        }
      }

      if (!isLocationServiceEnabled.value) {
        await _checkLocationServices();
        if (!isLocationServiceEnabled.value) {
          throw Exception('Location services not enabled');
        }
      }

      log('LocationController: Getting current location...');

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );

      final location = LatLng(position.latitude, position.longitude);
      currentLocation.value = location;
      selectedLocation.value = location;

      log(
        'LocationController: Current location: ${position.latitude}, ${position.longitude}',
      );

      // Get address for current location
      await _getAddressFromCoordinates(location);
    } catch (e) {
      log('LocationController: Error getting current location: $e');
      _setError('Failed to get current location: ${e.toString()}');
    } finally {
      isLoadingLocation.value = false;
    }
  }

  /// Get address from coordinates using reverse geocoding
  Future<void> _getAddressFromCoordinates(LatLng location) async {
    try {
      isLoadingAddress.value = true;

      final placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final address = _formatAddress(placemark);

        if (location == currentLocation.value) {
          currentAddress.value = address;
        }

        if (location == selectedLocation.value) {
          selectedAddress.value = address;
        }

        log('LocationController: Address found: $address');
      }
    } catch (e) {
      log('LocationController: Error getting address: $e');
      // Don't show error for address lookup failure
    } finally {
      isLoadingAddress.value = false;
    }
  }

  /// Format address from placemark
  String _formatAddress(Placemark placemark) {
    final parts = <String>[];

    if (placemark.name?.isNotEmpty == true) parts.add(placemark.name!);
    if (placemark.locality?.isNotEmpty == true) parts.add(placemark.locality!);
    if (placemark.administrativeArea?.isNotEmpty == true) {
      parts.add(placemark.administrativeArea!);
    }
    if (placemark.country?.isNotEmpty == true) parts.add(placemark.country!);

    return parts.join(', ');
  }

  /// Debounced search to avoid too many API calls
  void debouncedSearch(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      searchLocations(query);
    });
  }

  /// Search for locations with improved context and fallback strategies
  Future<void> searchLocations(String query) async {
    if (query.trim().isEmpty) {
      searchResults.clear();
      showSearchResults.value = false;
      return;
    }

    try {
      isSearching.value = true;
      hasError.value = false;

      log('LocationController: Searching for: $query');

      final placemarks = <Placemark>[];

      // Strategy 1: Try direct search
      await _tryDirectSearch(query, placemarks);

      // Strategy 2: If no results, try with India context
      if (placemarks.isEmpty) {
        await _trySearchWithContext(query, placemarks);
      }

      // Strategy 3: If still no results, try with major city context
      if (placemarks.isEmpty) {
        await _trySearchWithMajorCities(query, placemarks);
      }

      searchResults.value = placemarks;
      showSearchResults.value = placemarks.isNotEmpty;

      if (placemarks.isNotEmpty) {
        log('LocationController: Found ${placemarks.length} search results');
        hasError.value = false;
      } else {
        log('LocationController: No search results found for: $query');
        _setError(
          'No locations found for "$query". Try being more specific (e.g., "Vashi, Navi Mumbai")',
        );
      }
    } catch (e) {
      log('LocationController: Search error: $e');
      searchResults.clear();
      showSearchResults.value = false;
      _setError(
        'Search failed. Please check your internet connection and try again.',
      );
    } finally {
      isSearching.value = false;
    }
  }

  /// Try direct search with the query as-is
  Future<void> _tryDirectSearch(
    String query,
    List<Placemark> placemarks,
  ) async {
    try {
      final locations = await locationFromAddress(query);
      await _processLocations(locations, placemarks);
    } catch (e) {
      log('LocationController: Direct search failed: $e');
    }
  }

  /// Try search with India context
  Future<void> _trySearchWithContext(
    String query,
    List<Placemark> placemarks,
  ) async {
    final contextQueries = [
      '$query, India',
      '$query, Maharashtra, India',
      '$query, Mumbai, India',
      '$query, Delhi, India',
      '$query, Bangalore, India',
    ];

    for (final contextQuery in contextQueries) {
      try {
        log('LocationController: Trying search with context: $contextQuery');
        final locations = await locationFromAddress(contextQuery);
        await _processLocations(locations, placemarks);
        if (placemarks.isNotEmpty) break;
      } catch (e) {
        log('LocationController: Context search failed for $contextQuery: $e');
      }
    }
  }

  /// Try search with major city context for common area names
  Future<void> _trySearchWithMajorCities(
    String query,
    List<Placemark> placemarks,
  ) async {
    final majorCities = [
      'Mumbai',
      'Delhi',
      'Bangalore',
      'Chennai',
      'Kolkata',
      'Hyderabad',
      'Pune',
      'Ahmedabad',
      'Navi Mumbai',
      'Thane',
    ];

    for (final city in majorCities) {
      final cityQuery = '$query, $city, India';
      try {
        log('LocationController: Trying search with major city: $cityQuery');
        final locations = await locationFromAddress(cityQuery);
        await _processLocations(locations, placemarks);
        if (placemarks.isNotEmpty) break;
      } catch (e) {
        log('LocationController: Major city search failed for $cityQuery: $e');
      }
    }
  }

  /// Process locations and convert to placemarks
  Future<void> _processLocations(
    List<Location> locations,
    List<Placemark> placemarks,
  ) async {
    for (final location in locations.take(5)) {
      try {
        final placemark = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );
        if (placemark.isNotEmpty) {
          // Avoid duplicates by checking if we already have this location
          final newPlacemark = placemark.first;
          final isDuplicate = placemarks.any(
            (existing) =>
                existing.name == newPlacemark.name &&
                existing.locality == newPlacemark.locality &&
                existing.administrativeArea == newPlacemark.administrativeArea,
          );

          if (!isDuplicate) {
            placemarks.add(newPlacemark);
          }
        }
      } catch (e) {
        log('LocationController: Error getting placemark for location: $e');
      }
    }
  }

  /// Get popular search suggestions for common Indian locations
  List<String> getSearchSuggestions(String query) {
    final suggestions = <String>[];
    final lowerQuery = query.toLowerCase();

    final popularLocations = [
      'Vashi, Navi Mumbai',
      'Bandra, Mumbai',
      'Andheri, Mumbai',
      'Powai, Mumbai',
      'Thane, Maharashtra',
      'Pune, Maharashtra',
      'Connaught Place, Delhi',
      'Gurgaon, Haryana',
      'Noida, Uttar Pradesh',
      'Koramangala, Bangalore',
      'Whitefield, Bangalore',
      'Electronic City, Bangalore',
      'Anna Nagar, Chennai',
      'T. Nagar, Chennai',
      'Hitech City, Hyderabad',
      'Banjara Hills, Hyderabad',
      'Salt Lake, Kolkata',
      'Park Street, Kolkata',
    ];

    // Add exact matches first
    for (final location in popularLocations) {
      if (location.toLowerCase().contains(lowerQuery)) {
        suggestions.add(location);
      }
    }

    // Limit to 5 suggestions
    return suggestions.take(5).toList();
  }

  /// Select location from search results
  Future<void> selectSearchResult(Placemark placemark) async {
    try {
      final locations = await locationFromAddress(_formatAddress(placemark));

      if (locations.isNotEmpty) {
        final location = LatLng(
          locations.first.latitude,
          locations.first.longitude,
        );
        selectedLocation.value = location;
        selectedAddress.value = _formatAddress(placemark);

        // Clear search
        searchController.clear();
        searchResults.clear();
        showSearchResults.value = false;

        log(
          'LocationController: Selected search result: ${selectedAddress.value}',
        );
        log(
          'LocationController: Coordinates: ${location.latitude}, ${location.longitude}',
        );
      }
    } catch (e) {
      log('LocationController: Error selecting search result: $e');
      _setError('Failed to select location');
    }
  }

  /// Select location by tapping on map
  Future<void> selectLocationOnMap(LatLng location) async {
    selectedLocation.value = location;

    log(
      'LocationController: Selected location on map: ${location.latitude}, ${location.longitude}',
    );

    // Get address for selected location
    await _getAddressFromCoordinates(location);
  }

  /// Use current location as selected location
  void useCurrentLocation() {
    if (currentLocation.value != null) {
      selectedLocation.value = currentLocation.value;
      selectedAddress.value = currentAddress.value;

      log('LocationController: Using current location as selected');
    } else {
      _setError('Current location not available');
    }
  }

  /// Confirm and save selected location
  Future<void> confirmLocation() async {
    if (selectedLocation.value == null) {
      _setError('Please select a location first');
      return;
    }

    try {
      final location = selectedLocation.value!;
      final address = selectedAddress.value;

      log('LocationController: Confirming location: $address');
      log(
        'LocationController: Coordinates: ${location.latitude}, ${location.longitude}',
      );

      // Save to SharedPreferences
      await SharedPreferencesService.saveUserLocation(address);

      // Update home controllers
      _updateHomeControllers(address);

      // Navigate back
      Get.back(
        result: {
          'address': address,
          'latitude': location.latitude,
          'longitude': location.longitude,
        },
      );
    } catch (e) {
      log('LocationController: Error confirming location: $e');
      _setError('Failed to save location');
    }
  }

  /// Update home controllers with new address
  void _updateHomeControllers(String address) {
    try {
      // Update user home controller if exists
      if (Get.isRegistered<HomeController>()) {
        final homeController = Get.find<HomeController>();
        homeController.userAddress.value = address;
        log('LocationController: Updated user home controller address');
      }

      // Update owner home controller if exists
      if (Get.isRegistered<OwnerHomeController>()) {
        final ownerController = Get.find<OwnerHomeController>();
        ownerController.salonLocation.value = address;
        log('LocationController: Updated owner home controller address');
      }
    } catch (e) {
      log('LocationController: Error updating home controllers: $e');
    }
  }

  /// Set error state
  void _setError(String message) {
    hasError.value = true;
    errorMessage.value = message;
    log('LocationController: Error: $message');
  }

  /// Clear error state
  void clearError() {
    hasError.value = false;
    errorMessage.value = '';
  }

  /// Refresh location permissions and services (call when returning from settings)
  Future<void> refreshLocationStatus() async {
    log('LocationController: Refreshing location status...');
    clearError();
    await _checkLocationPermissions();
    await _checkLocationServices();

    if (hasLocationPermission.value && isLocationServiceEnabled.value) {
      await getCurrentLocation();
    }
  }

  /// Open device location settings
  Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      log('LocationController: Error opening location settings: $e');
    }
  }

  /// Open app settings for permissions
  Future<void> openAppSettings() async {
    try {
      await Geolocator.openAppSettings();
    } catch (e) {
      log('LocationController: Error opening app settings: $e');
    }
  }
}
