import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../models/owner_profile_model.dart';
import '../../services/owner_profile_service.dart';

class OwnerEditProfileController extends GetxController {
  // Form controllers
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final salonNameController = TextEditingController();
  final salonDescriptionController = TextEditingController();
  final salonPhoneController = TextEditingController();
  final salonEmailController = TextEditingController();
  final salonRegIdController = TextEditingController();
  final shopNoController = TextEditingController();
  final areaController = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final countryController = TextEditingController();
  final pincodeController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Profile data
  final Rx<OwnerProfileData?> currentProfile = Rx<OwnerProfileData?>(null);

  // Time selection
  final RxString selectedOffDay = 'Monday'.obs;
  final Rx<TimeOfDay> startTime = TimeOfDay(hour: 9, minute: 0).obs;
  final Rx<TimeOfDay> endTime = TimeOfDay(hour: 21, minute: 0).obs;

  // Off days options
  final List<String> offDaysOptions = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  // Form key for validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    loadProfileData();
  }

  @override
  void onClose() {
    // Dispose controllers
    firstNameController.dispose();
    lastNameController.dispose();
    salonNameController.dispose();
    salonDescriptionController.dispose();
    salonPhoneController.dispose();
    salonEmailController.dispose();
    salonRegIdController.dispose();
    shopNoController.dispose();
    areaController.dispose();
    cityController.dispose();
    stateController.dispose();
    countryController.dispose();
    pincodeController.dispose();
    super.onClose();
  }

  /// Load current profile data
  Future<void> loadProfileData() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      log('OwnerEditProfileController: Loading profile data');

      final response = await OwnerProfileService.getOwnerProfile();

      if (response.success && response.data != null) {
        currentProfile.value = response.data;
        _populateFormFields(response.data!);
        log('OwnerEditProfileController: Profile data loaded successfully');
      } else {
        errorMessage.value = response.message ?? 'Failed to load profile data';
        _showErrorSnackbar('Error', errorMessage.value);
      }
    } catch (e) {
      log('OwnerEditProfileController: Error loading profile: $e');
      errorMessage.value = 'Failed to load profile data';
      _showErrorSnackbar('Error', 'Failed to load profile data');
    } finally {
      isLoading.value = false;
    }
  }

  /// Populate form fields with current profile data
  void _populateFormFields(OwnerProfileData profile) {
    firstNameController.text = profile.firstName;
    lastNameController.text = profile.lastName;
    salonNameController.text = profile.saloonName;
    salonDescriptionController.text = profile.salonDescription;
    salonPhoneController.text = profile.phone;
    salonEmailController.text = profile.email;
    salonRegIdController.text = profile.salonRegId;
    shopNoController.text = profile.shopNo;

    // Set off day
    if (offDaysOptions.contains(profile.offDays)) {
      selectedOffDay.value = profile.offDays;
    }

    // Parse and set times
    if (profile.startTime.isNotEmpty) {
      final startTimeData = OwnerProfileService.parseTimeFromApi(
        profile.startTime,
      );
      startTime.value = TimeOfDay(
        hour: startTimeData['hour']!,
        minute: startTimeData['minute']!,
      );
    }

    if (profile.endTime.isNotEmpty) {
      final endTimeData = OwnerProfileService.parseTimeFromApi(profile.endTime);
      endTime.value = TimeOfDay(
        hour: endTimeData['hour']!,
        minute: endTimeData['minute']!,
      );
    }

    // Set address fields
    if (profile.address != null) {
      areaController.text = profile.address!.area;
      cityController.text = profile.address!.city;
      stateController.text = profile.address!.state;
      countryController.text = profile.address!.country;
      pincodeController.text = profile.address!.pincode ?? '';
    }
  }

  /// Select start time
  Future<void> selectStartTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: startTime.value,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppConstants.primaryBlack,
              onPrimary: AppConstants.primaryWhite,
              surface: AppConstants.primaryWhite,
              onSurface: AppConstants.primaryBlack,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Validate that start time is before end time
      final startMinutes = picked.hour * 60 + picked.minute;
      final endMinutes = endTime.value.hour * 60 + endTime.value.minute;

      if (startMinutes >= endMinutes) {
        _showErrorSnackbar(
          'Invalid Time',
          'Start time must be before end time',
        );
        return;
      }

      startTime.value = picked;
      log(
        'OwnerEditProfileController: Start time selected: ${picked.hour}:${picked.minute}',
      );
    }
  }

  /// Select end time
  Future<void> selectEndTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: endTime.value,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppConstants.primaryBlack,
              onPrimary: AppConstants.primaryWhite,
              surface: AppConstants.primaryWhite,
              onSurface: AppConstants.primaryBlack,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Validate that end time is after start time
      final startMinutes = startTime.value.hour * 60 + startTime.value.minute;
      final endMinutes = picked.hour * 60 + picked.minute;

      if (endMinutes <= startMinutes) {
        _showErrorSnackbar('Invalid Time', 'End time must be after start time');
        return;
      }

      endTime.value = picked;
      log(
        'OwnerEditProfileController: End time selected: ${picked.hour}:${picked.minute}',
      );
    }
  }

  /// Save profile changes
  Future<void> saveChanges() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isSaving.value = true;
      errorMessage.value = '';
      successMessage.value = '';

      // Create update request
      final updateRequest = OwnerProfileUpdateRequest(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
        salonName: salonNameController.text.trim(),
        salonDescription: salonDescriptionController.text.trim(),
        salonPhone: salonPhoneController.text.trim(),
        salonEmail: salonEmailController.text.trim(),
        offDays: selectedOffDay.value,
        startTime: startTime.value.hour == 0 && startTime.value.minute == 0
            ? null
            : formatTimeTo24Hour(startTime.value),
        endTime: endTime.value.hour == 0 && endTime.value.minute == 0
            ? null
            : formatTimeTo24Hour(endTime.value),
        salonRegId: salonRegIdController.text.trim(),
        shopNo: shopNoController.text.trim(),
        area: areaController.text.trim(),
        city: cityController.text.trim(),
        state: stateController.text.trim(),
        country: countryController.text.trim(),
        pinCode: pincodeController.text.trim(),
      );

      // Validate data
      if (!OwnerProfileService.validateProfileData(updateRequest)) {
        _showErrorSnackbar('Validation Error', 'Please check your input data');
        return;
      }

      log('OwnerEditProfileController: Saving profile changes');

      final response = await OwnerProfileService.updateOwnerProfile(
        updateRequest,
      );

      if (response.success) {
        successMessage.value = 'Profile updated successfully';
        _showSuccessSnackbar('Success', 'Profile updated successfully');

        // Update current profile data
        if (response.data != null) {
          currentProfile.value = response.data;
        }

        // Navigate back after a short delay
        Future.delayed(const Duration(milliseconds: 1500), () {
          Get.back(result: true); // Return true to indicate successful update
        });
      } else {
        errorMessage.value = response.message ?? 'Failed to update profile';
        _showErrorSnackbar('Update Failed', errorMessage.value);
      }
    } catch (e) {
      log('OwnerEditProfileController: Error saving changes: $e');
      errorMessage.value = 'Failed to save changes';
      _showErrorSnackbar('Error', 'Failed to save changes');
    } finally {
      isSaving.value = false;
    }
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppConstants.primaryBlack,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.red,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }

  /// Format time for display (12-hour format)
  String formatTimeForDisplay(TimeOfDay time) {
    final hour = time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '${hour == 0 ? 12 : hour}:$minute $period';
  }

  /// Convert TimeOfDay to 24-hour format string for API
  String formatTimeTo24Hour(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Validate required fields
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validate phone number
  String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }

    final digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');
    if (digitsOnly.length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    return null;
  }

  /// Validate email
  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }

    return null;
  }
}
