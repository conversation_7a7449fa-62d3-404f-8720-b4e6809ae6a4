import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/barber_models.dart';
import '../../models/owner_service_models.dart';
import '../../services/barber_service.dart';
import '../../services/owner_service_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../constants/app_constants.dart';

class AddBarberController extends GetxController {
  // Form controllers
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final phoneController = TextEditingController();
  final emailController = TextEditingController();
  final bioController = TextEditingController();

  // Focus nodes
  final firstNameFocus = FocusNode();
  final lastNameFocus = FocusNode();
  final phoneFocus = FocusNode();
  final emailFocus = FocusNode();
  final bioFocus = FocusNode();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isAddingBarber = false.obs;
  final RxBool isDeletingBarber = false.obs;
  final RxBool isUpdatingBarber = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Barber management
  final RxList<BarberData> barbers = <BarberData>[].obs;
  final RxList<BarberModel> allBarbers = <BarberModel>[].obs;
  final RxBool isEditing = false.obs;
  final RxString editingBarberId = ''.obs;

  // Image handling
  final Rx<File?> selectedImage = Rx<File?>(null);
  final RxString selectedImageUrl = ''.obs;
  final RxBool isUploadingImage = false.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final FirebaseStorageService _firebaseStorage = FirebaseStorageService();

  // Availability toggle
  final RxBool isAvailable = true.obs;

  // Services selection
  final RxList<GetAllServiceModel> availableServices =
      <GetAllServiceModel>[].obs;
  final RxList<String> selectedServiceIds = <String>[].obs;
  final RxBool isLoadingServices = false.obs;

  // Predefined specialties
  final List<String> predefinedSpecialties = [
    'Haircut',
    'Hair Styling',
    'Hair Coloring',
    'Beard Trim',
    'Shave',
    'Hair Treatment',
    'Facial',
    'Massage',
  ];
  final RxList<String> selectedSpecialties = <String>[].obs;

  // Form validation
  RxBool get isFormValid =>
      (firstNameController.text.trim().isNotEmpty &&
              lastNameController.text.trim().isNotEmpty &&
              phoneController.text.trim().isNotEmpty &&
              emailController.text.trim().isNotEmpty &&
              GetUtils.isEmail(emailController.text.trim()) &&
              selectedServiceIds.isNotEmpty)
          .obs;

  @override
  void onInit() {
    super.onInit();
    // Only use the enhanced method since it's working
    loadAllBarbers(); // Enhanced method with more data
    loadServices();
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    phoneController.dispose();
    emailController.dispose();
    bioController.dispose();
    firstNameFocus.dispose();
    lastNameFocus.dispose();
    phoneFocus.dispose();
    emailFocus.dispose();
    bioFocus.dispose();
    super.onClose();
  }

  /// Load barbers from API (legacy method)
  Future<void> loadBarbers() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      log('AddBarberController: Loading barbers (legacy)');

      final barbersList = await BarberService.getBarbers();
      barbers.value = barbersList;
      log('AddBarberController: Loaded ${barbers.length} barbers');
    } catch (e) {
      log('AddBarberController: Load barbers error: $e');
      if (e is BarberServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to load barbers. Please try again.';
        _showErrorSnackbar(
          'Error',
          'Failed to load barbers. Please try again.',
        );
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// Load all barbers using enhanced API
  Future<void> loadAllBarbers() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      log('AddBarberController: Loading all barbers (enhanced)');

      final barbersList = await BarberService.getAllBarbers();
      allBarbers.value = barbersList;
      log(
        'AddBarberController: Loaded ${allBarbers.length} barbers with enhanced data',
      );
    } catch (e) {
      log('AddBarberController: Load all barbers error: $e');
      if (e is BarberServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to load barbers. Please try again.';
        _showErrorSnackbar(
          'Error',
          'Failed to load barbers. Please try again.',
        );
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// Load services for selection
  Future<void> loadServices() async {
    try {
      isLoadingServices.value = true;

      log('AddBarberController: Loading services');

      final servicesList = await OwnerServiceService.getBarberService();
      availableServices.value = servicesList;
      log('AddBarberController: Loaded ${availableServices.length} services');
    } catch (e) {
      log('AddBarberController: Load services error: $e');
      _showErrorSnackbar('Error', 'Failed to load services');
    } finally {
      isLoadingServices.value = false;
    }
  }

  /// Add new barber
  Future<void> addBarber() async {
    if (!isFormValid.value) {
      _showErrorSnackbar(
        'Validation Error',
        'Please fill all required fields correctly.',
      );
      return;
    }

    try {
      isAddingBarber.value = true;
      errorMessage.value = '';

      // Validate required fields
      if (firstNameController.text.trim().isEmpty) {
        errorMessage.value = 'First name is required';
        _showErrorSnackbar('Validation Error', 'First name is required');
        return;
      }

      if (lastNameController.text.trim().isEmpty) {
        errorMessage.value = 'Last name is required';
        _showErrorSnackbar('Validation Error', 'Last name is required');
        return;
      }

      if (phoneController.text.trim().isEmpty) {
        errorMessage.value = 'Phone number is required';
        _showErrorSnackbar('Validation Error', 'Phone number is required');
        return;
      }

      if (phoneController.text.trim().length < 10) {
        errorMessage.value = 'Phone number must be at least 10 digits';
        _showErrorSnackbar(
          'Validation Error',
          'Phone number must be at least 10 digits',
        );
        return;
      }

      if (emailController.text.trim().isEmpty) {
        errorMessage.value = 'Email is required';
        _showErrorSnackbar('Validation Error', 'Email is required');
        return;
      }

      if (selectedServiceIds.isEmpty) {
        errorMessage.value = 'At least one service must be selected';
        _showErrorSnackbar(
          'Validation Error',
          'Please select at least one service for the barber',
        );
        return;
      }

      final request = BarberRequest(
        firstname: firstNameController.text.trim(),
        lastname: lastNameController.text.trim(),
        phone: phoneController.text.trim(),
        email: emailController.text.trim(),
        bio: bioController.text.trim().isNotEmpty
            ? bioController.text.trim()
            : 'Experienced barber specializing in ${selectedSpecialties.join(", ")}.',
        available: isAvailable.value,
        services: selectedServiceIds.toList(),
        profileimage:
            selectedImageUrl.value.isNotEmpty &&
                !selectedImageUrl.value.startsWith(
                  '/',
                ) // Check if it's a Firebase URL, not local path
            ? selectedImageUrl.value
            : null,
      );

      log(
        'AddBarberController: Adding barber: ${request.firstname} ${request.lastname}',
      );
      log(
        'AddBarberController: Selected services: ${selectedServiceIds.toList()}',
      );

      final response = await BarberService.addBarber(
        request,
        selectedImage.value,
      );

      if (response.success) {
        successMessage.value = response.message;
        _showSuccessSnackbar('Success', 'Barber added successfully!');
        _clearForm();
        await loadAllBarbers(); // Refresh the list
        Get.back(); // Close the modal
      } else {
        errorMessage.value = response.message;
        _showErrorSnackbar('Failed to add barber', response.message);
      }
    } catch (e) {
      log('AddBarberController: Add barber error: $e');
      if (e is BarberServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to add barber. Please try again.';
        _showErrorSnackbar('Error', 'Failed to add barber. Please try again.');
      }
    } finally {
      isAddingBarber.value = false;
    }
  }

  /// Edit barber
  Future<void> editBarber(BarberData barber) async {
    // Clear form first to ensure clean state
    _clearForm();

    // Set editing state
    isEditing.value = true;
    editingBarberId.value = barber.id;

    // Populate form with barber data
    firstNameController.text = barber.firstname;
    lastNameController.text = barber.lastname;
    phoneController.text = barber.phone ?? '';
    emailController.text = barber.email ?? '';
    bioController.text = barber.bio;
    isAvailable.value = barber.available;
    selectedServiceIds.value = barber.serviceIds;

    if (barber.hasImage) {
      selectedImageUrl.value = barber.profileImage!;
    }

    // Set specialties based on bio or services
    _extractSpecialtiesFromBio(barber.bio);
  }

  /// Update barber
  Future<void> updateBarber() async {
    if (!isFormValid.value) {
      _showErrorSnackbar(
        'Validation Error',
        'Please fill all required fields correctly.',
      );
      return;
    }

    try {
      isUpdatingBarber.value = true;
      errorMessage.value = '';

      final request = BarberRequest(
        firstname: firstNameController.text.trim(),
        lastname: lastNameController.text.trim(),
        phone: phoneController.text.trim(),
        email: emailController.text.trim(),
        bio: bioController.text.trim().isNotEmpty
            ? bioController.text.trim()
            : 'Experienced barber specializing in ${selectedSpecialties.join(", ")}.',
        available: isAvailable.value,
        services: selectedServiceIds.toList(),
      );

      log('AddBarberController: Updating barber: ${editingBarberId.value}');

      // Convert BarberRequest to BarberModel for the new API
      final barberModel = BarberModel(
        id: editingBarberId.value,
        firstname: request.firstname,
        lastname: request.lastname,
        bio: request.bio,
        available: request.available,
        profileimage: selectedImageUrl.value.isNotEmpty
            ? selectedImageUrl.value
            : null,
        assignedServices: request.services,
        saloonid: null, // Will be set by the server
        isDelete: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await BarberService.updateBarber(
        editingBarberId.value,
        barberModel,
      );

      if (success) {
        successMessage.value = 'Barber updated successfully!';
        _showSuccessSnackbar('Success', 'Barber updated successfully!');
        _clearForm();
        await loadAllBarbers(); // Refresh the list
        Get.back(); // Close the modal
      } else {
        errorMessage.value = 'Failed to update barber';
        _showErrorSnackbar('Error', 'Failed to update barber');
      }
    } catch (e) {
      log('AddBarberController: Update barber error: $e');
      if (e is BarberServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to update barber. Please try again.';
        _showErrorSnackbar(
          'Error',
          'Failed to update barber. Please try again.',
        );
      }
    } finally {
      isUpdatingBarber.value = false;
    }
  }

  /// Delete barber
  Future<void> deleteBarber(BarberData barber) async {
    try {
      isDeletingBarber.value = true;
      errorMessage.value = '';

      log('AddBarberController: Deleting barber: ${barber.id}');

      final response = await BarberService.deleteBarber(barber.id);

      if (response.success) {
        _showSuccessSnackbar(
          'Success',
          'Barber "${barber.fullName}" deleted successfully!',
        );
        await loadAllBarbers(); // Refresh the list
      } else {
        _showErrorSnackbar(
          'Error',
          response.message.isNotEmpty
              ? response.message
              : 'Failed to delete barber. Please try again.',
        );
      }
    } catch (e) {
      log('AddBarberController: Delete barber error: $e');
      if (e is BarberServiceException) {
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        _showErrorSnackbar(
          'Error',
          'Failed to delete barber. Please try again.',
        );
      }
    } finally {
      isDeletingBarber.value = false;
    }
  }

  /// Toggle barber availability
  Future<void> toggleBarberAvailability(BarberData barber) async {
    try {
      final newAvailability = !barber.available;

      log(
        'AddBarberController: Toggling barber availability - ${barber.fullName}: $newAvailability',
      );

      final success = await BarberService.updateBarberAvailability(
        barber.id,
        newAvailability,
      );

      if (success) {
        await loadAllBarbers(); // Refresh the list
        _showSuccessSnackbar(
          'Success',
          'Barber is now ${newAvailability ? "online" : "offline"}',
        );
      } else {
        _showErrorSnackbar('Error', 'Failed to update barber availability');
      }
    } catch (e) {
      log('AddBarberController: Toggle availability error: $e');
      _showErrorSnackbar('Error', 'Failed to update availability');
    }
  }

  /// Update barber availability for enhanced barber model
  Future<void> updateBarberAvailabilityEnhanced(
    int index,
    BarberModel barber,
    bool newAvailability,
  ) async {
    try {
      log(
        'AddBarberController: Updating enhanced barber availability - ${barber.name}: $newAvailability',
      );

      // Update the barber in the list optimistically
      if (index < allBarbers.length) {
        final updatedBarber = allBarbers[index].copyWith(
          available: newAvailability,
        );
        allBarbers[index] = updatedBarber;
      }

      // Call the API to update availability using comprehensive update
      final updatedBarber = barber.copyWith(available: newAvailability);
      final success = await BarberService.updateBarber(
        barber.id ?? '',
        updatedBarber,
      );

      if (success) {
        log(
          'AddBarberController: Enhanced barber availability updated successfully',
        );
        _showSuccessSnackbar(
          'Success',
          'Barber availability updated to ${newAvailability ? "Available" : "Unavailable"}',
        );
      } else {
        // Revert the change if API call failed
        if (index < allBarbers.length) {
          final revertedBarber = allBarbers[index].copyWith(
            available: !newAvailability,
          );
          allBarbers[index] = revertedBarber;
        }

        log(
          'AddBarberController: Failed to update enhanced barber availability',
        );
        _showErrorSnackbar('Error', 'Failed to update barber availability');
      }
    } catch (e) {
      // Revert the change if error occurred
      if (index < allBarbers.length) {
        final revertedBarber = allBarbers[index].copyWith(
          available: !newAvailability,
        );
        allBarbers[index] = revertedBarber;
      }

      log(
        'AddBarberController: Error updating enhanced barber availability: $e',
      );
      _showErrorSnackbar(
        'Error',
        'An error occurred while updating barber availability',
      );
    }
  }

  /// Update barber state in the list (for real-time UI updates)
  void updateBarberInList(String barberId, BarberModel updatedBarber) {
    final index = allBarbers.indexWhere((barber) => barber.id == barberId);
    if (index != -1) {
      allBarbers[index] = updatedBarber;
      log('AddBarberController: Updated barber in list at index $index');
    }
  }

  /// Get barber from list by ID
  BarberModel? getBarberById(String barberId) {
    try {
      return allBarbers.firstWhere((barber) => barber.id == barberId);
    } catch (e) {
      return null;
    }
  }

  /// Update barber availability with state management
  Future<void> updateBarberAvailabilityWithState(
    String barberId,
    bool newAvailability,
  ) async {
    try {
      log(
        'AddBarberController: Updating barber availability with state management',
      );

      // Find the barber in the list
      final barber = getBarberById(barberId);
      if (barber == null) {
        log('AddBarberController: Barber not found in list: $barberId');
        _showErrorSnackbar('Error', 'Barber not found');
        return;
      }

      // Update the barber in the list optimistically
      final updatedBarber = barber.copyWith(available: newAvailability);
      updateBarberInList(barberId, updatedBarber);

      // Call the API to update availability (use dedicated method for status updates)
      final success = await BarberService.updateBarberAvailability(
        barberId,
        newAvailability,
      );

      if (success) {
        log('AddBarberController: Barber availability updated successfully');
        _showSuccessSnackbar(
          'Success',
          'Barber is now ${newAvailability ? "Available" : "Unavailable"}',
        );
      } else {
        // Revert the change if API call failed
        updateBarberInList(barberId, barber);
        log('AddBarberController: Failed to update barber availability');
        _showErrorSnackbar('Error', 'Failed to update barber availability');
      }
    } catch (e) {
      // Revert the change if error occurred
      final originalBarber = getBarberById(barberId);
      if (originalBarber != null) {
        final revertedBarber = originalBarber.copyWith(
          available: !newAvailability,
        );
        updateBarberInList(barberId, revertedBarber);
      }

      log('AddBarberController: Error updating barber availability: $e');
      _showErrorSnackbar(
        'Error',
        'An error occurred while updating barber availability',
      );
    }
  }

  /// Clear form
  void _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    bioController.clear();
    selectedImage.value = null;
    selectedImageUrl.value = '';
    isAvailable.value = true;
    selectedServiceIds.clear();
    selectedSpecialties.clear();
    isEditing.value = false;
    editingBarberId.value = '';
  }

  /// Extract specialties from bio text
  void _extractSpecialtiesFromBio(String bio) {
    selectedSpecialties.clear();
    for (final specialty in predefinedSpecialties) {
      if (bio.toLowerCase().contains(specialty.toLowerCase())) {
        selectedSpecialties.add(specialty);
      }
    }
  }

  /// Toggle service selection
  void toggleServiceSelection(String serviceId) {
    if (selectedServiceIds.contains(serviceId)) {
      selectedServiceIds.remove(serviceId);
    } else {
      selectedServiceIds.add(serviceId);
    }
  }

  /// Toggle specialty selection
  void toggleSpecialtySelection(String specialty) {
    if (selectedSpecialties.contains(specialty)) {
      selectedSpecialties.remove(specialty);
    } else {
      selectedSpecialties.add(specialty);
    }
  }

  /// Select image directly (like AddServiceController) - simpler approach
  Future<void> selectImageDirect(ImageSource source) async {
    try {
      log('AddBarberController: Selecting image from ${source.name}');

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 80,
      );

      if (image != null) {
        final imageFile = File(image.path);
        selectedImage.value = imageFile;
        selectedImageUrl.value = imageFile.path;

        log('AddBarberController: Image selected from ${source.name}');

        // Upload to Firebase immediately
        await _uploadImageToFirebase(imageFile);
      }
    } catch (e) {
      log('AddBarberController: Image selection error: $e');
      Get.snackbar(
        'Error',
        'Failed to select image. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Set profile image and upload to Firebase
  void setProfileImage(File? image) {
    try {
      log(
        'AddBarberController: Setting profile image: ${image?.path ?? "null"}',
      );

      selectedImage.value = image;
      if (image != null) {
        // Validate image file exists
        if (!image.existsSync()) {
          log('AddBarberController: Image file does not exist: ${image.path}');
          Get.snackbar(
            'Error',
            'Selected image file is not accessible. Please try again.',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }

        selectedImageUrl.value = image.path;
        _uploadImageToFirebase(image);
      } else {
        selectedImageUrl.value = '';
        isUploadingImage.value = false;
        uploadProgress.value = 0.0;
      }
    } catch (e) {
      log('AddBarberController: Error setting profile image: $e');
      Get.snackbar(
        'Error',
        'Failed to process selected image. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Upload image to Firebase Storage
  Future<void> _uploadImageToFirebase(File imageFile) async {
    try {
      // Validate file before upload
      if (!imageFile.existsSync()) {
        log('AddBarberController: Image file does not exist for upload');
        Get.snackbar(
          'Error',
          'Image file is not accessible. Please select again.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // Check file size (limit to 10MB)
      final fileSize = await imageFile.length();
      if (fileSize > 10 * 1024 * 1024) {
        log('AddBarberController: Image file too large: $fileSize bytes');
        Get.snackbar(
          'Error',
          'Image file is too large. Please select a smaller image.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      isUploadingImage.value = true;
      uploadProgress.value = 0.0;

      log(
        'AddBarberController: Starting Firebase upload for barber image ($fileSize bytes)',
      );

      final downloadUrl = await _firebaseStorage.uploadBarberImage(
        imageFile,
        onProgress: (progress) {
          uploadProgress.value = progress;
          log(
            'AddBarberController: Upload progress: ${(progress * 100).toStringAsFixed(1)}%',
          );
        },
      );

      if (downloadUrl != null && downloadUrl.isNotEmpty) {
        selectedImageUrl.value = downloadUrl;
        log('AddBarberController: Image uploaded successfully: $downloadUrl');

        Get.snackbar(
          'Success',
          'Image uploaded successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } else {
        log('AddBarberController: Failed to upload image - no download URL');
        selectedImageUrl.value = imageFile.path; // Fallback to local path

        Get.snackbar(
          'Upload Failed',
          'Failed to upload image. You can still add the barber.',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      log('AddBarberController: Error uploading image: $e');
      selectedImageUrl.value = imageFile.path; // Fallback to local path

      Get.snackbar(
        'Upload Error',
        'Error uploading image. You can still add the barber.',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isUploadingImage.value = false;
      uploadProgress.value = 1.0;
    }
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.green,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.red,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }
}
