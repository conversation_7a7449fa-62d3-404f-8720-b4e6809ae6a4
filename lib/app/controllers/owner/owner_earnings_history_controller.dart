import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../models/earnings_history_models.dart';
import '../../services/earnings_history_service.dart';

class OwnerEarningsHistoryController extends GetxController
    with GetSingleTickerProviderStateMixin {
  
  // Tab controller for animated tabs
  late TabController tabController;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  // Data variables
  final Rx<EarningsHistoryData?> earningsData = Rx<EarningsHistoryData?>(null);
  final RxList<WithdrawalTransaction> filteredTransactions = <WithdrawalTransaction>[].obs;
  final Rx<TransactionStatus> selectedStatus = TransactionStatus.all.obs;

  // Download variables
  final RxBool isDownloading = false.obs;
  final RxList<DownloadOption> previousSlips = <DownloadOption>[].obs;
  final RxList<String> monthlyReports = <String>[].obs;

  // Animation variables
  final RxDouble animationValue = 0.0.obs;
  final RxBool showContent = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeTabController();
    _initializeMonthlyReports();
    loadEarningsHistory();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// Initialize tab controller
  void _initializeTabController() {
    tabController = TabController(
      length: TransactionStatus.values.length,
      vsync: this,
    );
    
    tabController.addListener(() {
      if (!tabController.indexIsChanging) {
        final newStatus = TransactionStatus.values[tabController.index];
        changeTransactionStatus(newStatus);
      }
    });
  }

  /// Initialize monthly reports list
  void _initializeMonthlyReports() {
    final now = DateTime.now();
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    monthlyReports.clear();
    for (int i = 0; i < 12; i++) {
      final month = now.month - i;
      final year = month <= 0 ? now.year - 1 : now.year;
      final adjustedMonth = month <= 0 ? month + 12 : month;
      monthlyReports.add('${months[adjustedMonth - 1]} $year');
    }
  }

  /// Load earnings history from API
  Future<void> loadEarningsHistory() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('OwnerEarningsHistoryController: Loading earnings history');

      final response = await EarningsHistoryService.getWithdrawalHistory();

      if (response.success && response.data != null) {
        earningsData.value = response.data;
        _updateFilteredTransactions();
        _generatePreviousSlips();
        _animateContent();
        
        log('OwnerEarningsHistoryController: Earnings history loaded successfully');
      } else {
        hasError.value = true;
        errorMessage.value = response.message;
        log('OwnerEarningsHistoryController: Error: ${response.message}');
      }
    } catch (e) {
      log('OwnerEarningsHistoryController: Exception: $e');
      hasError.value = true;
      errorMessage.value = 'An unexpected error occurred. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh earnings history
  Future<void> refreshEarningsHistory() async {
    try {
      isRefreshing.value = true;
      await loadEarningsHistory();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Change transaction status filter
  void changeTransactionStatus(TransactionStatus status) {
    selectedStatus.value = status;
    _updateFilteredTransactions();
    _animateTabChange();
    log('OwnerEarningsHistoryController: Changed status to ${status.displayName}');
  }

  /// Update filtered transactions based on selected status
  void _updateFilteredTransactions() {
    if (earningsData.value == null) {
      filteredTransactions.clear();
      return;
    }

    final transactions = earningsData.value!.withdrawalData
        .getTransactionsByStatus(selectedStatus.value);
    
    filteredTransactions.assignAll(transactions);
    log('OwnerEarningsHistoryController: Filtered ${transactions.length} transactions for ${selectedStatus.value.displayName}');
  }

  /// Generate previous slips for download
  void _generatePreviousSlips() {
    if (earningsData.value == null) return;

    previousSlips.clear();
    final completedTransactions = earningsData.value!.withdrawalData.completed;
    
    for (final transaction in completedTransactions) {
      previousSlips.add(
        DownloadOption(
          title: 'Transaction Slip',
          subtitle: transaction.referenceNumber,
          date: transaction.withDrawalAt,
          amount: transaction.withDrawalAmount,
        ),
      );
    }
  }

  /// Animate content appearance
  void _animateContent() {
    showContent.value = true;
    animationValue.value = 1.0;
  }

  /// Animate tab change
  void _animateTabChange() {
    animationValue.value = 0.8;
    Future.delayed(const Duration(milliseconds: 150), () {
      animationValue.value = 1.0;
    });
  }

  /// Download previous slip
  Future<void> downloadPreviousSlip(DownloadOption slip) async {
    try {
      isDownloading.value = true;
      
      log('OwnerEarningsHistoryController: Downloading slip: ${slip.subtitle}');
      
      // Extract transaction ID from reference number
      final transactionId = slip.subtitle.replaceAll('TXN', '').toLowerCase();
      
      final success = await EarningsHistoryService.downloadTransactionSlip(transactionId);
      
      if (success) {
        Get.snackbar(
          'Download Complete',
          'Transaction slip downloaded successfully',
          backgroundColor: AppConstants.primaryBlack,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.download_done, color: AppConstants.primaryWhite),
        );
      } else {
        Get.snackbar(
          'Download Failed',
          'Failed to download transaction slip',
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: AppConstants.primaryWhite),
        );
      }
    } catch (e) {
      log('OwnerEarningsHistoryController: Download error: $e');
      Get.snackbar(
        'Download Error',
        'An error occurred while downloading',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isDownloading.value = false;
    }
  }

  /// Download monthly report
  Future<void> downloadMonthlyReport(String monthYear) async {
    try {
      isDownloading.value = true;
      
      log('OwnerEarningsHistoryController: Downloading monthly report: $monthYear');
      
      // Parse month and year
      final parts = monthYear.split(' ');
      final monthName = parts[0];
      final year = int.parse(parts[1]);
      
      final months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      final month = months.indexOf(monthName) + 1;
      
      final success = await EarningsHistoryService.downloadMonthlyReport(year, month);
      
      if (success) {
        Get.snackbar(
          'Download Complete',
          'Monthly report for $monthYear downloaded successfully',
          backgroundColor: AppConstants.primaryBlack,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.download_done, color: AppConstants.primaryWhite),
        );
      } else {
        Get.snackbar(
          'Download Failed',
          'Failed to download monthly report',
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: AppConstants.primaryWhite),
        );
      }
    } catch (e) {
      log('OwnerEarningsHistoryController: Monthly report download error: $e');
      Get.snackbar(
        'Download Error',
        'An error occurred while downloading',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isDownloading.value = false;
    }
  }

  /// Format currency
  String formatCurrency(double amount) {
    return EarningsHistoryService.formatCurrency(amount);
  }

  /// Format percentage
  String formatPercentage(double percentage) {
    return EarningsHistoryService.formatPercentage(percentage);
  }

  /// Get status color
  Color getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return Colors.green;
      case TransactionStatus.pending:
        return Colors.orange;
      case TransactionStatus.rejected:
        return Colors.red;
      default:
        return AppConstants.mediumGrey;
    }
  }

  /// Get status icon
  IconData getStatusIcon(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return Icons.check_circle;
      case TransactionStatus.pending:
        return Icons.access_time;
      case TransactionStatus.rejected:
        return Icons.cancel;
      default:
        return Icons.list;
    }
  }

  /// Check if data is available
  bool get hasData => earningsData.value != null;

  /// Check if transactions are available
  bool get hasTransactions => filteredTransactions.isNotEmpty;

  /// Get total earnings
  double get totalEarnings => earningsData.value?.totalEarning ?? 0.0;

  /// Get completion rate
  double get completionRate => earningsData.value?.completionRate ?? 0.0;

  /// Get total transactions count
  int get totalTransactions => earningsData.value?.totalTransactions ?? 0;

  /// Get completed transactions count
  int get completedCount => earningsData.value?.completed ?? 0;

  /// Get pending transactions count
  int get pendingCount => earningsData.value?.pending ?? 0;

  /// Get rejected transactions count
  int get rejectedCount => earningsData.value?.rejected ?? 0;
}
