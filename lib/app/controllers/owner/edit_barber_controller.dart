import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/barber_models.dart';
import '../../services/barber_service.dart';
import '../../services/owner_service_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../models/owner_service_models.dart';

class EditBarberController extends GetxController {
  // Form controllers
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final mobileController = TextEditingController();
  final bioController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isUpdating = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;
  final RxBool isAvailable = true.obs;

  // Barber data
  final Rx<BarberModel?> currentBarber = Rx<BarberModel?>(null);
  final RxString barberId = ''.obs;

  // Services
  final RxList<OwnerServiceData> availableServices = <OwnerServiceData>[].obs;
  final RxList<String> selectedServiceIds = <String>[].obs;

  // Image handling
  final Rx<File?> selectedImage = Rx<File?>(null);
  final RxString selectedImageUrl = ''.obs;
  final RxBool isUploadingImage = false.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final FirebaseStorageService _firebaseStorage = FirebaseStorageService();

  @override
  void onInit() {
    super.onInit();
    loadServices();
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    mobileController.dispose();
    bioController.dispose();
    super.onClose();
  }

  /// Initialize with barber ID
  Future<void> initializeWithBarberId(String id) async {
    barberId.value = id;
    await loadBarberDetails();
  }

  /// Load barber details by ID
  Future<void> loadBarberDetails() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      log(
        'EditBarberController: Loading barber details for ID: ${barberId.value}',
      );

      if (barberId.value.isEmpty) {
        errorMessage.value = 'Invalid barber ID';
        _showErrorSnackbar('Error', 'Invalid barber ID provided');
        return;
      }

      // Load services first, then barber details
      await loadServices();

      final barber = await BarberService.getBarberById(barberId.value);
      if (barber != null) {
        currentBarber.value = barber;
        _populateForm(barber);
        log('EditBarberController: Barber details loaded successfully');
        log(
          'EditBarberController: Barber name: ${barber.firstname} ${barber.lastname}',
        );
        log(
          'EditBarberController: Barber image: ${barber.profileimage ?? 'No image'}',
        );
      } else {
        errorMessage.value = 'Barber not found with ID: ${barberId.value}';
        log('EditBarberController: Barber not found for ID: ${barberId.value}');
        _showErrorSnackbar('Error', 'Barber not found');
      }
    } catch (e) {
      log('EditBarberController: Error loading barber details: $e');
      errorMessage.value = 'Failed to load barber details: ${e.toString()}';
      _showErrorSnackbar('Error', 'Failed to load barber details');
    } finally {
      isLoading.value = false;
    }
  }

  /// Populate form with barber data
  void _populateForm(BarberModel barber) {
    firstNameController.text = barber.firstname;
    lastNameController.text = barber.lastname;
    mobileController.text = ''; // Mobile not available in current model
    bioController.text = barber.bio ?? '';
    isAvailable.value = barber.available;
    selectedImageUrl.value = barber.profileimage ?? '';

    // Set selected services - handle both assignedServices and services arrays
    selectedServiceIds.clear(); // Clear first

    if (barber.assignedServices != null &&
        barber.assignedServices!.isNotEmpty) {
      selectedServiceIds.value = barber.assignedServices!;
      log(
        'EditBarberController: Pre-selected services from assignedServices: ${barber.assignedServices}',
      );
    } else if (barber.services != null && barber.services!.isNotEmpty) {
      // For services from API response, we need to match them with available services
      // The API might return service names, so we need to find corresponding IDs
      final serviceIds = <String>[];
      for (final barberService in barber.services!) {
        // Try to find matching service by name in available services
        final matchingService = availableServices.firstWhere(
          (service) =>
              service.name.toLowerCase() ==
              barberService.serviceName.toLowerCase(),
          orElse: () => OwnerServiceData(
            id: '',
            name: '',
            categoryname: '',
            price: 0.0,
            length: 0,
            serviceImage: '',
            saloonid: '',
            isDelete: false,
            createdAt: '',
            updatedAt: '',
          ),
        );
        if (matchingService.id.isNotEmpty) {
          serviceIds.add(matchingService.id);
        }
      }
      selectedServiceIds.value = serviceIds;
      log(
        'EditBarberController: Pre-selected services from services (matched by name): $serviceIds',
      );
    } else {
      log('EditBarberController: No services assigned to this barber');
    }
  }

  /// Load available services
  Future<void> loadServices() async {
    try {
      log('EditBarberController: Loading services');
      final services = await OwnerServiceService.getBarberService();

      // Convert GetAllServiceModel to OwnerServiceData
      availableServices.value = services
          .map(
            (service) => OwnerServiceData(
              id: service.id,
              name: service.name,
              categoryname: service.categoryName,
              price: service.price.toDouble(),
              length: service.length,
              serviceImage: service.images,
              saloonid: service.saloonId,
              isDelete: service.isDelete,
              createdAt: service.createdAt,
              updatedAt: service.updatedAt,
            ),
          )
          .toList();

      log('EditBarberController: Loaded ${availableServices.length} services');
    } catch (e) {
      log('EditBarberController: Error loading services: $e');
    }
  }

  /// Toggle service selection
  void toggleServiceSelection(String serviceId) {
    final currentList = List<String>.from(selectedServiceIds);
    if (currentList.contains(serviceId)) {
      currentList.remove(serviceId);
    } else {
      currentList.add(serviceId);
    }
    selectedServiceIds.value = currentList;
  }

  /// Set profile image and upload to Firebase
  void setProfileImage(File? image) {
    selectedImage.value = image;
    if (image != null) {
      selectedImageUrl.value = image.path;
      _uploadImageToFirebase(image);
    } else {
      selectedImageUrl.value = '';
      isUploadingImage.value = false;
      uploadProgress.value = 0.0;
    }
  }

  /// Upload image to Firebase Storage
  Future<void> _uploadImageToFirebase(File imageFile) async {
    try {
      isUploadingImage.value = true;
      uploadProgress.value = 0.0;

      log('EditBarberController: Starting Firebase upload for barber image');

      final downloadUrl = await _firebaseStorage.uploadBarberImage(
        imageFile,
        onProgress: (progress) {
          uploadProgress.value = progress;
          log(
            'EditBarberController: Upload progress: ${(progress * 100).toStringAsFixed(1)}%',
          );
        },
      );

      if (downloadUrl != null) {
        selectedImageUrl.value = downloadUrl;
        log('EditBarberController: Image uploaded successfully: $downloadUrl');

        Get.snackbar(
          'Success',
          'Image uploaded successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } else {
        log('EditBarberController: Failed to upload image');
        selectedImageUrl.value = imageFile.path; // Fallback to local path

        Get.snackbar(
          'Upload Failed',
          'Failed to upload image. Please try again.',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      log('EditBarberController: Error uploading image: $e');
      selectedImageUrl.value = imageFile.path; // Fallback to local path

      Get.snackbar(
        'Upload Error',
        'Error uploading image: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isUploadingImage.value = false;
      uploadProgress.value = 1.0;
    }
  }

  /// Update barber
  Future<void> updateBarber() async {
    try {
      isUpdating.value = true;
      errorMessage.value = '';

      // Validation
      if (firstNameController.text.trim().isEmpty) {
        errorMessage.value = 'First name is required';
        _showErrorSnackbar('Validation Error', 'First name is required');
        return;
      }

      if (lastNameController.text.trim().isEmpty) {
        errorMessage.value = 'Last name is required';
        _showErrorSnackbar('Validation Error', 'Last name is required');
        return;
      }

      // Mobile number validation (optional but if provided should be valid)
      if (mobileController.text.trim().isNotEmpty) {
        final mobile = mobileController.text.trim();
        if (mobile.length < 10 || !RegExp(r'^[0-9]+$').hasMatch(mobile)) {
          errorMessage.value = 'Please enter a valid mobile number';
          _showErrorSnackbar(
            'Validation Error',
            'Please enter a valid mobile number',
          );
          return;
        }
      }

      if (selectedServiceIds.isEmpty) {
        errorMessage.value = 'At least one service must be selected';
        _showErrorSnackbar(
          'Validation Error',
          'Please select at least one service',
        );
        return;
      }

      // Create updated barber model
      final updatedBarber = BarberModel(
        id: barberId.value,
        firstname: firstNameController.text.trim(),
        lastname: lastNameController.text.trim(),
        bio: bioController.text.trim().isNotEmpty
            ? bioController.text.trim()
            : 'Experienced barber',
        available: isAvailable.value,
        profileimage:
            selectedImageUrl.value.isNotEmpty &&
                !selectedImageUrl.value.startsWith(
                  '/',
                ) // Check if it's a Firebase URL, not local path
            ? selectedImageUrl.value
            : currentBarber
                  .value
                  ?.profileimage, // Keep existing image if no new one uploaded
        assignedServices: selectedServiceIds.toList(),
        saloonid: currentBarber.value?.saloonid,
        isDelete: false,
        createdAt: currentBarber.value?.createdAt,
        updatedAt: DateTime.now(),
      );

      log(
        'EditBarberController: Updating barber: ${updatedBarber.firstname} ${updatedBarber.lastname}',
      );

      log(
        'EditBarberController: Updating barber with services: ${selectedServiceIds.toList()}',
      );

      final success = await BarberService.updateBarber(
        barberId.value,
        updatedBarber,
        mobileNo: mobileController.text.trim().isNotEmpty
            ? mobileController.text.trim()
            : null,
      );

      if (success) {
        successMessage.value = 'Barber updated successfully!';
        _showSuccessSnackbar('Success', 'Barber updated successfully!');

        // Navigate back with success result
        Get.back(result: true);
      } else {
        errorMessage.value = 'Failed to update barber';
        _showErrorSnackbar('Error', 'Failed to update barber');
      }
    } catch (e) {
      log('EditBarberController: Update barber error: $e');
      errorMessage.value = 'An error occurred while updating barber';
      _showErrorSnackbar('Error', 'An error occurred while updating barber');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Delete barber
  Future<void> deleteBarber() async {
    try {
      isUpdating.value = true;
      errorMessage.value = '';

      log('EditBarberController: Deleting barber: ${barberId.value}');

      final success = await BarberService.deleteBarberEnhanced(barberId.value);

      if (success) {
        successMessage.value = 'Barber deleted successfully!';
        _showSuccessSnackbar('Success', 'Barber deleted successfully!');

        // Navigate back with delete result
        Get.back(result: 'deleted');
      } else {
        errorMessage.value = 'Failed to delete barber';
        _showErrorSnackbar('Error', 'Failed to delete barber');
      }
    } catch (e) {
      log('EditBarberController: Delete barber error: $e');
      errorMessage.value = 'An error occurred while deleting barber';
      _showErrorSnackbar('Error', 'An error occurred while deleting barber');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }
}
