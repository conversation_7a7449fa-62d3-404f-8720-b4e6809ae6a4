import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../services/appointment_service.dart';
import '../../models/appoinment_model.dart';

class OwnerBookingsController extends GetxController
    with GetSingleTickerProviderStateMixin {
  // Tab controller
  late TabController tabController;
  final RxInt currentTabIndex = 0.obs;

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxBool isProcessingAction = false.obs;

  // Error handling
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  // Services
  final AppointmentService _appointmentService = AppointmentService();

  // Bookings data by status
  final RxList<Confirmed> pendingBookings = <Confirmed>[].obs;
  final RxList<Confirmed> approvedBookings = <Confirmed>[].obs;
  final RxList<Confirmed> ongoingBookings = <Confirmed>[].obs;
  final RxList<Confirmed> completedBookings = <Confirmed>[].obs;

  // OTP handling
  final RxMap<String, TextEditingController> otpControllers =
      <String, TextEditingController>{}.obs;
  final RxMap<String, bool> otpVerifying = <String, bool>{}.obs;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);
    tabController.addListener(() {
      currentTabIndex.value = tabController.index;
    });
    loadBookings();
  }

  @override
  void onClose() {
    tabController.dispose();
    // Dispose all OTP controllers
    for (var controller in otpControllers.values) {
      controller.dispose();
    }
    super.onClose();
  }

  /// Load all bookings data
  Future<void> loadBookings() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('OwnerBookingsController: Loading bookings data...');

      // Get appointments from the new API service
      final appointmentData = await _appointmentService.getAppointments();

      if (appointmentData != null && appointmentData.success) {
        _processNewAppointments(appointmentData.data);
        log('OwnerBookingsController: Successfully loaded appointments');
      } else {
        log('OwnerBookingsController: No appointments found or API failed');
        _clearAllBookings();
      }

      // Also load completed appointments
      final completedData = await _appointmentService
          .getCompletedAppointments();
      if (completedData != null && completedData.success) {
        completedBookings.addAll(completedData.data.confirmed);
        log(
          'OwnerBookingsController: Successfully loaded completed appointments',
        );
      }
    } catch (e) {
      log('OwnerBookingsController: Error loading bookings: $e');
      hasError.value = true;
      errorMessage.value = e.toString();
      _setFallbackData();
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh bookings data
  Future<void> refreshBookings() async {
    try {
      isRefreshing.value = true;
      await loadBookings();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Process new appointments from API
  void _processNewAppointments(Data appointmentData) {
    // Clear existing data
    _clearAllBookings();

    // Add appointments to respective lists based on API structure
    pendingBookings.addAll(appointmentData.pending);
    ongoingBookings.addAll(appointmentData.inProgress);
    approvedBookings.addAll(appointmentData.confirmed);

    // Ensure OTP controllers for approved bookings
    for (var booking in approvedBookings) {
      _ensureOtpController(booking.id);
    }

    log(
      'OwnerBookingsController: Processed appointments - '
      'Pending: ${pendingBookings.length}, '
      'Approved: ${approvedBookings.length}, '
      'Ongoing: ${ongoingBookings.length}, '
      'Completed: ${completedBookings.length}',
    );
  }

  /// Clear all booking lists
  void _clearAllBookings() {
    pendingBookings.clear();
    approvedBookings.clear();
    ongoingBookings.clear();
    completedBookings.clear();
  }

  /// Ensure OTP controller exists for appointment
  void _ensureOtpController(String appointmentId) {
    if (!otpControllers.containsKey(appointmentId)) {
      otpControllers[appointmentId] = TextEditingController();
      otpVerifying[appointmentId] = false;
    }
  }

  /// Set fallback data when API fails
  void _setFallbackData() {
    // Add some dummy data for testing
    pendingBookings.value = [
      Confirmed(
        id: 'dummy1',
        barberName: 'John Smith',
        userName: 'Customer 1',
        amount: 500,
        bookingType: 'Haircut & Styling',
        status: 'pending',
        startTime: '10:00:00',
        endTime: '11:00:00',
        appointmentDate: DateTime.now(),
        createdAt: DateTime.now(),
      ),
    ];
  }

  /// Accept booking
  Future<void> acceptBooking(String appointmentId) async {
    try {
      isProcessingAction.value = true;
      log('OwnerBookingsController: Accepting booking: $appointmentId');

      // Call API to accept booking
      final success = await _appointmentService.makeAppointmentDecision(
        appointmentId: appointmentId,
        decision: 'Accepted',
      );

      if (success) {
        // Move booking from pending to approved
        final booking = pendingBookings.firstWhereOrNull(
          (b) => b.id == appointmentId,
        );
        if (booking != null) {
          pendingBookings.remove(booking);
          booking.status = 'approved';
          approvedBookings.add(booking);
          _ensureOtpController(appointmentId);

          // Switch to approved tab
          tabController.animateTo(1);
        }
      } else {
        throw Exception('Failed to accept booking');
      }

      _showSuccessSnackbar('Success', 'Booking accepted successfully!');
    } catch (e) {
      log('OwnerBookingsController: Error accepting booking: $e');
      _showErrorSnackbar(
        'Error',
        'Failed to accept booking. Please try again.',
      );
    } finally {
      isProcessingAction.value = false;
    }
  }

  /// Deny booking
  Future<void> denyBooking(String appointmentId) async {
    try {
      isProcessingAction.value = true;
      log('OwnerBookingsController: Denying booking: $appointmentId');

      // Call API to deny booking
      final success = await _appointmentService.makeAppointmentDecision(
        appointmentId: appointmentId,
        decision: 'Rejected',
      );

      if (success) {
        // Remove booking from pending
        pendingBookings.removeWhere((b) => b.id == appointmentId);
        _showSuccessSnackbar('Success', 'Booking denied successfully!');
      } else {
        throw Exception('Failed to deny booking');
      }
    } catch (e) {
      log('OwnerBookingsController: Error denying booking: $e');
      _showErrorSnackbar('Error', 'Failed to deny booking. Please try again.');
    } finally {
      isProcessingAction.value = false;
    }
  }

  /// Verify OTP
  Future<void> verifyOtp(String appointmentId) async {
    final otpController = otpControllers[appointmentId];
    if (otpController == null || otpController.text.length != 4) {
      _showErrorSnackbar('Invalid OTP', 'Please enter a valid 4-digit OTP');
      return;
    }

    try {
      otpVerifying[appointmentId] = true;
      log('OwnerBookingsController: Verifying OTP for booking: $appointmentId');

      // Call API to start service with OTP
      final success = await _appointmentService.startService(
        appointmentId: appointmentId,
        otp: otpController.text,
      );

      if (success) {
        // Move booking from approved to ongoing
        final booking = approvedBookings.firstWhereOrNull(
          (b) => b.id == appointmentId,
        );
        if (booking != null) {
          approvedBookings.remove(booking);
          booking.status = 'ongoing';
          ongoingBookings.add(booking);

          // Clear OTP controller
          otpController.clear();

          // Switch to ongoing tab
          tabController.animateTo(2);
        }
      } else {
        throw Exception('Invalid OTP or failed to start service');
      }

      _showSuccessSnackbar('Success', 'OTP verified successfully!');
    } catch (e) {
      log('OwnerBookingsController: Error verifying OTP: $e');
      _showErrorSnackbar('Error', 'Failed to verify OTP. Please try again.');
    } finally {
      otpVerifying[appointmentId] = false;
    }
  }

  /// Mark booking as complete
  Future<void> markComplete(String appointmentId) async {
    try {
      isProcessingAction.value = true;
      log('OwnerBookingsController: Marking booking complete: $appointmentId');

      // Call API to complete service
      final success = await _appointmentService.completeService(
        appointmentId: appointmentId,
      );

      if (success) {
        // Move booking from ongoing to completed
        final booking = ongoingBookings.firstWhereOrNull(
          (b) => b.id == appointmentId,
        );
        if (booking != null) {
          ongoingBookings.remove(booking);
          booking.status = 'completed';
          completedBookings.add(booking);

          // Switch to completed tab
          tabController.animateTo(3);
        }
      } else {
        throw Exception('Failed to complete service');
      }

      _showSuccessSnackbar('Success', 'Booking marked as complete!');
    } catch (e) {
      log('OwnerBookingsController: Error marking booking complete: $e');
      _showErrorSnackbar(
        'Error',
        'Failed to mark booking complete. Please try again.',
      );
    } finally {
      isProcessingAction.value = false;
    }
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.green.withValues(alpha: 0.1),
      colorText: Colors.green,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.red.withValues(alpha: 0.1),
      colorText: Colors.red,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }
}
