import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../models/withdrawal_models.dart';
import '../../services/withdrawal_service.dart';

class WithdrawalController extends GetxController {
  // Form controllers
  final amountController = TextEditingController();
  final bankAccountController = TextEditingController();
  final ifscCodeController = TextEditingController();
  final accountNameController = TextEditingController();
  final upiIdController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;
  
  // Withdrawal method selection
  final Rx<WithdrawalMethod> selectedMethod = WithdrawalMethod.bankTransfer.obs;
  
  // Available balance (should be passed from parent screen)
  final RxDouble availableBalance = 0.0.obs;
  
  // Withdrawal response data
  final Rx<WithdrawalData?> withdrawalData = Rx<WithdrawalData?>(null);

  // Form key for validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    log('WithdrawalController: Initialized');
  }

  @override
  void onClose() {
    // Dispose controllers
    amountController.dispose();
    bankAccountController.dispose();
    ifscCodeController.dispose();
    accountNameController.dispose();
    upiIdController.dispose();
    super.onClose();
  }

  /// Set available balance from parent screen
  void setAvailableBalance(double balance) {
    availableBalance.value = balance;
    log('WithdrawalController: Available balance set to: ₹${balance.toStringAsFixed(2)}');
  }

  /// Change withdrawal method
  void changeWithdrawalMethod(WithdrawalMethod method) {
    selectedMethod.value = method;
    log('WithdrawalController: Withdrawal method changed to: ${method.displayName}');
  }

  /// Submit withdrawal request
  Future<void> submitWithdrawalRequest() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isLoading.value = true;
      errorMessage.value = '';
      successMessage.value = '';
      withdrawalData.value = null;

      // Parse amount
      final amount = double.tryParse(amountController.text.trim()) ?? 0.0;
      
      // Validate amount against available balance
      if (amount > availableBalance.value) {
        errorMessage.value = 'Withdrawal amount cannot exceed available balance';
        _showErrorSnackbar('Invalid Amount', errorMessage.value);
        return;
      }

      // Create withdrawal request
      final request = WithdrawalRequest(
        amount: amount,
        withdrawalMethod: selectedMethod.value.value,
        bankAccountNumber: bankAccountController.text.trim(),
        bankIfscCode: ifscCodeController.text.trim().toUpperCase(),
        bankAccountName: accountNameController.text.trim(),
        upiId: upiIdController.text.trim().isEmpty 
            ? null 
            : upiIdController.text.trim(),
      );

      // Validate request
      if (!WithdrawalService.validateWithdrawalRequest(request)) {
        errorMessage.value = 'Please check your input data';
        _showErrorSnackbar('Validation Error', errorMessage.value);
        return;
      }

      log('WithdrawalController: Submitting withdrawal request');

      final response = await WithdrawalService.submitWithdrawalRequest(request);

      if (response.success && response.data != null) {
        withdrawalData.value = response.data;
        successMessage.value = 'Withdrawal request submitted successfully';
        
        _showSuccessSnackbar('Success', 'Withdrawal request submitted successfully');
        
        // Clear form
        _clearForm();
        
        // Navigate back after delay
        Future.delayed(const Duration(seconds: 2), () {
          Get.back(result: true);
        });
      } else {
        errorMessage.value = response.message ?? 'Failed to submit withdrawal request';
        _showErrorSnackbar('Request Failed', errorMessage.value);
      }
    } catch (e) {
      log('WithdrawalController: Error submitting withdrawal: $e');
      errorMessage.value = 'Failed to submit withdrawal request';
      _showErrorSnackbar('Error', 'Failed to submit withdrawal request');
    } finally {
      isLoading.value = false;
    }
  }

  /// Clear form data
  void _clearForm() {
    amountController.clear();
    bankAccountController.clear();
    ifscCodeController.clear();
    accountNameController.clear();
    upiIdController.clear();
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.green,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.red,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }

  /// Validate amount field
  String? validateAmount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Amount is required';
    }

    final amount = double.tryParse(value.trim());
    if (amount == null) {
      return 'Please enter a valid amount';
    }

    if (amount < WithdrawalService.getMinimumWithdrawalAmount()) {
      return 'Minimum withdrawal amount is ₹${WithdrawalService.getMinimumWithdrawalAmount().toStringAsFixed(0)}';
    }

    if (amount > WithdrawalService.getMaximumWithdrawalAmount()) {
      return 'Maximum withdrawal amount is ₹${WithdrawalService.getMaximumWithdrawalAmount().toStringAsFixed(0)}';
    }

    if (amount > availableBalance.value) {
      return 'Amount cannot exceed available balance';
    }

    return null;
  }

  /// Validate required field
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validate bank account number
  String? validateBankAccount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Bank account number is required';
    }

    if (!WithdrawalService.isValidBankAccountNumber(value)) {
      return 'Please enter a valid bank account number (9-18 digits)';
    }

    return null;
  }

  /// Validate IFSC code
  String? validateIfscCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'IFSC code is required';
    }

    if (!WithdrawalService.isValidIfscCode(value)) {
      return 'Please enter a valid IFSC code (e.g., SBIN0000123)';
    }

    return null;
  }

  /// Validate UPI ID (optional)
  String? validateUpiId(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // UPI is optional
    }

    if (!WithdrawalService.isValidUpiId(value)) {
      return 'Please enter a valid UPI ID (e.g., user@paytm)';
    }

    return null;
  }

  /// Format currency for display
  String formatCurrency(double amount) {
    return WithdrawalService.formatCurrency(amount);
  }
}
