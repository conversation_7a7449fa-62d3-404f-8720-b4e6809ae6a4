import 'dart:developer';
import 'package:get/get.dart';
import '../../services/shared_preferences_service.dart';
import '../../services/user_data_service.dart';
import '../../services/dashboard_service.dart';
import '../../models/owner_dashboard_models.dart';
import '../../models/chart_data_models.dart';
import '../owner/owner_navigation_controller.dart';

class OwnerHomeController extends GetxController {
  // Owner data
  final RxString ownerName = 'Owner'.obs;
  final RxString salonName = 'Your Salon'.obs;
  final RxString salonLocation = 'Select your location'.obs;

  // Dashboard statistics
  final RxInt todayAppointmentsCount = 0.obs;
  final RxDouble todayRevenue = 0.0.obs;
  final RxInt newCustomers = 0.obs;
  final RxInt totalServices = 0.obs;

  // Weekly statistics
  final RxDouble weeklyEarnings = 0.0.obs;
  final RxInt weeklyBookings = 0.obs;
  final RxDouble earningsGrowth = 0.0.obs;
  final RxDouble bookingsGrowth = 0.0.obs;

  // Chart data
  final RxString chartTimeFilter = 'Weekly'.obs;
  final RxList<double> chartData = <double>[].obs;

  // New chart data models
  final Rx<WeeklyChartResponse?> weeklyChartData = Rx<WeeklyChartResponse?>(
    null,
  );
  final Rx<MonthlyChartResponse?> monthlyChartData = Rx<MonthlyChartResponse?>(
    null,
  );

  // Bank balance
  final RxDouble bankBalance = 0.0.obs;

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isLoadingStats = false.obs;
  final RxBool isLoadingChart = false.obs;

  // Dashboard data
  final Rx<OwnerDashboardData?> dashboardData = Rx<OwnerDashboardData?>(null);
  final RxList<Appointment> recentAppointments = <Appointment>[].obs;
  final RxList<Appointment> todayAppointments = <Appointment>[].obs;

  // Error handling
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadOwnerData();
    _loadDashboardStats();
  }

  // Load owner data from API or SharedPreferences
  Future<void> _loadOwnerData() async {
    try {
      // First try to load from API
      final userProfile = await UserDataService.getUserProfile();

      if (userProfile != null) {
        // Use data from API
        ownerName.value = userProfile.fullName.isNotEmpty
            ? userProfile.fullName
            : 'Owner';
        salonName.value = userProfile.salonName ?? 'Your Salon';
        salonLocation.value = userProfile.shortAddress;

        log(
          'Loaded owner data from API - Name: ${ownerName.value}, Salon: ${salonName.value}, Location: ${salonLocation.value}',
        );
      } else {
        // Fallback to SharedPreferences data
        final cachedName = await SharedPreferencesService.getUserName();

        if (cachedName != null && cachedName.isNotEmpty) {
          ownerName.value = cachedName;
        } else {
          // Extract name from email if available
          final email = await SharedPreferencesService.getEmail();
          if (email != null && email.isNotEmpty) {
            final emailParts = email.split('@');
            if (emailParts.isNotEmpty) {
              // Capitalize first letter and replace dots/underscores with spaces
              final nameFromEmail = emailParts[0]
                  .replaceAll(RegExp(r'[._]'), ' ')
                  .split(' ')
                  .map(
                    (word) => word.isNotEmpty
                        ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                        : '',
                  )
                  .join(' ');
              ownerName.value = nameFromEmail.isNotEmpty
                  ? nameFromEmail
                  : 'Owner';
            }
          } else {
            ownerName.value = 'Owner';
          }
        }

        salonName.value = 'Your Salon'; // Default salon name

        // Try to load cached location
        final cachedLocation = await SharedPreferencesService.getUserLocation();
        salonLocation.value = cachedLocation ?? 'Select your location';

        log(
          'Loaded owner data from SharedPreferences - Name: ${ownerName.value}, Location: ${salonLocation.value}',
        );
      }
    } catch (e) {
      log('Error loading owner data: $e');
      ownerName.value = 'Owner';
      salonName.value = 'Your Salon';
    }
  }

  // Load dashboard statistics
  Future<void> _loadDashboardStats() async {
    try {
      isLoadingStats.value = true;
      hasError.value = false;
      errorMessage.value = '';

      // Load dashboard data from API
      final response = await DashboardService.getOwnerDashboard();

      if (response.success && response.data != null) {
        dashboardData.value = response.data;

        // Update observable values
        weeklyEarnings.value = response.data!.weeklyEarnings;
        weeklyBookings.value = response.data!.weeklyBookings;
        bankBalance.value = response.data!.bankBalance;
        earningsGrowth.value = response.data!.earningsGrowth;
        bookingsGrowth.value = response.data!.bookingsGrowth;

        // Update appointments - show confirmed appointments as recent
        final allAppointments = <Appointment>[];
        allAppointments.addAll(response.data!.appointmentData.confirmed);
        allAppointments.addAll(response.data!.appointmentData.pending);

        // Sort by creation date (most recent first)
        allAppointments.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        recentAppointments.value = allAppointments.take(3).toList();
        todayAppointments.value = DashboardService.getTodayAppointments(
          response.data!,
        );
        todayAppointmentsCount.value = DashboardService.getTotalAppointments(
          response.data!,
        );

        log('Dashboard statistics loaded successfully');

        // Load chart data based on current filter
        await _loadChartData();
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      log('Error loading dashboard statistics: $e');
      hasError.value = true;
      errorMessage.value = e.toString();

      // Set fallback values
      setFallbackData();
    } finally {
      isLoadingStats.value = false;
    }
  }

  // Load chart data based on current filter
  Future<void> _loadChartData() async {
    try {
      isLoadingChart.value = true;

      if (chartTimeFilter.value == 'Weekly') {
        // Load new weekly chart data
        final weeklyResponse = await DashboardService.getWeeklyChartData();
        if (weeklyResponse.success) {
          weeklyChartData.value = weeklyResponse;
          chartData.value = weeklyResponse.earningsData;

          // Update weekly statistics from chart data
          weeklyEarnings.value = weeklyResponse.totalEarnings;
          weeklyBookings.value = weeklyResponse.totalBookings;

          // Calculate growth percentage
          if (weeklyResponse.earningsData.length >= 2) {
            earningsGrowth.value = ChartStatistics.calculateGrowthPercentage(
              weeklyResponse.earningsData,
            );
          }

          log(
            'Weekly chart data loaded: ${weeklyResponse.data.length} data points',
          );
        }
      } else {
        // Load new monthly chart data
        final monthlyResponse = await DashboardService.getMonthlyChartData();
        if (monthlyResponse.success) {
          monthlyChartData.value = monthlyResponse;
          chartData.value = monthlyResponse.earningsData;

          // Update monthly statistics from chart data
          weeklyEarnings.value = monthlyResponse.totalEarnings;
          weeklyBookings.value = monthlyResponse.totalBookings;

          // Calculate growth percentage
          if (monthlyResponse.earningsData.length >= 2) {
            earningsGrowth.value = ChartStatistics.calculateGrowthPercentage(
              monthlyResponse.earningsData,
            );
          }

          log(
            'Monthly chart data loaded: ${monthlyResponse.data.length} data points',
          );
        }
      }

      log('Chart data loaded for ${chartTimeFilter.value}');
    } catch (e) {
      log('Error loading chart data: $e');
      // Set fallback chart data
      chartData.value = chartTimeFilter.value == 'Weekly'
          ? [8000, 9500, 7200, 11000, 12500, 10800, 13200]
          : [
              85000,
              92000,
              78000,
              105000,
              98000,
              112000,
              125000,
              118000,
              135000,
              142000,
              138000,
              155000,
            ];
    } finally {
      isLoadingChart.value = false;
    }
  }

  // Set fallback data when API fails
  void setFallbackData() {
    weeklyEarnings.value = 12500.0;
    weeklyBookings.value = 45;
    earningsGrowth.value = 12.5;
    bookingsGrowth.value = -5.2;
    bankBalance.value = 85000.0;
    todayAppointmentsCount.value = 8;

    // Set fallback chart data
    chartData.value = chartTimeFilter.value == 'Weekly'
        ? [8000, 9500, 7200, 11000, 12500, 10800, 13200]
        : [
            85000,
            92000,
            78000,
            105000,
            98000,
            112000,
            125000,
            118000,
            135000,
            142000,
            138000,
            155000,
          ];
  }

  // Refresh all data
  Future<void> refreshData() async {
    await Future.wait([_loadOwnerData(), _loadDashboardStats()]);
  }

  // Handle chart time filter change
  Future<void> onChartFilterChanged(String newFilter) async {
    if (chartTimeFilter.value != newFilter) {
      chartTimeFilter.value = newFilter;
      await _loadChartData();
    }
  }

  // Get appointments by status
  List<Appointment> getAppointmentsByStatus(String status) {
    if (dashboardData.value == null) return [];
    return DashboardService.getAppointmentsByStatus(
      dashboardData.value!,
      status,
    );
  }

  // Get formatted currency
  String formatCurrency(double amount) {
    return DashboardService.formatCurrency(amount);
  }

  // Retry loading data
  Future<void> retryLoadData() async {
    hasError.value = false;
    errorMessage.value = '';
    await _loadDashboardStats();
  }

  // Navigate to specific sections
  void navigateToBookings() {
    final navigationController = Get.find<OwnerNavigationController>();
    navigationController.goToBookings();
  }

  void navigateToAddService() {
    final navigationController = Get.find<OwnerNavigationController>();
    navigationController.goToAddService();
  }

  void navigateToAddBarber() {
    final navigationController = Get.find<OwnerNavigationController>();
    navigationController.goToAddBarber();
  }
}
