import 'dart:developer';
import 'package:get/get.dart';
import 'owner_profile_controller.dart';
import '../../routes/app_routes.dart';

class OwnerNavigationController extends GetxController {
  final RxInt currentIndex = 0.obs;

  void changeIndex(int index) {
    currentIndex.value = index;
  }

  // Navigation methods for programmatic navigation
  void goToHome() => changeIndex(0);
  void goToBookings() => changeIndex(1);
  void goToAddService() => changeIndex(2);
  void goToAddBarber() => changeIndex(3);
  void goToProfile() => changeIndex(4);

  // Profile-related navigation methods
  Future<void> navigateToEditProfile() async {
    final result = await Get.toNamed(AppRoutes.ownerEditProfile);

    // If profile was updated successfully, refresh the profile data
    if (result == true) {
      // Find and refresh the profile controller if it exists
      try {
        if (Get.isRegistered<OwnerProfileController>()) {
          final profileController = Get.find<OwnerProfileController>();
          await profileController.loadOwnerProfile();
        }
      } catch (e) {
        // Profile controller not found, that's okay
        log('Navigation: Profile controller not found for refresh: $e');
      }
    }
  }

  void navigateToPayoutHistory() {
    // Placeholder for payout history navigation
    Get.snackbar(
      'Coming Soon',
      'Payout history feature will be available soon',
      snackPosition: SnackPosition.TOP,
    );
  }

  void navigateToBankDetails() {
    // Placeholder for bank details navigation
    Get.snackbar(
      'Coming Soon',
      'Bank details feature will be available soon',
      snackPosition: SnackPosition.TOP,
    );
  }

  void navigateToPrivacyPolicy() {
    // Placeholder for privacy policy navigation
    Get.snackbar(
      'Coming Soon',
      'Privacy policy feature will be available soon',
      snackPosition: SnackPosition.TOP,
    );
  }

  void navigateToTermsConditions() {
    // Placeholder for terms & conditions navigation
    Get.snackbar(
      'Coming Soon',
      'Terms & conditions feature will be available soon',
      snackPosition: SnackPosition.TOP,
    );
  }

  void navigateToHelpSupport() {
    // Placeholder for help & support navigation
    Get.snackbar(
      'Coming Soon',
      'Help & support feature will be available soon',
      snackPosition: SnackPosition.TOP,
    );
  }
}
