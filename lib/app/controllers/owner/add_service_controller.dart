import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/app_constants.dart';
import '../../models/owner_service_models.dart';
import '../../services/owner_service_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../services/image_picker_service.dart';

class AddServiceController extends GetxController {
  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Form controllers
  final serviceNameController = TextEditingController();
  final categoryController = TextEditingController();
  final priceController = TextEditingController();
  final durationController = TextEditingController();

  // Focus nodes
  final serviceNameFocus = FocusNode();
  final categoryFocus = FocusNode();
  final priceFocus = FocusNode();
  final durationFocus = FocusNode();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isAddingService = false.obs;
  final RxBool isDeletingService = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Service list (using new model)
  final RxList<GetAllServiceModel> services = <GetAllServiceModel>[].obs;

  // Image selection
  final Rx<File?> selectedImage = Rx<File?>(null);
  final RxString selectedImageUrl = ''.obs;
  final RxInt selectedPredefinedImageIndex = (-1).obs;

  // Firebase upload progress
  final RxBool isUploadingImage = false.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final FirebaseStorageService _firebaseStorage = FirebaseStorageService();

  // Predefined service images
  final List<String> predefinedImages = [
    'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=300',
    'https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=300',
    'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=300',
    'https://images.unsplash.com/photo-1622287162716-f311baa1a2b8?w=300',
    'https://images.unsplash.com/photo-1559599101-f09722fb4948?w=300',
  ];

  // Form validation
  final RxBool isFormValid = false.obs;

  // Editing state
  final RxBool isEditing = false.obs;
  final RxString editingServiceId = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _setupFormValidation();
    loadServices();
  }

  @override
  void onClose() {
    serviceNameController.dispose();
    categoryController.dispose();
    priceController.dispose();
    durationController.dispose();
    serviceNameFocus.dispose();
    categoryFocus.dispose();
    priceFocus.dispose();
    durationFocus.dispose();
    super.onClose();
  }

  /// Setup form validation
  void _setupFormValidation() {
    serviceNameController.addListener(_validateForm);
    categoryController.addListener(_validateForm);
    priceController.addListener(_validateForm);
    durationController.addListener(_validateForm);
  }

  /// Validate form
  void _validateForm() {
    final isValid =
        serviceNameController.text.trim().isNotEmpty &&
        categoryController.text.trim().isNotEmpty &&
        priceController.text.trim().isNotEmpty &&
        durationController.text.trim().isNotEmpty &&
        double.tryParse(priceController.text) != null &&
        int.tryParse(durationController.text) != null;

    isFormValid.value = isValid;
  }

  /// Enhanced validation with specific error messages
  bool _validateFormWithMessages() {
    // First validate using form key
    if (!formKey.currentState!.validate()) {
      _showErrorSnackbar(
        'Validation Error',
        'Please fix the errors in the form',
      );
      return false;
    }

    // Additional business logic validation
    final serviceName = serviceNameController.text.trim();
    final categoryName = categoryController.text.trim();
    final priceText = priceController.text.trim();
    final durationText = durationController.text.trim();

    // Service name validation
    if (serviceName.isEmpty) {
      _showErrorSnackbar('Validation Error', 'Service name is required');
      serviceNameFocus.requestFocus();
      return false;
    }

    if (serviceName.length < 2) {
      _showErrorSnackbar(
        'Validation Error',
        'Service name must be at least 2 characters',
      );
      serviceNameFocus.requestFocus();
      return false;
    }

    // Category validation
    if (categoryName.isEmpty) {
      _showErrorSnackbar('Validation Error', 'Category name is required');
      categoryFocus.requestFocus();
      return false;
    }

    if (categoryName.length < 2) {
      _showErrorSnackbar(
        'Validation Error',
        'Category name must be at least 2 characters',
      );
      categoryFocus.requestFocus();
      return false;
    }

    // Price validation
    if (priceText.isEmpty) {
      _showErrorSnackbar('Validation Error', 'Price is required');
      priceFocus.requestFocus();
      return false;
    }

    final price = double.tryParse(priceText);
    if (price == null) {
      _showErrorSnackbar('Validation Error', 'Please enter a valid price');
      priceFocus.requestFocus();
      return false;
    }

    if (price <= 0) {
      _showErrorSnackbar('Validation Error', 'Price must be greater than 0');
      priceFocus.requestFocus();
      return false;
    }

    // Duration validation
    if (durationText.isEmpty) {
      _showErrorSnackbar('Validation Error', 'Duration is required');
      durationFocus.requestFocus();
      return false;
    }

    final duration = int.tryParse(durationText);
    if (duration == null) {
      _showErrorSnackbar(
        'Validation Error',
        'Please enter a valid duration in minutes',
      );
      durationFocus.requestFocus();
      return false;
    }

    if (duration <= 0) {
      _showErrorSnackbar(
        'Validation Error',
        'Duration must be greater than 0 minutes',
      );
      durationFocus.requestFocus();
      return false;
    }

    if (duration > 480) {
      // 8 hours max
      _showErrorSnackbar(
        'Validation Error',
        'Duration cannot exceed 480 minutes (8 hours)',
      );
      durationFocus.requestFocus();
      return false;
    }

    log('AddServiceController: All validations passed');
    log('AddServiceController: Service name: "$serviceName"');
    log('AddServiceController: Category: "$categoryName"');
    log('AddServiceController: Price: $price');
    log('AddServiceController: Duration: $duration minutes');

    return true;
  }

  /// Load services from API
  Future<void> loadServices() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      log('AddServiceController: Loading services');

      final servicesList = await OwnerServiceService.getBarberService();
      services.value = servicesList;
      log('AddServiceController: Loaded ${services.length} services');
    } catch (e) {
      log('AddServiceController: Load services error: $e');
      if (e is ServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to load services. Please try again.';
        _showErrorSnackbar(
          'Error',
          'Failed to load services. Please try again.',
        );
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// Add new service
  Future<void> addService() async {
    // Enhanced validation with specific error messages
    if (!_validateFormWithMessages()) {
      return;
    }

    try {
      isAddingService.value = true;
      errorMessage.value = '';

      final request = OwnerServiceRequest(
        name: serviceNameController.text.trim(),
        categoryname: categoryController.text.trim(),
        price: double.parse(priceController.text.trim()),
        length: int.parse(durationController.text.trim()),
        serviceImage: selectedImageUrl.value.isNotEmpty
            ? selectedImageUrl.value
            : null,
      );

      log('AddServiceController: Adding service: ${request.name}');
      log('AddServiceController: Service details: ${request.toString()}');
      log('AddServiceController: Price: ${request.price}');
      log('AddServiceController: Duration: ${request.length}');
      log(
        'AddServiceController: Selected image: ${selectedImage.value?.path ?? 'No image'}',
      );
      log(
        'AddServiceController: Selected image URL: ${selectedImageUrl.value}',
      );

      final response = await OwnerServiceService.addService(
        request,
        selectedImage.value,
      );

      if (response.success) {
        successMessage.value = response.message;
        _showSuccessSnackbar('Success', 'Service added successfully!');
        _clearForm();
        await loadServices(); // Refresh the list

        // Close the modal with a small delay to ensure snackbar is shown
        Future.delayed(const Duration(milliseconds: 100), () {
          if (Get.isBottomSheetOpen == true) {
            Get.back(); // Close the modal
          }
        });
      } else {
        errorMessage.value = response.message;
        _showErrorSnackbar('Failed to add service', response.message);
      }
    } catch (e) {
      log('AddServiceController: Add service error: $e');
      if (e is ServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to add service. Please try again.';
        _showErrorSnackbar('Error', 'Failed to add service. Please try again.');
      }
    } finally {
      isAddingService.value = false;
    }
  }

  /// Edit service
  Future<void> editService(GetAllServiceModel service) async {
    // Clear form first to ensure clean state
    _clearForm();

    // Set editing state
    isEditing.value = true;
    editingServiceId.value = service.id;

    // Populate form with service data
    serviceNameController.text = service.name;
    categoryController.text = service.categoryName;
    priceController.text = service.price.toString();
    durationController.text = service.length.toString();

    if (service.hasImage) {
      selectedImageUrl.value = service.images;
      // Check if it's a predefined image
      final index = predefinedImages.indexOf(service.images);
      if (index != -1) {
        selectedPredefinedImageIndex.value = index;
      }
    }

    // Modal will be shown from UI
  }

  /// Update service
  Future<void> updateService() async {
    // Enhanced validation with specific error messages
    if (!_validateFormWithMessages()) {
      return;
    }

    try {
      isAddingService.value = true;
      errorMessage.value = '';

      final request = OwnerServiceRequest(
        name: serviceNameController.text.trim(),
        categoryname: categoryController.text.trim(),
        price: double.parse(priceController.text.trim()),
        length: int.parse(durationController.text.trim()),
        serviceImage: selectedImageUrl.value.isNotEmpty
            ? selectedImageUrl.value
            : null,
      );

      log('AddServiceController: Updating service: ${editingServiceId.value}');

      final response = await OwnerServiceService.updateService(
        editingServiceId.value,
        request,
        selectedImage.value,
      );

      if (response.success) {
        successMessage.value = response.message;
        _showSuccessSnackbar('Success', 'Service updated successfully!');
        _clearForm();
        await loadServices(); // Refresh the list

        // Close the modal with a small delay to ensure snackbar is shown
        Future.delayed(const Duration(milliseconds: 100), () {
          if (Get.isBottomSheetOpen == true) {
            Get.back(); // Close the modal
          }
        });
      } else {
        errorMessage.value = response.message;
        _showErrorSnackbar('Failed to update service', response.message);
      }
    } catch (e) {
      log('AddServiceController: Update service error: $e');
      if (e is ServiceException) {
        errorMessage.value = e.userFriendlyMessage;
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        errorMessage.value = 'Failed to update service. Please try again.';
        _showErrorSnackbar(
          'Error',
          'Failed to update service. Please try again.',
        );
      }
    } finally {
      isAddingService.value = false;
    }
  }

  /// Delete service with confirmation
  Future<void> deleteService(GetAllServiceModel service) async {
    final confirmed = await _showDeleteConfirmationDialog(service.name);
    if (!confirmed) return;

    try {
      isDeletingService.value = true;
      errorMessage.value = '';

      log('AddServiceController: Deleting service: ${service.id}');

      final response = await OwnerServiceService.deleteBarberService(
        service.id,
      );

      if (response.success) {
        _showSuccessSnackbar(
          'Success',
          'Service "${service.name}" deleted successfully!',
        );
        await loadServices(); // Refresh the list
      } else {
        _showErrorSnackbar(
          'Error',
          response.message.isNotEmpty
              ? response.message
              : 'Failed to delete service. Please try again.',
        );
      }
    } catch (e) {
      log('AddServiceController: Delete service error: $e');
      if (e is ServiceException) {
        _showErrorSnackbar('Error', e.userFriendlyMessage);
      } else {
        _showErrorSnackbar(
          'Error',
          'Failed to delete service. Please try again.',
        );
      }
    } finally {
      isDeletingService.value = false;
    }
  }

  /// Select image from camera or gallery with Firebase upload
  Future<void> selectImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        final imageFile = File(image.path);
        selectedImage.value = imageFile;
        selectedImageUrl.value = '';
        selectedPredefinedImageIndex.value = -1;

        log('AddServiceController: Image selected from ${source.name}');

        // Upload to Firebase immediately
        await _uploadImageToFirebase(imageFile);
      }
    } catch (e) {
      log('AddServiceController: Image selection error: $e');
      _showErrorSnackbar('Error', 'Failed to select image. Please try again.');
    }
  }

  /// Upload image to Firebase Storage
  Future<void> _uploadImageToFirebase(File imageFile) async {
    try {
      isUploadingImage.value = true;
      uploadProgress.value = 0.0;

      log('AddServiceController: Starting Firebase upload');

      final downloadUrl = await _firebaseStorage.uploadServiceImage(
        imageFile,
        onProgress: (progress) {
          uploadProgress.value = progress;
          log(
            'AddServiceController: Upload progress: ${(progress * 100).toInt()}%',
          );
        },
      );

      if (downloadUrl != null) {
        selectedImageUrl.value = downloadUrl;
        log('AddServiceController: Firebase upload successful: $downloadUrl');

        _showSuccessSnackbar(
          'Image Uploaded',
          'Service image uploaded successfully',
        );
      } else {
        throw Exception('Failed to get download URL');
      }
    } catch (e) {
      log('AddServiceController: Firebase upload error: $e');
      _showErrorSnackbar(
        'Upload Failed',
        'Failed to upload image. Please try again.',
      );

      // Reset image selection on upload failure
      selectedImage.value = null;
      selectedImageUrl.value = '';
    } finally {
      isUploadingImage.value = false;
      uploadProgress.value = 0.0;
    }
  }

  /// Select image using enhanced image picker service
  Future<void> selectImageWithPicker() async {
    try {
      final imageFile = await ImagePickerService.showImagePicker(
        context: Get.context!,
        title: 'Select Service Image',
        allowCropping: true,
      );

      if (imageFile != null) {
        selectedImage.value = imageFile;
        selectedImageUrl.value = '';
        selectedPredefinedImageIndex.value = -1;

        log('AddServiceController: Image selected with enhanced picker');

        // Upload to Firebase immediately
        await _uploadImageToFirebase(imageFile);
      }
    } catch (e) {
      log('AddServiceController: Enhanced image selection error: $e');
      _showErrorSnackbar('Error', 'Failed to select image');
    }
  }

  /// Select predefined image
  void selectPredefinedImage(int index) {
    if (index >= 0 && index < predefinedImages.length) {
      selectedPredefinedImageIndex.value = index;
      selectedImageUrl.value = predefinedImages[index];
      selectedImage.value = null;
      log('AddServiceController: Predefined image selected: $index');
    }
  }

  /// Clear form
  void _clearForm() {
    serviceNameController.clear();
    categoryController.clear();
    priceController.clear();
    durationController.clear();
    selectedImage.value = null;
    selectedImageUrl.value = '';
    selectedPredefinedImageIndex.value = -1;
    isEditing.value = false;
    editingServiceId.value = '';
    errorMessage.value = '';
    successMessage.value = '';
  }

  /// Show add service modal for new service
  void showAddServiceModal() {
    // Clear form for new service
    _clearForm();
  }

  /// Show delete confirmation dialog
  Future<bool> _showDeleteConfirmationDialog(String serviceName) async {
    return await Get.dialog<bool>(
          AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            ),
            title: const Text('Delete Service'),
            content: Text('Are you sure you want to delete "$serviceName"?'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppConstants.primaryBlack,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppConstants.primaryBlack,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      borderRadius: AppConstants.radiusSmall,
    );
  }
}
