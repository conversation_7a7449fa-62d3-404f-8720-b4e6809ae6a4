import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../models/bank_account_models.dart';
import '../../services/bank_account_service.dart';

class OwnerAddBankAccountController extends GetxController {
  // Form controllers
  final accountHolderNameController = TextEditingController();
  final bankNameController = TextEditingController();
  final accountNumberController = TextEditingController();
  final confirmAccountNumberController = TextEditingController();
  final ifscCodeController = TextEditingController();
  final mobileNumberController = TextEditingController();
  final upiIdController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;
  final RxBool showErrorOnScreen = false.obs;

  // Withdrawal method selection
  final Rx<WithdrawalMethod> selectedWithdrawalMethod = WithdrawalMethod.upi.obs;

  // Form validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Animation controllers
  final RxBool showBankFields = true.obs;
  final RxBool showUpiFields = true.obs;

  @override
  void onInit() {
    super.onInit();
    _setupFieldListeners();
  }

  @override
  void onClose() {
    // Dispose controllers
    accountHolderNameController.dispose();
    bankNameController.dispose();
    accountNumberController.dispose();
    confirmAccountNumberController.dispose();
    ifscCodeController.dispose();
    mobileNumberController.dispose();
    upiIdController.dispose();
    super.onClose();
  }

  /// Setup field listeners for real-time validation
  void _setupFieldListeners() {
    // Clear error message when user starts typing
    accountHolderNameController.addListener(_clearErrorMessage);
    bankNameController.addListener(_clearErrorMessage);
    accountNumberController.addListener(_clearErrorMessage);
    confirmAccountNumberController.addListener(_clearErrorMessage);
    ifscCodeController.addListener(_clearErrorMessage);
    mobileNumberController.addListener(_clearErrorMessage);
    upiIdController.addListener(_clearErrorMessage);
  }

  void _clearErrorMessage() {
    if (errorMessage.value.isNotEmpty) {
      errorMessage.value = '';
      showErrorOnScreen.value = false;
    }
  }

  /// Change withdrawal method
  void changeWithdrawalMethod(WithdrawalMethod method) {
    selectedWithdrawalMethod.value = method;
    _clearErrorMessage();
    log('OwnerAddBankAccountController: Withdrawal method changed to ${method.displayName}');
  }

  /// Validate account number confirmation
  String? validateConfirmAccountNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please confirm your account number';
    }
    if (value != accountNumberController.text) {
      return 'Account numbers do not match';
    }
    return null;
  }

  /// Auto-format IFSC code
  void formatIFSCCode(String value) {
    final formatted = value.toUpperCase();
    if (formatted != value) {
      ifscCodeController.value = ifscCodeController.value.copyWith(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    }
  }

  /// Auto-format UPI ID
  void formatUPIId(String value) {
    final formatted = value.toLowerCase();
    if (formatted != value) {
      upiIdController.value = upiIdController.value.copyWith(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    }
  }

  /// Save bank account details
  Future<void> saveBankAccount() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isSaving.value = true;
      errorMessage.value = '';
      successMessage.value = '';
      showErrorOnScreen.value = false;

      // Create bank account request
      final request = BankAccountRequest(
        accountHolderName: accountHolderNameController.text.trim(),
        bankName: bankNameController.text.trim(),
        bankAccountNumber: accountNumberController.text.trim(),
        bankIfscCode: ifscCodeController.text.trim().toUpperCase(),
        linkedMobileNumber: mobileNumberController.text.trim(),
        upiId: upiIdController.text.trim().toLowerCase(),
        preferredWithdrawalMethod: selectedWithdrawalMethod.value.value,
      );

      // Validate request
      if (!BankAccountService.validateBankAccountRequest(request)) {
        errorMessage.value = 'Please check all fields and try again';
        showErrorOnScreen.value = true;
        return;
      }

      log('OwnerAddBankAccountController: Saving bank account');
      
      final response = await BankAccountService.addBankAccount(request);
      
      if (response.success) {
        successMessage.value = 'Bank account added successfully!';
        _showSuccessDialog();
        
        // Navigate back after a short delay
        Future.delayed(const Duration(milliseconds: 2000), () {
          Get.back(result: true); // Return true to indicate successful addition
        });
      } else {
        // Show error on screen instead of snackbar
        errorMessage.value = response.message;
        showErrorOnScreen.value = true;
        log('OwnerAddBankAccountController: Error: ${response.message}');
      }
    } catch (e) {
      log('OwnerAddBankAccountController: Exception: $e');
      errorMessage.value = 'An unexpected error occurred. Please try again.';
      showErrorOnScreen.value = true;
    } finally {
      isSaving.value = false;
    }
  }

  /// Show success dialog with animation
  void _showSuccessDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success icon with animation
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 600),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: AppConstants.primaryGradient,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: AppConstants.primaryWhite,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: AppConstants.paddingLarge),
              
              Text(
                'Success!',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.primaryBlack,
                ),
              ),
              
              const SizedBox(height: AppConstants.paddingSmall),
              
              Text(
                successMessage.value,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppConstants.mediumGrey,
                ),
              ),
              
              const SizedBox(height: AppConstants.paddingLarge),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Get.back(); // Close dialog
                    Get.back(result: true); // Navigate back to profile
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryBlack,
                    foregroundColor: AppConstants.primaryWhite,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppConstants.paddingMedium,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// Clear all form fields
  void clearForm() {
    accountHolderNameController.clear();
    bankNameController.clear();
    accountNumberController.clear();
    confirmAccountNumberController.clear();
    ifscCodeController.clear();
    mobileNumberController.clear();
    upiIdController.clear();
    selectedWithdrawalMethod.value = WithdrawalMethod.upi;
    errorMessage.value = '';
    successMessage.value = '';
    showErrorOnScreen.value = false;
  }

  /// Get withdrawal method options
  List<WithdrawalMethod> get withdrawalMethods => WithdrawalMethod.values;

  /// Check if form is valid for saving
  bool get canSave {
    return accountHolderNameController.text.trim().isNotEmpty &&
           bankNameController.text.trim().isNotEmpty &&
           accountNumberController.text.trim().isNotEmpty &&
           confirmAccountNumberController.text.trim().isNotEmpty &&
           ifscCodeController.text.trim().isNotEmpty &&
           mobileNumberController.text.trim().isNotEmpty &&
           upiIdController.text.trim().isNotEmpty &&
           !isSaving.value;
  }
}
