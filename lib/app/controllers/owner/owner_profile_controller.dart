import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import '../../services/shared_preferences_service.dart';
import '../../services/user_data_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../services/owner_profile_service.dart';
import '../../services/salon_image_service.dart';
import '../../models/owner_profile_model.dart';
import '../../models/salon_image_models.dart';
import '../../views/owner/profile/owner_edit_profile_screen.dart';
import '../../services/bank_account_service.dart';
import '../../models/bank_account_models.dart';
import '../../views/owner/bank/owner_add_bank_account_screen.dart';
import '../../views/owner/earnings/owner_earnings_history_screen.dart';
import '../../constants/app_constants.dart';
import '../../routes/app_routes.dart';

class OwnerProfileController extends GetxController {
  // Owner profile data
  final RxString ownerName = 'Atif Ansari'.obs;
  final RxString salonName = 'Elite Hair Studio'.obs;
  final RxString salonDescription =
      'Welcome to our premium salon! We offer professional hair styling, cutting, coloring, and beauty treatments. Our experienced stylists use the latest techniques and high-quality products to ensure you look and feel your best.'
          .obs;
  final RxString ownerEmail = ''.obs;
  final RxString ownerPhone = ''.obs;
  final RxString salonAddress = 'Salon Address'.obs;
  final RxString workingHours = '9:00 AM - 8:00 PM'.obs;
  final RxString workingDays = 'Monday - Sunday'.obs;
  final RxString profileImageUrl = ''.obs;
  final RxString upiId = ''.obs;
  final RxString bankAccount = ''.obs;
  final RxBool hasBankAccount = false.obs;
  final RxString salonProfile = ''.obs;
  final Rx<BankAccountData?> bankAccountData = Rx<BankAccountData?>(null);

  // Gallery images
  final RxList<SaloonImage> galleryImages = <SaloonImage>[].obs;
  final RxList<File> selectedGalleryImages = <File>[].obs;

  // Helper method to ensure proper type initialization
  void initializeGalleryImages() {
    galleryImages.clear();
    selectedGalleryImages.clear();
    log(
      'OwnerProfileController: Gallery images initialized with type: ${galleryImages.runtimeType}',
    );
  }

  // Getter to ensure proper type
  RxList<SaloonImage> get salonImages => galleryImages;

  // Earnings data
  final RxDouble totalCollected = 0.0.obs;
  final RxDouble withdrawalAmount = 0.0.obs;
  final RxDouble availableBalance = 0.0.obs;

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isLoadingProfile = false.obs;
  final RxBool isUploadingImages = false.obs;

  // Error handling
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  @override
  void onInit() {
    super.onInit();
    initializeGalleryImages();
    loadOwnerProfile();
  }

  @override
  void onReady() {
    super.onReady();
    // Listen for profile updates from edit screen
    ever(isLoadingProfile, (loading) {
      if (!loading) {
        log('OwnerProfileController: Profile loading completed');
      }
    });
  }

  // Refresh profile data (for pull-to-refresh)
  Future<void> refreshProfileData() async {
    log('OwnerProfileController: Refreshing profile data');
    await loadOwnerProfile();
  }

  // Load owner profile data
  Future<void> loadOwnerProfile() async {
    try {
      isLoadingProfile.value = true;
      hasError.value = false;
      errorMessage.value = '';

      // Try to load from new owner profile API first
      final ownerProfileResponse = await OwnerProfileService.getOwnerProfile();

      if (ownerProfileResponse.success && ownerProfileResponse.data != null) {
        final profileData = ownerProfileResponse.data!;

        // Update profile data with new API response
        ownerName.value = profileData.fullName.isNotEmpty
            ? profileData.fullName
            : 'Owner Name';
        salonProfile.value = profileData.salonProfile.isNotEmpty
            ? profileData.salonProfile
            : "";
        salonName.value = profileData.saloonName.isNotEmpty
            ? profileData.saloonName
            : 'Salon Name';
        salonDescription.value = profileData.salonDescription.isNotEmpty
            ? profileData.salonDescription
            : 'Welcome to our premium salon! We offer professional hair styling, cutting, coloring, and beauty treatments.';
        ownerEmail.value = profileData.email;
        ownerPhone.value = profileData.phone;
        salonAddress.value = profileData.fullAddress;
        workingHours.value = profileData.workingHours;
        workingDays.value = 'Closed on ${profileData.offDays}';

        // Update gallery images
        galleryImages.clear();
        galleryImages.addAll(profileData.saloonImages);

        log('Owner profile loaded from new API successfully');
        log(
          'OwnerProfileController: Profile data - Name: ${ownerName.value}, Salon: ${salonName.value}',
        );
      } else {
        // Fallback to old API
        final userProfile = await UserDataService.getUserProfile();

        if (userProfile != null) {
          ownerName.value = userProfile.fullName.isNotEmpty
              ? userProfile.fullName
              : 'Owner Name';
          salonName.value = userProfile.salonName ?? 'Salon Name';
          salonDescription.value =
              userProfile.salonDescription ?? 'Salon Description';
          ownerEmail.value = userProfile.salonEmail ?? '';
          ownerPhone.value = userProfile.salonPhone ?? '';
          salonAddress.value = userProfile.fullAddress;
          profileImageUrl.value = userProfile.profileImage ?? '';

          log('Owner profile loaded from fallback API successfully');
        } else {
          // Final fallback to SharedPreferences
          await _loadFromSharedPreferences();
        }
      }

      // Load bank account data
      await loadBankAccountData();
    } catch (e) {
      log('Error loading owner profile: $e');
      hasError.value = true;
      errorMessage.value = 'Failed to load profile data';
      await _loadFromSharedPreferences();
    } finally {
      isLoadingProfile.value = false;
    }
  }

  // Load fallback data from SharedPreferences
  Future<void> _loadFromSharedPreferences() async {
    try {
      final cachedName = await SharedPreferencesService.getUserName();
      final cachedEmail = await SharedPreferencesService.getEmail();

      if (cachedName != null) ownerName.value = cachedName;
      if (cachedEmail != null) ownerEmail.value = cachedEmail;

      log('Loaded fallback data from SharedPreferences');
    } catch (e) {
      log('Error loading from SharedPreferences: $e');
    }
  }

  // Show image source selection bottom sheet
  Future<void> pickGalleryImages() async {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.radiusMedium),
            topRight: Radius.circular(AppConstants.radiusMedium),
          ),
        ),
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            Text(
              'Add Salon Photos',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Camera Option
            _buildImageSourceOption(
              icon: Icons.camera_alt,
              title: 'Take Photos',
              subtitle: 'Use camera to take new photos',
              onTap: () {
                Get.back();
                _pickImagesFromCamera();
              },
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Gallery Option
            _buildImageSourceOption(
              icon: Icons.photo_library,
              title: 'Choose from Gallery',
              subtitle: 'Select multiple photos from gallery',
              onTap: () {
                Get.back();
                _pickImagesFromGallery();
              },
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Cancel Button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Get.back(),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.mediumGrey,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  // Build image source option
  Widget _buildImageSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.lightGrey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(
            color: AppConstants.primaryGrey.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: AppConstants.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppConstants.primaryWhite, size: 24),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppConstants.mediumGrey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // Pick images from camera
  Future<void> _pickImagesFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        final remainingSlots = 5 - selectedGalleryImages.length;
        if (remainingSlots > 0) {
          selectedGalleryImages.add(File(image.path));

          // Automatically upload the captured image
          log('OwnerProfileController: Auto-uploading captured image');
          log(
            'OwnerProfileController: Current gallery images count: ${galleryImages.length}',
          );
          log(
            'OwnerProfileController: Selected images count: ${selectedGalleryImages.length}',
          );

          await uploadGalleryImages();
        } else {
          Get.snackbar(
            'Image Limit',
            'Maximum 5 images allowed.',
            backgroundColor: AppConstants.primaryBlack,
            colorText: AppConstants.primaryWhite,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        }
      }
    } catch (e) {
      log('Error taking photo: $e');
      Get.snackbar(
        'Error',
        'Failed to take photo. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Pick multiple images from gallery
  Future<void> _pickImagesFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (images.isNotEmpty) {
        // Limit to 5 images total
        final remainingSlots = 5 - selectedGalleryImages.length;
        final imagesToAdd = images.take(remainingSlots).toList();

        for (final image in imagesToAdd) {
          selectedGalleryImages.add(File(image.path));
        }

        if (images.length > remainingSlots) {
          Get.snackbar(
            'Image Limit',
            'Maximum 5 images allowed. $remainingSlots images added.',
            backgroundColor: AppConstants.primaryBlack,
            colorText: AppConstants.primaryWhite,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        }

        // Automatically upload the selected images
        log(
          'OwnerProfileController: Auto-uploading ${selectedGalleryImages.length} selected images',
        );
        log(
          'OwnerProfileController: Current gallery images count: ${galleryImages.length}',
        );
        log(
          'OwnerProfileController: Selected images count: ${selectedGalleryImages.length}',
        );

        // Trigger upload
        await uploadGalleryImages();
      }
    } catch (e) {
      log('Error picking gallery images: $e');
      Get.snackbar(
        'Error',
        'Failed to pick images. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Remove gallery image (for local images)
  void removeGalleryImage(int index) {
    if (index >= 0 && index < selectedGalleryImages.length) {
      selectedGalleryImages.removeAt(index);
    }
  }

  // Manual upload trigger for testing
  Future<void> manualUploadTrigger() async {
    log('OwnerProfileController: Manual upload trigger called');
    log(
      'OwnerProfileController: Selected images count: ${selectedGalleryImages.length}',
    );
    log(
      'OwnerProfileController: Gallery images count: ${galleryImages.length}',
    );
    log('OwnerProfileController: Is uploading: ${isUploadingImages.value}');

    if (selectedGalleryImages.isNotEmpty) {
      await uploadGalleryImages();
    } else {
      Get.snackbar(
        'No Images',
        'Please select images first',
        backgroundColor: AppConstants.primaryBlack,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Delete network image with confirmation
  void deleteNetworkImage(int index) {
    log(
      'OwnerProfileController: Delete network image requested for index: $index',
    );
    log(
      'OwnerProfileController: Total gallery images: ${galleryImages.length}',
    );

    if (index >= 0 && index < galleryImages.length) {
      final salonImage = galleryImages[index];
      log(
        'OwnerProfileController: Showing delete confirmation for image: ${salonImage.imageUrl}',
      );
      _showDeleteConfirmationDialog(salonImage, index);
    } else {
      log('OwnerProfileController: Invalid index for delete: $index');
    }
  }

  // Show delete confirmation dialog
  void _showDeleteConfirmationDialog(SaloonImage salonImage, int index) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        title: Text(
          'Delete Image',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this salon image? This action cannot be undone.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.darkGrey,
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppConstants.mediumGrey,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _deleteImageFromBackend(salonImage, index);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: AppConstants.primaryWhite,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
            ),
            child: Text(
              'Delete',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  // Delete image from backend
  Future<void> _deleteImageFromBackend(
    SaloonImage salonImage,
    int index,
  ) async {
    try {
      log('OwnerProfileController: Deleting image: ${salonImage.imageUrl}');
      log('OwnerProfileController: Image ID: ${salonImage.id}');

      final response = await SalonImageService.deleteSalonImage(salonImage.id);

      if (response.success) {
        // Remove from local list
        galleryImages.removeAt(index);

        log('OwnerProfileController: Image deleted successfully');

        Get.snackbar(
          'Success',
          'Image deleted successfully',
          backgroundColor: Colors.green,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } else {
        log('OwnerProfileController: Delete failed: ${response.message}');

        Get.snackbar(
          'Delete Failed',
          response.message,
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      log('OwnerProfileController: Error deleting image: $e');

      Get.snackbar(
        'Error',
        'Failed to delete image. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Upload gallery images to Firebase and then to backend
  Future<void> uploadGalleryImages() async {
    if (selectedGalleryImages.isEmpty) return;

    try {
      isUploadingImages.value = true;
      log(
        'OwnerProfileController: Starting upload of ${selectedGalleryImages.length} images',
      );

      // Show upload progress snackbar
      Get.snackbar(
        'Uploading Images',
        'Uploading ${selectedGalleryImages.length} images to salon gallery...',
        backgroundColor: AppConstants.primaryBlack,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
        showProgressIndicator: true,
      );

      // Step 1: Upload images to Firebase Storage
      final firebaseService = FirebaseStorageService();
      final firebaseUrls = await firebaseService.uploadSalonImages(
        selectedGalleryImages,
        onProgress: (current, total, progress) {
          log(
            'OwnerProfileController: Uploading image $current/$total (${(progress * 100).toInt()}%)',
          );
        },
      );

      if (firebaseUrls.isNotEmpty) {
        log(
          'OwnerProfileController: Successfully uploaded ${firebaseUrls.length} images to Firebase',
        );

        // Step 2: Send Firebase URLs to backend API
        final response = await SalonImageService.uploadSalonImages(
          firebaseUrls,
        );

        if (response.success && response.data != null) {
          // Convert SalonImageData to SaloonImage objects
          final newSalonImages = response.data!.map((imageData) {
            return SaloonImage(
              id: imageData.id,
              saloonId: '', // Will be set by backend
              imageUrl: imageData.imageUrl,
              altText: imageData.description,
              createdAt: imageData.createdAt.toIso8601String(),
              updatedAt: imageData.updatedAt.toIso8601String(),
            );
          }).toList();

          // Add uploaded images to gallery
          galleryImages.addAll(newSalonImages);
          selectedGalleryImages.clear();

          log(
            'OwnerProfileController: Successfully saved ${newSalonImages.length} images to backend',
          );

          Get.snackbar(
            'Success',
            'Gallery images uploaded successfully!',
            backgroundColor: Colors.green,
            colorText: AppConstants.primaryWhite,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        } else {
          log(
            'OwnerProfileController: Backend upload failed: ${response.message}',
          );

          Get.snackbar(
            'Upload Failed',
            response.message,
            backgroundColor: Colors.orange,
            colorText: AppConstants.primaryWhite,
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        log('OwnerProfileController: No images uploaded to Firebase');

        Get.snackbar(
          'Upload Failed',
          'Failed to upload images to storage. Please try again.',
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      log('OwnerProfileController: Error uploading gallery images: $e');
      Get.snackbar(
        'Error',
        'Failed to upload images. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isUploadingImages.value = false;
    }
  }

  // Format currency
  String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  // Navigate to edit profile
  Future<void> navigateToEditProfile() async {
    final result = await Get.to(
      () => const OwnerEditProfileScreen(),
      transition: Transition.rightToLeft,
      duration: const Duration(milliseconds: 300),
    );

    // If profile was updated successfully, refresh the profile data
    if (result == true) {
      await loadOwnerProfile();
    }
  }

  // Navigate to payout history
  void navigateToPayoutHistory() {
    Get.snackbar(
      'Coming Soon',
      'Payout history feature will be available soon',
      backgroundColor: AppConstants.primaryBlack,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
    );
  }

  // Navigate to bank details
  Future<void> navigateToBankDetails() async {
    final result = await Get.to(
      () => const OwnerAddBankAccountScreen(),
      transition: Transition.rightToLeft,
      duration: const Duration(milliseconds: 300),
    );

    // If bank account was added successfully, refresh the data
    if (result == true) {
      await loadBankAccountData();
    }
  }

  // Load bank account data
  Future<void> loadBankAccountData() async {
    try {
      log('OwnerProfileController: Loading bank account data');

      final response = await BankAccountService.getBankAccountDetails();

      if (response.success && response.data != null) {
        bankAccountData.value = response.data;

        // Check if user has any bank details (UPI or bank account)
        final hasUpi = response.data!.hasUpiDetails;
        final hasBankDetails = response.data!.hasBankDetails;

        hasBankAccount.value = hasUpi || hasBankDetails;
        upiId.value = response.data!.upiId ?? '';
        bankAccount.value = response.data!.displayName;

        log('OwnerProfileController: Bank account data loaded successfully');
        log(
          'OwnerProfileController: Has UPI: $hasUpi, Has Bank Details: $hasBankDetails',
        );
      } else {
        hasBankAccount.value = false;
        upiId.value = '';
        bankAccount.value = '';
        bankAccountData.value = null;

        log('OwnerProfileController: No bank account found');
      }
    } catch (e) {
      log('OwnerProfileController: Error loading bank account data: $e');
      hasBankAccount.value = false;
      upiId.value = '';
      bankAccount.value = '';
      bankAccountData.value = null;
    }
  }

  // Show logout confirmation dialog
  void showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: Text(
          'Logout',
          style: Get.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to logout?',
          style: Get.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Cancel',
              style: Get.textTheme.labelLarge?.copyWith(
                color: AppConstants.mediumGrey,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              logout();
            },
            child: Text(
              'Logout',
              style: Get.textTheme.labelLarge?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Logout
  Future<void> logout() async {
    try {
      isLoading.value = true;

      log('OwnerProfileController: Starting logout process');

      // Clear all user data from SharedPreferences
      final success = await SharedPreferencesService.clearUserData();

      if (success) {
        log('OwnerProfileController: User data cleared successfully');

        // Show success message
        Get.snackbar(
          'Logged Out',
          'You have been successfully logged out',
          backgroundColor: AppConstants.primaryBlack,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.logout, color: AppConstants.primaryWhite),
        );

        // Navigate to social login page and clear navigation stack
        Get.offAllNamed(AppRoutes.socialLogin);

        log('OwnerProfileController: Navigated to social login page');
      } else {
        throw Exception('Failed to clear user data');
      }
    } catch (e) {
      log('OwnerProfileController: Logout error: $e');
      Get.snackbar(
        'Error',
        'Failed to logout. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: AppConstants.primaryWhite),
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh profile data
  Future<void> refreshProfile() async {
    await loadOwnerProfile();
  }

  // Navigate to earnings history
  void navigateToEarningsHistory() {
    Get.to(
      () => const OwnerEarningsHistoryScreen(),
      transition: Transition.rightToLeft,
      duration: const Duration(milliseconds: 300),
    );
  }
}
