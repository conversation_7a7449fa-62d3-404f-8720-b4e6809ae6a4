import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/search_salon_models.dart';
import '../../services/search_service.dart';
import '../../services/shared_preferences_service.dart';

class SearchController extends GetxController {
  // Text controller for search input
  final TextEditingController searchTextController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isSearching = false.obs;
  final RxString searchQuery = ''.obs;
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  // Search results
  final RxList<SearchSalon> searchResults = <SearchSalon>[].obs;
  final RxList<SearchSalon> allResults = <SearchSalon>[].obs;

  // Filter options
  final Rx<SearchFilterType> selectedFilter = SearchFilterType.all.obs;
  final RxBool showFilters = false.obs;

  // Search history
  final RxList<SearchHistoryItem> searchHistory = <SearchHistoryItem>[].obs;
  final RxList<String> recentSearches = <String>[].obs;

  // UI state
  final RxBool showSearchHistory = false.obs;
  final RxBool isSearchFocused = false.obs;

  // Focus node for search field
  final FocusNode searchFocusNode = FocusNode();

  // Filter options list
  final List<SearchFilterType> filterOptions = [
    SearchFilterType.all,
    SearchFilterType.salon,
    SearchFilterType.service,
    SearchFilterType.barber,
  ];

  @override
  void onInit() {
    super.onInit();

    // Listen to search text changes
    searchTextController.addListener(() {
      searchQuery.value = searchTextController.text;
      if (searchQuery.value.isEmpty) {
        clearSearch();
      } else {
        // Debounce search to avoid too many API calls
        _debounceSearch();
      }
    });

    // Listen to focus changes
    searchFocusNode.addListener(() {
      isSearchFocused.value = searchFocusNode.hasFocus;
      if (searchFocusNode.hasFocus && searchQuery.value.isEmpty) {
        showSearchHistory.value = true;
      } else {
        showSearchHistory.value = false;
      }
    });

    // Load search history
    _loadSearchHistory();
  }

  @override
  void onClose() {
    searchTextController.dispose();
    searchFocusNode.dispose();
    _debounceTimer?.cancel();
    super.onClose();
  }

  // Debounce timer for search
  Timer? _debounceTimer;

  void _debounceSearch() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (searchQuery.value.isNotEmpty) {
        performSearch();
      }
    });
  }

  /// Perform search
  Future<void> performSearch() async {
    if (searchQuery.value.trim().isEmpty) {
      clearSearch();
      return;
    }

    try {
      isLoading.value = true;
      isSearching.value = true;
      hasError.value = false;
      errorMessage.value = '';
      showSearchHistory.value = false;

      // Determine search type based on selected filter
      String searchType = selectedFilter.value.apiValue;

      log('🎯 Controller Debug:');
      log('Search Query: "${searchQuery.value.trim()}"');
      log('Search Type: $searchType');
      log('Selected Filter: ${selectedFilter.value.displayName}');

      final response = await SearchService.searchByType(
        searchQuery: searchQuery.value.trim(),
        searchType: searchType,
      );

      log('📊 Response Debug:');
      log('Success: ${response.success}');
      log('Message: ${response.message}');
      log('Data Count: ${response.data.length}');

      if (response.success) {
        allResults.assignAll(response.data);
        applyFilters();

        log('✅ Search successful: ${searchResults.length} results');

        // Add to search history
        _addToSearchHistory(searchQuery.value.trim());

        // Show success message if needed
        if (response.data.isEmpty) {
          errorMessage.value = 'No results found for "${searchQuery.value}"';
        }
      } else {
        hasError.value = true;
        errorMessage.value = response.message;
        searchResults.clear();
        log('❌ Search failed: ${response.message}');
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Search failed. Please try again.';
      searchResults.clear();
      log('❌ Search error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply filters to results
  void applyFilters() {
    if (selectedFilter.value == SearchFilterType.all) {
      searchResults.assignAll(allResults);
    } else {
      // For now, since API returns based on type, just show all results
      // In future, you can add more filtering logic here
      searchResults.assignAll(allResults);
    }
  }

  /// Change filter
  void changeFilter(SearchFilterType filter) {
    selectedFilter.value = filter;
    if (searchQuery.value.isNotEmpty) {
      performSearch(); // Re-search with new filter
    }
  }

  /// Toggle filters visibility
  void toggleFilters() {
    showFilters.value = !showFilters.value;
  }

  /// Clear search
  void clearSearch() {
    searchTextController.clear();
    searchQuery.value = '';
    searchResults.clear();
    allResults.clear();
    isSearching.value = false;
    hasError.value = false;
    errorMessage.value = '';
    showSearchHistory.value = isSearchFocused.value;
  }

  /// Refresh search
  Future<void> refreshSearch() async {
    if (searchQuery.value.isNotEmpty) {
      await performSearch();
    }
  }

  /// Search by specific type
  Future<void> searchByType(SearchFilterType type) async {
    selectedFilter.value = type;
    if (searchQuery.value.isNotEmpty) {
      await performSearch();
    }
  }

  /// Handle search suggestion tap
  void onSuggestionTap(String suggestion) {
    searchTextController.text = suggestion;
    searchQuery.value = suggestion;
    searchFocusNode.unfocus();
    performSearch();
  }

  /// Handle search history item tap
  void onHistoryItemTap(SearchHistoryItem item) {
    searchTextController.text = item.query;
    searchQuery.value = item.query;
    selectedFilter.value = item.filterType;
    searchFocusNode.unfocus();
    performSearch();
  }

  /// Clear search history
  void clearSearchHistory() {
    searchHistory.clear();
    recentSearches.clear();
    _saveSearchHistory();
  }

  /// Remove specific history item
  void removeHistoryItem(SearchHistoryItem item) {
    searchHistory.remove(item);
    _saveSearchHistory();
  }

  /// Add to search history
  void _addToSearchHistory(String query) {
    // Remove if already exists
    searchHistory.removeWhere((item) => item.query == query);

    // Add to beginning
    searchHistory.insert(
      0,
      SearchHistoryItem(
        query: query,
        timestamp: DateTime.now(),
        filterType: selectedFilter.value,
      ),
    );

    // Keep only last 10 items
    if (searchHistory.length > 10) {
      searchHistory.removeRange(10, searchHistory.length);
    }

    _saveSearchHistory();
  }

  /// Load search history from storage
  Future<void> _loadSearchHistory() async {
    try {
      final historyJson = await SharedPreferencesService.getString(
        'search_history',
      );
      if (historyJson != null) {
        final List<dynamic> historyList = jsonDecode(historyJson);
        searchHistory.assignAll(
          historyList.map((item) => SearchHistoryItem.fromJson(item)).toList(),
        );
      }
    } catch (e) {
      log('Error loading search history: $e');
    }
  }

  /// Save search history to storage
  Future<void> _saveSearchHistory() async {
    try {
      final historyJson = jsonEncode(
        searchHistory.map((item) => item.toJson()).toList(),
      );
      await SharedPreferencesService.setString('search_history', historyJson);
    } catch (e) {
      log('Error saving search history: $e');
    }
  }

  /// Get display text for empty state
  String get emptyStateText {
    if (hasError.value) {
      return errorMessage.value;
    } else if (isSearching.value && searchResults.isEmpty) {
      return 'No results found for "${searchQuery.value}"';
    } else {
      return 'Start typing to search for salons, services, or barbers';
    }
  }

  /// Check if should show results
  bool get shouldShowResults {
    return isSearching.value && !isLoading.value && !showSearchHistory.value;
  }

  /// Check if should show empty state
  bool get shouldShowEmptyState {
    return !isLoading.value &&
        !showSearchHistory.value &&
        ((!isSearching.value && searchQuery.value.isEmpty) ||
            (isSearching.value && searchResults.isEmpty));
  }

  /// Check if should show search history
  bool get shouldShowSearchHistory {
    return showSearchHistory.value && searchHistory.isNotEmpty;
  }

  /// Get popular suggestions
  List<SearchSuggestion> get popularSuggestions {
    return SearchService.getPopularSuggestions();
  }

  /// Get trending searches
  List<String> get trendingSearches {
    return SearchService.getTrendingSearches();
  }
}
