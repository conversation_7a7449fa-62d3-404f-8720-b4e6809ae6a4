import 'dart:developer';
import 'package:get/get.dart';
import '../../models/dashboard_models.dart';
import '../../services/dashboard_service.dart';

/// Controller for managing user dashboard data and state
class DashboardController extends GetxController {
  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxBool hasError = false.obs;

  // Data observables
  final Rx<DashboardData?> dashboardData = Rx<DashboardData?>(null);
  final RxList<SalonModel> nearestSalons = <SalonModel>[].obs;
  final RxList<SalonModel> popularSalons = <SalonModel>[].obs;
  final Rx<HomeSalonModel?> homeSaloon = Rx<HomeSalonModel?>(null);

  // Error handling
  final RxString errorMessage = ''.obs;
  final Rx<DashboardServiceException?> lastError =
      Rx<DashboardServiceException?>(null);

  // UI state
  final RxBool showHomeSalon = false.obs;
  final RxBool showNearestSalons = false.obs;
  final RxBool showPopularSalons = false.obs;

  @override
  void onInit() {
    super.onInit();
    log('DashboardController: Initializing...');
    loadDashboardData();
  }

  /// Test method to manually trigger data loading with detailed logging
  Future<void> testLoadData() async {
    log('DashboardController: TEST - Starting manual data load...');
    try {
      isLoading.value = true;
      hasError.value = false;

      final response = await DashboardService.getUserDashboard();
      log('DashboardController: TEST - Response received: ${response.success}');
      log('DashboardController: TEST - Response message: ${response.message}');

      if (response.data != null) {
        log('DashboardController: TEST - Data is not null');
        log(
          'DashboardController: TEST - Nearest salons count: ${response.data!.nearestSalons.length}',
        );
        log(
          'DashboardController: TEST - Popular salons count: ${response.data!.popularSalons.length}',
        );
        log(
          'DashboardController: TEST - Home salon: ${response.data!.homeSaloon?.name ?? 'null'}',
        );

        // Log individual salon details for debugging
        for (int i = 0; i < response.data!.nearestSalons.length; i++) {
          final salon = response.data!.nearestSalons[i];
          log(
            'DashboardController: TEST - Nearest salon $i: ${salon.name}, distance: ${salon.distance}, distanceKm: ${salon.distanceInKm}',
          );
        }

        for (int i = 0; i < response.data!.popularSalons.length; i++) {
          final salon = response.data!.popularSalons[i];
          log(
            'DashboardController: TEST - Popular salon $i: ${salon.name}, distance: ${salon.distance}, distanceKm: ${salon.distanceInKm}',
          );
        }

        _updateDashboardData(response.data!);
      } else {
        log('DashboardController: TEST - Data is null');
      }
    } catch (e) {
      log('DashboardController: TEST - Error: $e');
      _handleError(
        DashboardServiceException('Test load failed: $e', error: e.toString()),
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onReady() {
    super.onReady();
    log('DashboardController: Ready');
  }

  /// Loads dashboard data from API with optional location parameters
  Future<void> loadDashboardData({
    bool showLoading = true,
    double? latitude,
    double? longitude,
  }) async {
    try {
      if (showLoading) {
        isLoading.value = true;
      }
      hasError.value = false;
      errorMessage.value = '';
      lastError.value = null;

      log('DashboardController: Loading dashboard data...');

      final response = await DashboardService.getUserDashboard(
        latitude: latitude,
        longitude: longitude,
      );

      if (response.success && response.data != null) {
        _updateDashboardData(response.data!);
        log('DashboardController: Dashboard data loaded successfully');
      } else {
        _handleError(
          DashboardServiceException(
            response.message.isNotEmpty
                ? response.message
                : 'Failed to load dashboard data',
          ),
        );
      }
    } on DashboardServiceException catch (e) {
      log('DashboardController: Service exception: $e');
      _handleError(e);
    } catch (e) {
      log('DashboardController: Unexpected error: $e');
      _handleError(
        DashboardServiceException(
          'An unexpected error occurred while loading dashboard data',
          error: e.toString(),
        ),
      );
    } finally {
      if (showLoading) {
        isLoading.value = false;
      }
    }
  }

  /// Refreshes dashboard data with optional location parameters
  Future<void> refreshDashboard({double? latitude, double? longitude}) async {
    try {
      isRefreshing.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('DashboardController: Refreshing dashboard data...');

      final response = await DashboardService.refreshDashboard(
        forceRefresh: true,
        latitude: latitude,
        longitude: longitude,
      );

      if (response.success && response.data != null) {
        _updateDashboardData(response.data!);
        log('DashboardController: Dashboard data refreshed successfully');

        // Show success message
        Get.snackbar(
          'Success',
          'Dashboard updated successfully',
          duration: const Duration(seconds: 2),
        );
      } else {
        _handleError(
          DashboardServiceException(
            response.message.isNotEmpty
                ? response.message
                : 'Failed to refresh dashboard data',
          ),
        );
      }
    } on DashboardServiceException catch (e) {
      log('DashboardController: Refresh service exception: $e');
      _handleError(e);
    } catch (e) {
      log('DashboardController: Refresh unexpected error: $e');
      _handleError(
        DashboardServiceException(
          'An unexpected error occurred while refreshing dashboard data',
          error: e.toString(),
        ),
      );
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Updates dashboard data and UI state
  void _updateDashboardData(DashboardData data) {
    dashboardData.value = data;

    // Update individual lists
    nearestSalons.value = data.nearestSalons;
    popularSalons.value = data.popularSalons;
    homeSaloon.value = data.homeSaloon;

    // Update UI visibility flags
    showNearestSalons.value = data.nearestSalons.isNotEmpty;
    showPopularSalons.value = data.popularSalons.isNotEmpty;
    showHomeSalon.value = data.homeSaloon != null;

    log(
      'DashboardController: Updated data - Nearest: ${data.nearestSalons.length}, Popular: ${data.popularSalons.length}, Home: ${data.homeSaloon != null}',
    );
    log(
      'DashboardController: Visibility flags - showNearestSalons: ${showNearestSalons.value}, showPopularSalons: ${showPopularSalons.value}, showHomeSalon: ${showHomeSalon.value}',
    );

    // Debug: Print salon names
    if (data.nearestSalons.isNotEmpty) {
      log(
        'DashboardController: Nearest salons: ${data.nearestSalons.map((s) => s.name).join(', ')}',
      );
    }
    if (data.popularSalons.isNotEmpty) {
      log(
        'DashboardController: Popular salons: ${data.popularSalons.map((s) => s.name).join(', ')}',
      );
    }
    if (data.homeSaloon != null) {
      log('DashboardController: Home salon: ${data.homeSaloon!.name}');
    }
  }

  /// Handles errors and updates error state
  void _handleError(DashboardServiceException exception) {
    hasError.value = true;
    errorMessage.value = exception.userFriendlyMessage;
    lastError.value = exception;

    log('DashboardController: Error handled - ${exception.message}');

    // Show error snackbar
    Get.snackbar(
      'Error',
      exception.userFriendlyMessage,
      duration: const Duration(seconds: 4),
    );
  }

  /// Retries loading dashboard data
  Future<void> retryLoading() async {
    log('DashboardController: Retrying dashboard data load...');
    await loadDashboardData();
  }

  /// Clears all data and resets state
  void clearData() {
    dashboardData.value = null;
    nearestSalons.clear();
    popularSalons.clear();
    homeSaloon.value = null;

    showHomeSalon.value = false;
    showNearestSalons.value = false;
    showPopularSalons.value = false;

    hasError.value = false;
    errorMessage.value = '';
    lastError.value = null;

    log('DashboardController: Data cleared');
  }

  /// Gets salon by ID from all available salons
  SalonModel? getSalonById(String salonId) {
    if (salonId.isEmpty) return null;

    // Check home salon first
    if (homeSaloon.value?.id == salonId) {
      return homeSaloon.value;
    }

    // Check nearest salons
    for (final salon in nearestSalons) {
      if (salon.id == salonId) return salon;
    }

    // Check popular salons
    for (final salon in popularSalons) {
      if (salon.id == salonId) return salon;
    }

    return null;
  }

  /// Gets all unique salons (avoiding duplicates)
  List<SalonModel> getAllUniqueSalons() {
    final Set<String> seenIds = <String>{};
    final List<SalonModel> uniqueSalons = <SalonModel>[];

    // Add home salon first if available
    if (homeSaloon.value != null) {
      uniqueSalons.add(homeSaloon.value!);
      seenIds.add(homeSaloon.value!.id);
    }

    // Add nearest salons
    for (final salon in nearestSalons) {
      if (!seenIds.contains(salon.id)) {
        uniqueSalons.add(salon);
        seenIds.add(salon.id);
      }
    }

    // Add popular salons
    for (final salon in popularSalons) {
      if (!seenIds.contains(salon.id)) {
        uniqueSalons.add(salon);
        seenIds.add(salon.id);
      }
    }

    return uniqueSalons;
  }

  /// Checks if data is available
  bool get hasData => dashboardData.value != null;

  /// Checks if any salons are available
  bool get hasSalons =>
      nearestSalons.isNotEmpty ||
      popularSalons.isNotEmpty ||
      homeSaloon.value != null;

  /// Gets total salon count
  int get totalSalonCount => getAllUniqueSalons().length;

  /// Checks if error is recoverable
  bool get canRetry => lastError.value?.isRecoverable ?? true;

  @override
  void onClose() {
    log('DashboardController: Disposing...');
    super.onClose();
  }
}
