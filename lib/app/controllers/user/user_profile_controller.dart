import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../constants/app_constants.dart';
import '../../models/user_profile_models.dart';
import '../../services/user_profile_api_service.dart';
import '../../services/shared_preferences_service.dart';

class UserProfileController extends GetxController {
  final UserProfileApiService _apiService = UserProfileApiService();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isUpdating = false.obs;
  final RxBool isUploadingImage = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  // User profile data
  final Rx<UserProfile?> userProfile = Rx<UserProfile?>(null);
  final RxString appVersion = ''.obs;

  // UI state
  final RxBool showLogoutDialog = false.obs;

  // Preference states
  final RxBool pushNotificationsEnabled = true.obs;
  final RxBool faceIdEnabled = true.obs;

  @override
  void onInit() {
    super.onInit();
    loadUserProfile();
    loadAppVersion();
  }

  /// Load user profile from API
  Future<void> loadUserProfile() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('UserProfileController: Loading user profile');

      final response = await _apiService.getUserProfile();

      if (response.success && response.data != null) {
        userProfile.value = response.data;
        log('UserProfileController: Profile loaded successfully');
      } else {
        hasError.value = true;
        errorMessage.value = response.message;
        log(
          'UserProfileController: Failed to load profile: ${response.message}',
        );
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to load profile';
      log('UserProfileController: Error loading profile: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(UserProfile updatedProfile) async {
    try {
      isUpdating.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('UserProfileController: Updating user profile');

      final response = await _apiService.updateUserProfile(updatedProfile);

      if (response.success && response.data != null) {
        userProfile.value = response.data;

        Get.snackbar(
          'Success',
          'Profile updated successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log('UserProfileController: Profile updated successfully');
      } else {
        hasError.value = true;
        errorMessage.value = response.message;

        Get.snackbar(
          'Error',
          response.message,
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log(
          'UserProfileController: Failed to update profile: ${response.message}',
        );
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to update profile';

      Get.snackbar(
        'Error',
        'Failed to update profile',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserProfileController: Error updating profile: $e');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Upload profile image
  Future<void> uploadProfileImage(File imageFile) async {
    try {
      isUploadingImage.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('UserProfileController: Uploading profile image');

      final response = await _apiService.uploadProfileImage(imageFile);

      if (response.success && response.data != null) {
        userProfile.value = response.data;

        Get.snackbar(
          'Success',
          'Profile image updated successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log('UserProfileController: Profile image uploaded successfully');
      } else {
        Get.snackbar(
          'Error',
          response.message,
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log(
          'UserProfileController: Failed to upload image: ${response.message}',
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to upload image',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserProfileController: Error uploading image: $e');
    } finally {
      isUploadingImage.value = false;
    }
  }

  /// Load app version
  Future<void> loadAppVersion() async {
    try {
      // For now, use a static version. In production, you can use package_info_plus
      appVersion.value = 'v1.0.0 (1)';
    } catch (e) {
      appVersion.value = 'v1.0.0';
      log('UserProfileController: Error loading app version: $e');
    }
  }

  /// Show logout confirmation dialog
  void showLogoutConfirmation() {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        title: Text(
          'Logout',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
        ),
        content: Text(
          'Are you sure you want to logout? You will need to login again to access your account.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppConstants.mediumGrey,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog first
              logout(); // Perform logout
            },
            child: Text(
              'Logout',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Hide logout confirmation dialog
  void hideLogoutConfirmation() {
    showLogoutDialog.value = false;
  }

  /// Perform logout
  Future<void> logout() async {
    try {
      log('UserProfileController: Logging out user');

      // Clear all stored data
      await SharedPreferencesService.clearUserData();

      // Navigate to login page with replacement
      Get.offAllNamed('/login');

      Get.snackbar(
        'Logged Out',
        'You have been logged out successfully',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserProfileController: User logged out successfully');
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to logout. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserProfileController: Error during logout: $e');
    }
  }

  /// Refresh profile data
  Future<void> refreshProfile() async {
    await loadUserProfile();
  }

  /// Clear error state
  void clearError() {
    hasError.value = false;
    errorMessage.value = '';
  }

  /// Navigate to edit profile
  void navigateToEditProfile() {
    // For now, show a coming soon message
    // In the future, this can navigate to a dedicated edit screen
    Get.snackbar(
      'Coming Soon',
      'Profile editing will be available soon',
      backgroundColor: AppConstants.primaryBlack,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
    );
  }

  /// Navigate to privacy policy
  void navigateToPrivacyPolicy() {
    // TODO: Implement privacy policy navigation
    Get.snackbar(
      'Coming Soon',
      'Privacy Policy page will be available soon',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
    );
  }

  /// Navigate to terms and conditions
  void navigateToTermsAndConditions() {
    // TODO: Implement terms and conditions navigation
    Get.snackbar(
      'Coming Soon',
      'Terms & Conditions page will be available soon',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
    );
  }

  /// Navigate to security notes
  void navigateToSecurityNotes() {
    // TODO: Implement security notes navigation
    Get.snackbar(
      'Coming Soon',
      'Security Notes page will be available soon',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
    );
  }

  /// Get user display name
  String get displayName {
    return userProfile.value?.displayName ?? 'User';
  }

  /// Get user email
  String get userEmail {
    return userProfile.value?.email ?? 'No email';
  }

  /// Get user initials
  String get userInitials {
    return userProfile.value?.initials ?? 'U';
  }

  /// Check if user has profile image
  bool get hasProfileImage {
    return userProfile.value?.hasProfileImage ?? false;
  }

  /// Get profile image URL
  String? get profileImageUrl {
    return userProfile.value?.profileImage;
  }

  /// Toggle push notifications
  void togglePushNotifications(bool value) {
    pushNotificationsEnabled.value = value;
    // TODO: Save preference to backend or local storage
    log(
      'UserProfileController: Push notifications ${value ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle Face ID
  void toggleFaceId(bool value) {
    faceIdEnabled.value = value;
    // TODO: Save preference to backend or local storage
    log('UserProfileController: Face ID ${value ? 'enabled' : 'disabled'}');
  }
}
