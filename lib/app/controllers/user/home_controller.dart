import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../models/dashboard_models.dart';
import '../../services/shared_preferences_service.dart';
import '../../services/user_data_service.dart';
import 'dashboard_controller.dart';

class HomeController extends GetxController {
  // Dashboard controller dependency
  late final DashboardController _dashboardController;

  // User data
  final RxString userName = 'User'.obs;
  final RxString userAddress = 'Select your location'.obs;

  // Location data for API calls
  final Rx<double?> selectedLatitude = Rx<double?>(null);
  final Rx<double?> selectedLongitude = Rx<double?>(null);
  final RxBool hasSelectedLocation = false.obs;

  // Legacy compatibility - these will delegate to dashboard controller
  bool get isLoading => _dashboardController.isLoading.value;
  bool get isLoadingNearestSalons => _dashboardController.isLoading.value;
  bool get isLoadingPopularSalons => _dashboardController.isLoading.value;
  bool get isRefreshing => _dashboardController.isRefreshing.value;

  // Data access - delegate to dashboard controller
  HomeSalonModel? get homeSalon => _dashboardController.homeSaloon.value;
  List<SalonModel> get nearestSalons => _dashboardController.nearestSalons;
  List<SalonModel> get popularSalons => _dashboardController.popularSalons;

  // UI state - return observables for reactivity
  RxBool get showHomeSalonRx => _dashboardController.showHomeSalon;
  RxBool get showNearestSalonsRx => _dashboardController.showNearestSalons;
  RxBool get showPopularSalonsRx => _dashboardController.showPopularSalons;
  RxBool get hasErrorRx => _dashboardController.hasError;
  RxString get errorMessageRx => _dashboardController.errorMessage;

  // Convenience getters for direct value access
  bool get showHomeSalon => _dashboardController.showHomeSalon.value;
  bool get showNearestSalons => _dashboardController.showNearestSalons.value;
  bool get showPopularSalons => _dashboardController.showPopularSalons.value;
  bool get hasError => _dashboardController.hasError.value;
  String get errorMessage => _dashboardController.errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    log('HomeController: Initializing...');

    // Initialize dashboard controller
    _dashboardController = Get.put(DashboardController());

    // Load user data (without location - session only)
    _loadUserData();

    // Listen for location updates from location selection screen
    ever(selectedLatitude, (_) => _onLocationChanged());
    ever(selectedLongitude, (_) => _onLocationChanged());

    // Location is session-only, not persisted across app restarts
    log('HomeController: Location will be requested from user (session-only)');
  }

  // Load user data from API and shared preferences
  Future<void> _loadUserData() async {
    try {
      // First try to load from API
      final userProfile = await UserDataService.getUserProfile();

      if (userProfile != null) {
        // Use data from API
        userName.value = userProfile.fullName.isNotEmpty
            ? userProfile.fullName
            : 'User';
        // Use shortAddress for header display (locality, city)
        userAddress.value = userProfile.shortAddress;

        // Cache the data for future use
        await UserDataService.saveUserProfileToCache(userProfile);

        log(
          'Loaded user data from API - Name: ${userName.value}, Address: ${userAddress.value}',
        );
      } else {
        // Fallback to SharedPreferences data
        final cachedName = await SharedPreferencesService.getUserName();
        // Note: cachedLocation not used - location is session-only

        if (cachedName != null && cachedName.isNotEmpty) {
          userName.value = cachedName;
        } else {
          // Extract name from email if available
          final email = await SharedPreferencesService.getEmail();
          if (email != null && email.isNotEmpty) {
            final emailParts = email.split('@');
            if (emailParts.isNotEmpty) {
              // Capitalize first letter and replace dots/underscores with spaces
              final nameFromEmail = emailParts[0]
                  .replaceAll(RegExp(r'[._]'), ' ')
                  .split(' ')
                  .map(
                    (word) => word.isNotEmpty
                        ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                        : '',
                  )
                  .join(' ');
              userName.value = nameFromEmail.isNotEmpty
                  ? nameFromEmail
                  : 'User';
            }
          } else {
            userName.value = 'User';
          }
        }

        // Always show "Select your location" - location is session-only
        userAddress.value = 'Select your location';

        log(
          'Loaded user data from SharedPreferences - Name: ${userName.value}, Location: ${userAddress.value}',
        );
      }
    } catch (e) {
      log('Error loading user data: $e');
      userName.value = 'User';
      // Always show "Select your location" - location is session-only
      userAddress.value = 'Select your location';
    }
  }

  // Handle location changes and refresh dashboard data
  void _onLocationChanged() {
    final lat = selectedLatitude.value;
    final lng = selectedLongitude.value;

    if (lat != null && lng != null) {
      log('HomeController: Location changed - lat: $lat, lng: $lng');
      _dashboardController.loadDashboardData(
        showLoading: true,
        latitude: lat,
        longitude: lng,
      );
    }
  }

  // Set selected location and trigger data refresh
  void setSelectedLocation(double latitude, double longitude, String address) {
    selectedLatitude.value = latitude;
    selectedLongitude.value = longitude;
    userAddress.value = address;
    hasSelectedLocation.value = true;

    log(
      'HomeController: Location set - lat: $latitude, lng: $longitude, address: $address',
    );

    // Refresh dashboard data with new location
    _dashboardController.loadDashboardData(
      showLoading: true,
      latitude: latitude,
      longitude: longitude,
    );
  }

  // Refresh all data with current location
  Future<void> refreshData() async {
    await Future.wait([
      _loadUserData(),
      _dashboardController.refreshDashboard(
        latitude: selectedLatitude.value,
        longitude: selectedLongitude.value,
      ),
    ]);
  }

  // Retry loading dashboard data with current location
  Future<void> retryLoading() async {
    await _dashboardController.loadDashboardData(
      latitude: selectedLatitude.value,
      longitude: selectedLongitude.value,
    );
  }

  // Search salons
  void searchSalons(String query) {
    if (query.trim().isEmpty) {
      Get.snackbar('Search', 'Please enter a search term');
      return;
    }

    // TODO: Implement search functionality with API
    log('HomeController: Searching for: $query');
    Get.snackbar('Search', 'Searching for: $query');
  }

  // Navigate to salon details
  void navigateToSalonDetails(String salonId) {
    if (salonId.isEmpty) {
      log('HomeController: Invalid salon ID');
      return;
    }

    final salon = _dashboardController.getSalonById(salonId);
    if (salon != null) {
      log('HomeController: Navigating to salon details: ${salon.name}');
      Get.toNamed('/salon-detail', arguments: {'salonId': salonId});
    } else {
      log('HomeController: Salon not found with ID: $salonId');
      Get.snackbar('Error', 'Salon not found');
    }
  }

  // Navigate to salon booking
  void navigateToSalonBooking(String salonId) {
    if (salonId.isEmpty) {
      log('HomeController: Invalid salon ID for booking');
      return;
    }

    final salon = _dashboardController.getSalonById(salonId);
    if (salon != null) {
      log('HomeController: Navigating to salon booking: ${salon.name}');
      // TODO: Navigate to salon booking page
      Get.snackbar('Booking', 'Book appointment at: ${salon.name}');
    } else {
      log('HomeController: Salon not found for booking with ID: $salonId');
      Get.snackbar('Error', 'Salon not found');
    }
  }

  // Rebook at home salon
  void rebookHomeSalon() {
    final homeSalonData = homeSalon;
    if (homeSalonData != null) {
      log('HomeController: Rebooking at home salon: ${homeSalonData.name}');
      navigateToSalonBooking(homeSalonData.id);
    } else {
      log('HomeController: No home salon available for rebooking');
      Get.snackbar('Error', 'No previous salon found');
    }
  }

  // Navigate to all nearest salons
  void viewAllNearestSalons() {
    log('HomeController: Viewing all nearest salons');
    // TODO: Navigate to all nearest salons page
    Get.snackbar('Navigation', 'View all nearest salons');
  }

  // Navigate to all popular salons
  void viewAllPopularSalons() {
    log('HomeController: Viewing all popular salons');
    // TODO: Navigate to all popular salons page
    Get.snackbar('Navigation', 'View all popular salons');
  }

  // Navigate to notifications
  void navigateToNotifications() {
    log('HomeController: Navigating to notifications');
    // TODO: Navigate to notifications page
    Get.snackbar('Navigation', 'Navigate to notifications');
  }

  // Navigate to profile
  void navigateToProfile() {
    log('HomeController: Navigating to profile');
    // TODO: Navigate to profile page
    Get.snackbar('Navigation', 'Navigate to profile');
  }

  // Get salon count for display
  int get totalSalonCount => _dashboardController.totalSalonCount;

  // Check if any data is available
  bool get hasData => _dashboardController.hasData;

  // Check if any salons are available
  bool get hasSalons => _dashboardController.hasSalons;

  // Check if error is recoverable
  bool get canRetry => _dashboardController.canRetry;

  // Debug method to test dashboard loading
  Future<void> testDashboardLoad() async {
    log('HomeController: Testing dashboard load...');
    await _dashboardController.testLoadData();
  }

  // Debug method to test API with location
  Future<void> testDashboardWithLocation() async {
    log('HomeController: Testing dashboard with location...');
    await _dashboardController.loadDashboardData(
      latitude: 19.0605455,
      longitude: 72.87737,
    );
  }

  // Debug method to test API without location
  Future<void> testDashboardWithoutLocation() async {
    log('HomeController: Testing dashboard without location...');
    await _dashboardController.loadDashboardData();
  }

  // Refresh dashboard data after home salon changes
  Future<void> refreshDashboardData() async {
    log('HomeController: Refreshing dashboard data after home salon change...');

    if (hasSelectedLocation.value &&
        selectedLatitude.value != null &&
        selectedLongitude.value != null) {
      // Refresh with current location
      await _dashboardController.loadDashboardData(
        showLoading: false, // Don't show loading indicator for refresh
        latitude: selectedLatitude.value!,
        longitude: selectedLongitude.value!,
      );
      log('HomeController: Dashboard refreshed with location coordinates');
    } else {
      // Refresh without location
      await _dashboardController.loadDashboardData(showLoading: false);
      log('HomeController: Dashboard refreshed without location');
    }
  }

  // Show location selection bottom sheet
  Future<void> showLocationSelectionBottomSheet() async {
    final result = await Get.bottomSheet<Map<String, dynamic>>(
      Container(
        decoration: const BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppConstants.lightGrey,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 24),
              // Location icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppConstants.lightGrey,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.location_on,
                  size: 40,
                  color: AppConstants.primaryBlack,
                ),
              ),
              const SizedBox(height: 24),
              // Title
              Text(
                'Select Your Location',
                style: Theme.of(Get.context!).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryBlack,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              // Description
              Text(
                'To find the nearest salons around you, please select your location first.',
                style: Theme.of(Get.context!).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.mediumGrey,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              // Select Location Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () async {
                    final locationResult = await Get.toNamed(
                      '/location-selection',
                    );
                    if (locationResult != null &&
                        locationResult is Map<String, dynamic>) {
                      final latitude = locationResult['latitude'] as double?;
                      final longitude = locationResult['longitude'] as double?;
                      final address = locationResult['address'] as String?;

                      if (latitude != null &&
                          longitude != null &&
                          address != null) {
                        Get.back();
                        setSelectedLocation(latitude, longitude, address);
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryBlack,
                    foregroundColor: AppConstants.primaryWhite,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 20,
                        color: AppConstants.primaryWhite,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Select Location',
                        style: Theme.of(Get.context!).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppConstants.primaryWhite,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Skip for now button
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'Skip for now',
                  style: Theme.of(Get.context!).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppConstants.mediumGrey,
                  ),
                ),
              ),
              SizedBox(height: MediaQuery.of(Get.context!).padding.bottom),
            ],
          ),
        ),
      ),
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      backgroundColor: Colors.transparent,
    );
  }

  @override
  void onReady() {
    super.onReady();
    log('HomeController: Ready');
  }

  @override
  void onClose() {
    log('HomeController: Disposing...');
    super.onClose();
  }
}
