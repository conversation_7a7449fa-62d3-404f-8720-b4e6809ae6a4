import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/user_appointment_models.dart';
import '../../services/user_appointment_service.dart';

class UserAppointmentController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final UserAppointmentService _appointmentService = UserAppointmentService();

  // Tab controller for switching between upcoming and completed
  late TabController tabController;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;

  // Appointments data
  final RxList<UserAppointment> upcomingAppointments = <UserAppointment>[].obs;
  final RxList<UserAppointment> completedAppointments = <UserAppointment>[].obs;

  // Current tab index
  final RxInt currentTabIndex = 0.obs;

  // Loading states for individual actions
  final RxMap<String, bool> cancellingAppointments = <String, bool>{}.obs;
  final RxMap<String, bool> reschedulingAppointments = <String, bool>{}.obs;
  final RxMap<String, bool> submittingReviews = <String, bool>{}.obs;

  @override
  void onInit() {
    super.onInit();

    // Initialize tab controller
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(() {
      currentTabIndex.value = tabController.index;
    });

    // Load appointments on initialization
    loadAppointments();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// Load all appointments (upcoming and completed)
  Future<void> loadAppointments() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('UserAppointmentController: Loading appointments');

      final response = await _appointmentService.getUserAppointments();

      if (response.success && response.data != null) {
        upcomingAppointments.value = response.data!.upcoming;
        completedAppointments.value = response.data!.past;

        log(
          'UserAppointmentController: Loaded ${upcomingAppointments.length} upcoming and ${completedAppointments.length} completed appointments',
        );
      } else {
        hasError.value = true;
        errorMessage.value = response.message;
        log(
          'UserAppointmentController: Failed to load appointments: ${response.message}',
        );
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to load appointments';
      log('UserAppointmentController: Error loading appointments: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh appointments (pull-to-refresh)
  Future<void> refreshAppointments() async {
    try {
      isRefreshing.value = true;
      await loadAppointments();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Cancel an appointment
  Future<void> cancelAppointment(String appointmentId) async {
    try {
      cancellingAppointments[appointmentId] = true;

      log('UserAppointmentController: Cancelling appointment: $appointmentId');

      final result = await _appointmentService.cancelAppointment(appointmentId);

      if (result['success'] == true) {
        // Remove from upcoming appointments
        upcomingAppointments.removeWhere(
          (appointment) => appointment.id == appointmentId,
        );

        Get.snackbar(
          'Success',
          result['message'] ?? 'Appointment cancelled successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log('UserAppointmentController: Successfully cancelled appointment');
      } else {
        Get.snackbar(
          'Error',
          result['message'] ?? 'Failed to cancel appointment',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log(
          'UserAppointmentController: Failed to cancel appointment: ${result['message']}',
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to cancel appointment',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserAppointmentController: Error cancelling appointment: $e');
    } finally {
      cancellingAppointments.remove(appointmentId);
    }
  }

  /// Reschedule an appointment
  Future<void> rescheduleAppointment({
    required String appointmentId,
    required DateTime newDate,
    required String newTimeSlot,
  }) async {
    try {
      reschedulingAppointments[appointmentId] = true;

      log(
        'UserAppointmentController: Rescheduling appointment: $appointmentId',
      );

      final result = await _appointmentService.rescheduleAppointment(
        appointmentId: appointmentId,
        newDate: newDate,
        newTimeSlot: newTimeSlot,
      );

      if (result['success'] == true) {
        // Refresh appointments to get updated data
        await loadAppointments();

        Get.snackbar(
          'Success',
          result['message'] ?? 'Appointment rescheduled successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log('UserAppointmentController: Successfully rescheduled appointment');
      } else {
        Get.snackbar(
          'Error',
          result['message'] ?? 'Failed to reschedule appointment',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log(
          'UserAppointmentController: Failed to reschedule appointment: ${result['message']}',
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to reschedule appointment',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserAppointmentController: Error rescheduling appointment: $e');
    } finally {
      reschedulingAppointments.remove(appointmentId);
    }
  }

  /// Submit review for completed appointment
  Future<void> submitReview({
    required String appointmentId,
    required int stars,
    String? comment,
  }) async {
    try {
      submittingReviews[appointmentId] = true;

      log(
        'UserAppointmentController: Submitting review for appointment: $appointmentId',
      );

      final request = SubmitReviewRequest(
        appointmentId: appointmentId,
        stars: stars,
        comment: comment,
      );

      final response = await _appointmentService.submitReview(request);

      if (response.success) {
        // Update the appointment in the list to mark as reviewed
        final appointmentIndex = completedAppointments.indexWhere(
          (appointment) => appointment.id == appointmentId,
        );

        if (appointmentIndex != -1) {
          final updatedAppointment = UserAppointment(
            id: completedAppointments[appointmentIndex].id,
            saloonId: completedAppointments[appointmentIndex].saloonId,
            saloonName: completedAppointments[appointmentIndex].saloonName,
            barberId: completedAppointments[appointmentIndex].barberId,
            barberName: completedAppointments[appointmentIndex].barberName,
            amount: completedAppointments[appointmentIndex].amount,
            bookingType: completedAppointments[appointmentIndex].bookingType,
            status: completedAppointments[appointmentIndex].status,
            startTime: completedAppointments[appointmentIndex].startTime,
            date: completedAppointments[appointmentIndex].date,
            endTime: completedAppointments[appointmentIndex].endTime,
            appointmentDate:
                completedAppointments[appointmentIndex].appointmentDate,
            otp: completedAppointments[appointmentIndex].otp,
            canRescheduled:
                completedAppointments[appointmentIndex].canRescheduled,
            createdAt: completedAppointments[appointmentIndex].createdAt,
            services: completedAppointments[appointmentIndex].services,
            isReviewed: true,
            review: response.data,
          );

          completedAppointments[appointmentIndex] = updatedAppointment;
        }

        Get.snackbar(
          'Success',
          'Review submitted successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log('UserAppointmentController: Successfully submitted review');
      } else {
        Get.snackbar(
          'Error',
          response.message,
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        log(
          'UserAppointmentController: Failed to submit review: ${response.message}',
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to submit review',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      log('UserAppointmentController: Error submitting review: $e');
    } finally {
      submittingReviews.remove(appointmentId);
    }
  }

  /// Navigate to booking page for rebooking
  void rebookAppointment(UserAppointment appointment) {
    log('UserAppointmentController: Rebooking appointment: ${appointment.id}');

    // Navigate to booking page with pre-filled data
    Get.toNamed(
      '/booking',
      arguments: {
        'saloonId': appointment.saloonId,
        'barberId': appointment.barberId,
        'serviceIds': appointment.services.map((s) => s.serviceId).toList(),
        'isRebook': true,
      },
    );
  }

  /// Switch to specific tab
  void switchToTab(int index) {
    if (index >= 0 && index < 2) {
      tabController.animateTo(index);
      currentTabIndex.value = index;
    }
  }

  /// Check if appointment is being cancelled
  bool isAppointmentCancelling(String appointmentId) {
    return cancellingAppointments[appointmentId] ?? false;
  }

  /// Check if appointment is being rescheduled
  bool isAppointmentRescheduling(String appointmentId) {
    return reschedulingAppointments[appointmentId] ?? false;
  }

  /// Check if review is being submitted
  bool isReviewSubmitting(String appointmentId) {
    return submittingReviews[appointmentId] ?? false;
  }

  /// Clear error state
  void clearError() {
    hasError.value = false;
    errorMessage.value = '';
  }

  /// Get upcoming appointments count
  int get upcomingCount => upcomingAppointments.length;

  /// Get completed appointments count
  int get completedCount => completedAppointments.length;

  /// Check if there are any appointments
  bool get hasAnyAppointments => upcomingCount > 0 || completedCount > 0;
}
