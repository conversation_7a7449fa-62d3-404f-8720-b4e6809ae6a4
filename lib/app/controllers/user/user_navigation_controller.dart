import 'package:get/get.dart';

class UserNavigationController extends GetxController {
  final RxInt currentIndex = 0.obs;

  void changeIndex(int index) {
    currentIndex.value = index;
  }

  // Navigation methods for programmatic navigation
  void goToHome() => changeIndex(0);
  void goToSearch() => changeIndex(1);
  void goToAppointments() => changeIndex(2);
  void goToBookings() => changeIndex(3);
  void goToProfile() => changeIndex(4);
}
