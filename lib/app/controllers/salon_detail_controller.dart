import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/salon_detail_models.dart';
import '../services/salon_detail_service.dart';
import '../services/home_salon_service.dart';
import 'user/home_controller.dart';

/// Controller for managing salon detail data and state
class SalonDetailController extends GetxController
    with GetSingleTickerProviderStateMixin {
  // Required salon ID
  late final String salonId;

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxBool hasError = false.obs;

  // Data observables
  final Rx<SalonDetailData?> salonDetailData = Rx<SalonDetailData?>(null);
  final Rx<SalonData?> salonData = Rx<SalonData?>(null);
  final RxList<SalonService> services = <SalonService>[].obs;
  final RxList<SalonBarber> barbers = <SalonBarber>[].obs;
  final RxList<SalonReview> reviews = <SalonReview>[].obs;

  // Error handling
  final RxString errorMessage = ''.obs;
  final Rx<SalonDetailServiceException?> lastError =
      Rx<SalonDetailServiceException?>(null);

  // UI state
  final RxInt currentImageIndex = 0.obs;
  final RxBool isHomeSalon = false.obs;
  final RxBool showMoreContent = false.obs;

  // Image carousel auto-scroll
  Timer? _autoScrollTimer;
  PageController? pageController;

  // Tab controller for Services, Barbers, Reviews
  TabController? tabController;
  final RxInt currentTabIndex = 0.obs;

  // Modal bottom sheet states
  final RxBool isServicesModalOpen = false.obs;
  final RxBool isBarbersModalOpen = false.obs;
  final RxBool isReviewModalOpen = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Get salon ID from arguments
    final arguments = Get.arguments;
    log('SalonDetailController: Arguments received: $arguments');

    if (arguments != null && arguments is Map<String, dynamic>) {
      salonId = arguments['salonId'] as String? ?? '';
      log('SalonDetailController: Extracted salon ID from map: $salonId');
    } else if (arguments != null && arguments is String) {
      salonId = arguments;
      log('SalonDetailController: Extracted salon ID from string: $salonId');
    } else {
      salonId = '';
      log('SalonDetailController: No valid arguments found');
    }

    if (salonId.isEmpty) {
      log('SalonDetailController: Salon ID is empty');
      _handleError(SalonDetailServiceException('Salon ID is required'));
      return;
    }

    // Initialize tab controller
    tabController = TabController(length: 3, vsync: this);
    tabController?.addListener(() {
      currentTabIndex.value = tabController?.index ?? 0;
    });

    log('SalonDetailController: Initializing for salon ID: $salonId');
    loadSalonDetail();
  }

  /// Loads salon detail data from API
  Future<void> loadSalonDetail({bool showLoading = true}) async {
    try {
      if (showLoading) {
        isLoading.value = true;
      }
      hasError.value = false;
      errorMessage.value = '';
      lastError.value = null;

      log('SalonDetailController: Loading salon detail data...');

      final response = await SalonDetailService.getSalonDetail(salonId);

      if (response.success && response.data != null) {
        _updateSalonDetailData(response.data!);
        log('SalonDetailController: Salon detail data loaded successfully');
      } else {
        _handleError(
          SalonDetailServiceException(
            response.message.isNotEmpty
                ? response.message
                : 'Failed to load salon detail data',
          ),
        );
      }
    } on SalonDetailServiceException catch (e) {
      log('SalonDetailController: Service exception: $e');
      _handleError(e);
    } catch (e) {
      log('SalonDetailController: Unexpected error: $e');
      _handleError(
        SalonDetailServiceException(
          'An unexpected error occurred while loading salon detail data',
          error: e.toString(),
        ),
      );
    } finally {
      if (showLoading) {
        isLoading.value = false;
      }
    }
  }

  /// Refreshes salon detail data
  Future<void> refreshSalonDetail() async {
    try {
      isRefreshing.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log('SalonDetailController: Refreshing salon detail data...');

      final response = await SalonDetailService.refreshSalonDetail(
        salonId,
        forceRefresh: true,
      );

      if (response.success && response.data != null) {
        _updateSalonDetailData(response.data!);
        log('SalonDetailController: Salon detail data refreshed successfully');

        // Show success message
        Get.snackbar(
          'Success',
          'Salon details updated successfully',
          duration: const Duration(seconds: 2),
        );
      } else {
        _handleError(
          SalonDetailServiceException(
            response.message.isNotEmpty
                ? response.message
                : 'Failed to refresh salon detail data',
          ),
        );
      }
    } on SalonDetailServiceException catch (e) {
      log('SalonDetailController: Refresh service exception: $e');
      _handleError(e);
    } catch (e) {
      log('SalonDetailController: Refresh unexpected error: $e');
      _handleError(
        SalonDetailServiceException(
          'An unexpected error occurred while refreshing salon detail data',
          error: e.toString(),
        ),
      );
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Updates salon detail data and UI state
  void _updateSalonDetailData(SalonDetailData data) {
    salonDetailData.value = data;

    // Update individual data
    salonData.value = data.saloonData;
    services.value = data.saloonServices;
    barbers.value = data.saloonBarbers;
    reviews.value = data.saloonReviews;

    log(
      'SalonDetailController: Updated data - Services: ${data.saloonServices.length}, Barbers: ${data.saloonBarbers.length}, Reviews: ${data.saloonReviews.length}',
    );
  }

  /// Handles errors and updates error state
  void _handleError(SalonDetailServiceException exception) {
    hasError.value = true;
    errorMessage.value = exception.userFriendlyMessage;
    lastError.value = exception;

    log('SalonDetailController: Error handled - ${exception.message}');

    // Show error snackbar
    Get.snackbar(
      'Error',
      exception.userFriendlyMessage,
      duration: const Duration(seconds: 4),
    );
  }

  /// Retries loading salon detail data
  Future<void> retryLoading() async {
    log('SalonDetailController: Retrying salon detail data load...');
    await loadSalonDetail();
  }

  /// Clears all data and resets state
  void clearData() {
    salonDetailData.value = null;
    salonData.value = null;
    services.clear();
    barbers.clear();
    reviews.clear();

    hasError.value = false;
    errorMessage.value = '';
    lastError.value = null;
    currentImageIndex.value = 0;
    isHomeSalon.value = false;

    log('SalonDetailController: Data cleared');
  }

  /// Image carousel methods
  void nextImage() {
    if (salonData.value?.images.isNotEmpty == true) {
      final nextIndex =
          (currentImageIndex.value + 1) % salonData.value!.images.length;
      currentImageIndex.value = nextIndex;

      // Animate to next page if pageController is available
      if (pageController != null) {
        pageController!.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void previousImage() {
    if (salonData.value?.images.isNotEmpty == true) {
      final prevIndex = currentImageIndex.value > 0
          ? currentImageIndex.value - 1
          : salonData.value!.images.length - 1;
      currentImageIndex.value = prevIndex;

      // Animate to previous page if pageController is available
      if (pageController != null) {
        pageController!.animateToPage(
          prevIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void setImageIndex(int index) {
    if (salonData.value?.images.isNotEmpty == true &&
        index >= 0 &&
        index < salonData.value!.images.length) {
      currentImageIndex.value = index;
    }
  }

  /// Auto-scroll functionality
  void startAutoScroll() {
    if (salonData.value?.images.length != null &&
        salonData.value!.images.length > 1) {
      _autoScrollTimer?.cancel();
      _autoScrollTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
        nextImage();
      });
    }
  }

  void stopAutoScroll() {
    _autoScrollTimer?.cancel();
    _autoScrollTimer = null;
  }

  void initializePageController() {
    pageController = PageController(initialPage: currentImageIndex.value);
    startAutoScroll();
  }

  /// Show more content functionality
  void toggleShowMore() {
    showMoreContent.value = !showMoreContent.value;
  }

  /// Tab navigation methods
  void switchToTab(int index) {
    if (index >= 0 && index < 3 && tabController != null) {
      tabController!.animateTo(index);
    }
  }

  /// Modal bottom sheet methods
  void openServicesModal() {
    isServicesModalOpen.value = true;
    // Modal will be opened from the UI
  }

  void openBarbersModal() {
    isBarbersModalOpen.value = true;
    // Modal will be opened from the UI
  }

  void openReviewModal() {
    isReviewModalOpen.value = true;
    // TODO: Implement review modal
    Get.snackbar('Reviews', 'Review modal will be implemented');
  }

  /// Action methods
  Future<void> toggleHomeSalon() async {
    if (salonData.value == null) {
      Get.snackbar('Error', 'Salon data not available');
      return;
    }

    final salonId = salonData.value!.id;
    if (salonId.isEmpty) {
      Get.snackbar('Error', 'Salon ID not available');
      return;
    }
    final wasHomeSalon = isHomeSalon.value;

    try {
      // Optimistically update UI
      isHomeSalon.value = !isHomeSalon.value;

      if (isHomeSalon.value) {
        // Add to home salon
        log('SalonDetailController: Adding salon to home salon - ID: $salonId');

        final response = await HomeSalonService.addHomeSalon(salonId);

        if (response.success) {
          log('SalonDetailController: Successfully added to home salon');

          // Refresh home screen dashboard data
          try {
            final homeController = Get.find<HomeController>();
            await homeController.refreshDashboardData();
            log(
              'SalonDetailController: Home dashboard refreshed after adding home salon',
            );
          } catch (e) {
            log('SalonDetailController: Could not refresh home dashboard: $e');
          }

          Get.snackbar(
            'Added to Home',
            'Salon added to your home salon',
            backgroundColor: Colors.green.withValues(alpha: 0.1),
            colorText: Colors.green,
            icon: const Icon(Icons.favorite, color: Colors.green),
            duration: const Duration(seconds: 2),
          );
        } else {
          // Revert on failure
          isHomeSalon.value = wasHomeSalon;
          Get.snackbar(
            'Error',
            response.message.isNotEmpty
                ? response.message
                : 'Failed to add to home salon',
            backgroundColor: Colors.red.withValues(alpha: 0.1),
            colorText: Colors.red,
            icon: const Icon(Icons.error, color: Colors.red),
          );
        }
      } else {
        // For now, just show success message for remove action
        // TODO: Implement remove from home salon API when available
        log('SalonDetailController: Removed from home salon (local only)');
        Get.snackbar(
          'Removed',
          'Salon removed from your home salon',
          backgroundColor: Colors.orange.withValues(alpha: 0.1),
          colorText: Colors.orange,
          icon: const Icon(Icons.favorite_border, color: Colors.orange),
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      // Revert UI state on error
      isHomeSalon.value = wasHomeSalon;

      log('SalonDetailController: Error toggling home salon: $e');

      String errorMessage = 'Failed to update home salon';
      if (e is HomeSalonServiceException) {
        errorMessage = e.message;
      }

      Get.snackbar(
        'Error',
        errorMessage,
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red,
        icon: const Icon(Icons.error, color: Colors.red),
        duration: const Duration(seconds: 3),
      );
    }
  }

  void bookAppointment() {
    if (salonData.value == null) {
      Get.snackbar('Error', 'Salon data not available');
      return;
    }

    final salonId = salonData.value!.id;
    String? selectedBarberId;

    // Select a random barber if available
    if (barbers.isNotEmpty) {
      final availableBarbers = barbers
          .where((barber) => barber.available)
          .toList();
      if (availableBarbers.isNotEmpty) {
        // Select random available barber
        selectedBarberId =
            availableBarbers[DateTime.now().millisecondsSinceEpoch %
                    availableBarbers.length]
                .id;
      } else {
        // If no available barbers, select any barber
        selectedBarberId = barbers.first.id;
      }
    }

    log(
      'SalonDetailController: Booking appointment - Salon: $salonId, Barber: $selectedBarberId',
    );

    Get.toNamed(
      '/booking',
      arguments: {
        'saloonId': salonId,
        'barberId': selectedBarberId,
        'services': services.toList(),
        'salonName': salonData.value!.saloonName,
      },
    );
  }

  void bookWithBarber(String barberId) {
    // Find the barber name
    final barber = barbers.firstWhereOrNull((b) => b.id == barberId);
    final barberName = barber?.fullName ?? '';

    Get.toNamed(
      '/booking',
      arguments: {
        'saloonId': salonId,
        'barberId': barberId,
        'barberName': barberName,
        'services': services.toList(),
      },
    );
  }

  /// Getters for UI
  bool get hasData => salonDetailData.value != null;
  bool get hasSalonData => salonData.value != null;
  bool get hasServices => services.isNotEmpty;
  bool get hasBarbers => barbers.isNotEmpty;
  bool get hasReviews => reviews.isNotEmpty;
  bool get hasImages => salonData.value?.hasImages == true;
  bool get canRetry => lastError.value?.isRecoverable ?? true;
  bool get showReviewFAB => currentTabIndex.value == 2; // Reviews tab

  @override
  void onClose() {
    tabController?.dispose();
    pageController?.dispose();
    stopAutoScroll();
    log('SalonDetailController: Disposing...');
    super.onClose();
  }
}
