import 'dart:developer';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

class AppController extends GetxController {
  static AppController get instance => Get.find<AppController>();

  final RxBool isAppInitialized = false.obs;
  late AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _authController = Get.find<AuthController>();
  }

  /// Initialize app and handle first-time vs returning user flow
  Future<void> initializeApp() async {
    try {
      log('Initializing app...');

      // Check authentication status using AuthController
      await _authController.checkAuthenticationStatus();

      // Mark app as initialized
      isAppInitialized.value = true;

      log('App initialization complete');
    } catch (e) {
      log('App initialization error: $e');
      // Even if there's an error, mark as initialized to prevent infinite loading
      isAppInitialized.value = true;
    }
  }

  /// Handle navigation based on authentication status
  void handleAppNavigation() {
    if (!isAppInitialized.value) {
      log('App not initialized yet, staying on splash');
      return;
    }

    // Use AuthController to handle navigation
    _authController.navigateBasedOnAuthStatus();
  }

  // Navigation is now handled by AuthController

  /// Force app reinitialization (useful for logout)
  Future<void> reinitializeApp() async {
    isAppInitialized.value = false;
    await initializeApp();
    handleAppNavigation();
  }
}
