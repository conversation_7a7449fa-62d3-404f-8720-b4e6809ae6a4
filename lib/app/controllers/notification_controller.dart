import 'dart:developer';
import 'package:get/get.dart';
import '../models/notification_model.dart';
import '../models/auth_models.dart';
import '../services/notification_api_service.dart';

class NotificationController extends GetxController {
  // Observable lists and states
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  // Pagination
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxBool hasNextPage = false.obs;
  final RxBool hasPreviousPage = false.obs;
  final RxInt totalNotifications = 0.obs;
  final int limit = 20;

  // Unread count
  final RxInt unreadCount = 0.obs;

  // Mark as read loading states
  final RxSet<String> markingAsReadIds = <String>{}.obs;

  @override
  void onInit() {
    super.onInit();
    log('NotificationController: ===== CONTROLLER INITIALIZED =====');
    log(
      'NotificationController: Starting to fetch notifications and unread count',
    );
    fetchNotifications();
    getUnreadCount();
  }

  /// Fetch notifications for the current page
  Future<void> fetchNotifications({bool refresh = false}) async {
    log('NotificationController: ===== FETCH NOTIFICATIONS CALLED =====');
    log('NotificationController: Refresh mode: $refresh');

    try {
      if (refresh) {
        currentPage.value = 1;
        notifications.clear();
        log('NotificationController: Cleared notifications for refresh');
      }

      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      log(
        'NotificationController: Fetching notifications - Page: ${currentPage.value}',
      );

      final response = await NotificationApiService.getNotifications(
        page: currentPage.value,
        limit: limit,
      );

      if (response.success) {
        // Debug: Log detailed response information
        log('NotificationController: ===== API RESPONSE DEBUG =====');
        log('NotificationController: Response success: ${response.success}');
        log(
          'NotificationController: Notifications count: ${response.notifications.length}',
        );
        log(
          'NotificationController: Pagination info: ${response.pagination.toString()}',
        );

        // Debug: Log each notification details
        for (int i = 0; i < response.notifications.length; i++) {
          final notification = response.notifications[i];
          log(
            'NotificationController: Notification $i: ID=${notification.id}, Title="${notification.title}", IsRead=${notification.isRead}',
          );
        }

        if (refresh) {
          notifications.value = response.notifications;
          log(
            'NotificationController: Notifications list refreshed with ${response.notifications.length} items',
          );
        } else {
          notifications.addAll(response.notifications);
          log(
            'NotificationController: Added ${response.notifications.length} notifications to existing list',
          );
        }

        // Update pagination info
        currentPage.value = response.pagination.page;
        totalPages.value = response.pagination.totalPages;
        hasNextPage.value = response.pagination.hasNext;
        hasPreviousPage.value = response.pagination.hasPrev;
        totalNotifications.value = response.pagination.total;

        log(
          'NotificationController: Pagination updated - Page: ${currentPage.value}/${totalPages.value}, HasNext: ${hasNextPage.value}',
        );
        log(
          'NotificationController: Total notifications in list: ${notifications.length}',
        );

        // Update unread count
        _updateUnreadCount();
      } else {
        hasError.value = true;
        errorMessage.value = 'Failed to fetch notifications';
      }
    } catch (e) {
      log('NotificationController: Error fetching notifications: $e');
      hasError.value = true;
      if (e is ApiError) {
        errorMessage.value = e.message;
      } else {
        errorMessage.value = 'Failed to fetch notifications. Please try again.';
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (isLoadingMore.value || !hasNextPage.value) return;

    try {
      isLoadingMore.value = true;
      currentPage.value++;

      log(
        'NotificationController: Loading more notifications - Page: ${currentPage.value}',
      );

      final response = await NotificationApiService.getNotifications(
        page: currentPage.value,
        limit: limit,
      );

      if (response.success) {
        notifications.addAll(response.notifications);

        // Update pagination info
        hasNextPage.value = response.pagination.hasNext;
        hasPreviousPage.value = response.pagination.hasPrev;

        log(
          'NotificationController: Successfully loaded ${response.notifications.length} more notifications',
        );
      }
    } catch (e) {
      log('NotificationController: Error loading more notifications: $e');
      // Revert page increment on error
      currentPage.value--;
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// Mark a specific notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      markingAsReadIds.add(notificationId);

      log(
        'NotificationController: Marking notification as read - ID: $notificationId',
      );

      final response = await NotificationApiService.markAsRead(notificationId);

      if (response.success) {
        // Update the notification in the list
        final index = notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          notifications[index] = notifications[index].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
          notifications.refresh();
        }

        // Update unread count
        _updateUnreadCount();

        log('NotificationController: Successfully marked notification as read');
      }
    } catch (e) {
      log('NotificationController: Error marking notification as read: $e');
      // Don't show error to user as per requirements
    } finally {
      markingAsReadIds.remove(notificationId);
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      log('NotificationController: Marking all notifications as read');

      final response = await NotificationApiService.markAllAsRead();

      if (response.success) {
        // Update all notifications in the list
        for (int i = 0; i < notifications.length; i++) {
          if (!notifications[i].isRead) {
            notifications[i] = notifications[i].copyWith(
              isRead: true,
              readAt: DateTime.now(),
            );
          }
        }
        notifications.refresh();

        // Update unread count
        unreadCount.value = 0;

        log(
          'NotificationController: Successfully marked all notifications as read',
        );
      }
    } catch (e) {
      log(
        'NotificationController: Error marking all notifications as read: $e',
      );
      // Don't show error to user as per requirements
    }
  }

  /// Get unread notification count
  Future<void> getUnreadCount() async {
    try {
      final count = await NotificationApiService.getUnreadCount();
      unreadCount.value = count;
      log('NotificationController: Unread count updated: $count');
    } catch (e) {
      log('NotificationController: Error getting unread count: $e');
    }
  }

  /// Update unread count based on current notifications
  void _updateUnreadCount() {
    final count = notifications.where((n) => !n.isRead).length;
    unreadCount.value = count;
  }

  /// Refresh notifications (pull to refresh)
  Future<void> refreshNotifications() async {
    await fetchNotifications(refresh: true);
    await getUnreadCount();
  }

  /// Check if a notification is being marked as read
  bool isMarkingAsRead(String notificationId) {
    return markingAsReadIds.contains(notificationId);
  }

  /// Get notifications grouped by date
  Map<String, List<NotificationModel>> get notificationsByDate {
    final Map<String, List<NotificationModel>> grouped = {};

    for (final notification in notifications) {
      final dateKey = notification.formattedDate;
      if (grouped[dateKey] == null) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }

    return grouped;
  }

  /// Clear all data (useful for logout)
  void clearData() {
    notifications.clear();
    currentPage.value = 1;
    totalPages.value = 1;
    hasNextPage.value = false;
    hasPreviousPage.value = false;
    totalNotifications.value = 0;
    unreadCount.value = 0;
    markingAsReadIds.clear();
    hasError.value = false;
    errorMessage.value = '';
  }
}
