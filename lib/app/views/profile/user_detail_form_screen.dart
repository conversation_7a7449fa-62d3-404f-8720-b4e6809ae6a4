import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:country_picker/country_picker.dart';
import 'dart:io';
import 'dart:developer';
import '../../constants/app_constants.dart';
import '../../routes/app_routes.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_phone_field.dart';
import '../../widgets/profile_image_picker.dart';
import '../../services/registration_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../services/shared_preferences_service.dart';
import '../../models/user_registration_model.dart';
import '../../controllers/auth_controller.dart';

class UserDetailFormScreen extends StatefulWidget {
  const UserDetailFormScreen({super.key});

  @override
  State<UserDetailFormScreen> createState() => _UserDetailFormScreenState();
}

class _UserDetailFormScreenState extends State<UserDetailFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _localityController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();

  // Focus nodes for keyboard navigation
  final _firstNameFocus = FocusNode();
  final _lastNameFocus = FocusNode();
  final _emailFocus = FocusNode();
  final _phoneFocus = FocusNode();
  final _localityFocus = FocusNode();
  final _cityFocus = FocusNode();
  final _stateFocus = FocusNode();
  final _pincodeFocus = FocusNode();

  File? _profileImage;
  bool _isLoading = false;
  Country? _selectedCountry;
  final FirebaseStorageService _firebaseStorageService =
      FirebaseStorageService();
  final AuthController _authController = Get.find<AuthController>();

  @override
  void initState() {
    super.initState();
    _loadUserEmail();
  }

  // Load user email from SharedPreferences
  void _loadUserEmail() async {
    try {
      final email = await SharedPreferencesService.getEmail();
      if (email != null && email.isNotEmpty) {
        _emailController.text = email;
      }
    } catch (e) {
      log('Error loading user email: $e');
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _localityController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();

    _firstNameFocus.dispose();
    _lastNameFocus.dispose();
    _emailFocus.dispose();
    _phoneFocus.dispose();
    _localityFocus.dispose();
    _cityFocus.dispose();
    _stateFocus.dispose();
    _pincodeFocus.dispose();
    super.dispose();
  }

  void _handleImagePicked(File? image) {
    setState(() {
      _profileImage = image;
    });
  }

  void _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String? profileImageUrl;

      // Upload profile image if selected
      if (_profileImage != null) {
        try {
          profileImageUrl = await _firebaseStorageService.uploadUserProfileFile(
            _profileImage!,
          );
          log('Profile image uploaded successfully: $profileImageUrl');
        } catch (e) {
          // Continue without profile image if upload fails
          log('Profile image upload failed: $e');
          Get.snackbar(
            'Warning',
            'Profile image upload failed, continuing without it',
            backgroundColor: Colors.orange,
            colorText: AppConstants.primaryWhite,
          );
        }
      }

      // Create user registration request
      final request = UserRegistrationRequest(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        phone: RegistrationService.formatPhoneNumber(
          _phoneController.text.trim(),
        ),
        email: RegistrationService.formatEmail(_emailController.text.trim()),
        area: _localityController.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        country: _selectedCountry?.name ?? 'India',
        pincode: RegistrationService.parsePincode(
          _pincodeController.text.trim(),
        ),
        profileImage: profileImageUrl,
      );

      // Validate request data
      final validationError = RegistrationService.validateUserData(request);
      if (validationError != null) {
        Get.snackbar(
          'Validation Error',
          validationError,
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
        );
        return;
      }

      // Submit user registration to API
      final response = await RegistrationService.registerUser(request);

      if (response.success) {
        Get.snackbar(
          'Success',
          response.message.isNotEmpty
              ? response.message
              : 'Profile created successfully!',
          backgroundColor: Colors.green,
          colorText: AppConstants.primaryWhite,
        );

        // Update profile completion status and navigate
        await SharedPreferencesService.saveProfileCompletionStatus(true);
        _authController.onProfileCompleted();
      } else {
        Get.snackbar(
          'Error',
          response.message.isNotEmpty
              ? response.message
              : 'Failed to create profile',
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Something went wrong. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
      );
      log('Profile submission error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Profile'),
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.paddingLarge),

                // Header
                _buildHeader(context),

                const SizedBox(height: AppConstants.paddingXLarge),

                // Profile Image Picker
                Center(
                  child: ProfileImagePicker(
                    onImagePicked: _handleImagePicked,
                    currentImage: _profileImage,
                  ),
                ),

                const SizedBox(height: AppConstants.paddingXLarge),

                // First Name
                CustomTextField(
                  controller: _firstNameController,
                  labelText: 'First Name *',
                  hintText: 'Enter your first name',
                  focusNode: _firstNameFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_lastNameFocus);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.firstNameRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Last Name
                CustomTextField(
                  controller: _lastNameController,
                  labelText: 'Last Name *',
                  hintText: 'Enter your last name',
                  focusNode: _lastNameFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_emailFocus);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.lastNameRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Email (Pre-filled from registration)
                CustomTextField(
                  controller: _emailController,
                  labelText: 'Email *',
                  hintText: 'Enter your email',
                  keyboardType: TextInputType.emailAddress,
                  focusNode: _emailFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_phoneFocus);
                  },
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Email is required';
                    }
                    if (!GetUtils.isEmail(value.trim())) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Phone Number
                CustomPhoneField(
                  controller: _phoneController,
                  labelText: 'Phone Number *',
                  hintText: 'Enter your phone number',
                  focusNode: _phoneFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_localityFocus);
                  },
                  onCountryChanged: (Country country) {
                    setState(() {
                      _selectedCountry = country;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.phoneRequired;
                    }
                    // Validate based on selected country's phone number length
                    if (_selectedCountry != null) {
                      // For India (default), expect 10 digits
                      if (_selectedCountry!.phoneCode == '91') {
                        if (value.length != 10 ||
                            !RegExp(r'^[0-9]+$').hasMatch(value)) {
                          return 'Please enter a valid 10-digit phone number';
                        }
                      } else {
                        // For other countries, basic validation
                        if (value.length < 7 ||
                            value.length > 15 ||
                            !RegExp(r'^[0-9]+$').hasMatch(value)) {
                          return 'Please enter a valid phone number';
                        }
                      }
                    } else {
                      // Default validation
                      if (value.length < 7 ||
                          value.length > 15 ||
                          !RegExp(r'^[0-9]+$').hasMatch(value)) {
                        return 'Please enter a valid phone number';
                      }
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Locality
                CustomTextField(
                  controller: _localityController,
                  labelText: 'Locality *',
                  hintText: 'Enter your locality',
                  focusNode: _localityFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_cityFocus);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.localityRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // City
                CustomTextField(
                  controller: _cityController,
                  labelText: 'City *',
                  hintText: 'Enter your city',
                  focusNode: _cityFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_stateFocus);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.cityRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // State
                CustomTextField(
                  controller: _stateController,
                  labelText: 'State *',
                  hintText: 'Enter your state',
                  focusNode: _stateFocus,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).requestFocus(_pincodeFocus);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.stateRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Pincode
                CustomTextField(
                  controller: _pincodeController,
                  labelText: 'Pincode *',
                  hintText: 'Enter your pincode',
                  keyboardType: TextInputType.number,
                  focusNode: _pincodeFocus,
                  textInputAction: TextInputAction.done,
                  onFieldSubmitted: (_) {
                    _pincodeFocus.unfocus();
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.pincodeRequired;
                    }
                    if (value.length != 6) {
                      return AppConstants.pincodeInvalid;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingXLarge * 2),

                // Submit Button
                CustomButton(
                  text: 'Complete Registration',
                  onPressed: _handleSubmit,
                  isLoading: _isLoading,
                ),

                const SizedBox(height: AppConstants.paddingLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Almost There!',
          style: Theme.of(
            context,
          ).textTheme.displaySmall?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Text(
          'Please fill in your details to complete your profile',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: AppConstants.mediumGrey),
        ),
      ],
    );
  }
}
