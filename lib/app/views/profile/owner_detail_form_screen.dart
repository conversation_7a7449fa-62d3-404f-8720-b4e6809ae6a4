import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:country_picker/country_picker.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:io';
import 'dart:developer';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_phone_field.dart';
import '../../widgets/profile_image_picker.dart';
import '../../services/registration_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../services/shared_preferences_service.dart';
import '../../models/user_registration_model.dart';
import '../../controllers/auth_controller.dart';

class OwnerDetailFormScreen extends StatefulWidget {
  const OwnerDetailFormScreen({super.key});

  @override
  State<OwnerDetailFormScreen> createState() => _OwnerDetailFormScreenState();
}

class _OwnerDetailFormScreenState extends State<OwnerDetailFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _localityController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _salonNameController = TextEditingController();
  final _salonRegIdController = TextEditingController();
  final _salonDescController = TextEditingController();
  final _businessEmailController = TextEditingController();
  final _actualAddressController = TextEditingController();

  // Focus nodes for keyboard navigation
  final _firstNameFocus = FocusNode();
  final _lastNameFocus = FocusNode();
  final _phoneFocus = FocusNode();
  final _localityFocus = FocusNode();
  final _cityFocus = FocusNode();
  final _stateFocus = FocusNode();
  final _pincodeFocus = FocusNode();
  final _salonNameFocus = FocusNode();
  final _salonRegIdFocus = FocusNode();
  final _businessEmailFocus = FocusNode();
  final _salonDescFocus = FocusNode();

  File? _profileImage;
  bool _isLoading = false;
  bool _isUploadingLogo = false;
  double _logoUploadProgress = 0.0;
  Country? _selectedCountry;
  final FirebaseStorageService _firebaseStorageService =
      FirebaseStorageService();
  final AuthController _authController = Get.find<AuthController>();

  // Location variables
  LatLng? _selectedLocation;
  double? _latitude;
  double? _longitude;
  bool _isLoadingLocation = false;

  @override
  void initState() {
    super.initState();
    _loadUserEmail();
  }

  // Load user email from SharedPreferences
  void _loadUserEmail() async {
    try {
      final email = await SharedPreferencesService.getEmail();
      if (email != null && email.isNotEmpty) {
        _businessEmailController.text = email;
      }
    } catch (e) {
      log('Error loading user email: $e');
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _localityController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _salonNameController.dispose();
    _salonRegIdController.dispose();
    _salonDescController.dispose();
    _businessEmailController.dispose();
    _actualAddressController.dispose();

    _firstNameFocus.dispose();
    _lastNameFocus.dispose();
    _phoneFocus.dispose();
    _localityFocus.dispose();
    _cityFocus.dispose();
    _stateFocus.dispose();
    _pincodeFocus.dispose();
    _salonNameFocus.dispose();
    _salonRegIdFocus.dispose();
    _businessEmailFocus.dispose();
    _salonDescFocus.dispose();
    super.dispose();
  }

  void _handleImagePicked(File? image) {
    setState(() {
      _profileImage = image;
    });
  }

  // Get current location and update address field
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            'Permission Denied',
            'Location permission is required to get your current location',
            backgroundColor: Colors.orange,
            colorText: AppConstants.primaryWhite,
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          'Permission Denied',
          'Location permission is permanently denied. Please enable it in settings.',
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
        );
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      _latitude = position.latitude;
      _longitude = position.longitude;
      _selectedLocation = LatLng(position.latitude, position.longitude);

      log('Current location: $_latitude, $_longitude');

      // Get address from coordinates
      try {
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          final address = _formatAddress(placemark);

          setState(() {
            _actualAddressController.text = address;
          });

          log('Address: $address');
        }
      } catch (e) {
        log('Error getting address: $e');
      }
    } catch (e) {
      log('Error getting location: $e');
      Get.snackbar(
        'Error',
        'Failed to get current location: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
      );
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  // Format address from placemark
  String _formatAddress(Placemark placemark) {
    final parts = <String>[];

    if (placemark.name?.isNotEmpty == true) parts.add(placemark.name!);
    if (placemark.locality?.isNotEmpty == true) parts.add(placemark.locality!);
    if (placemark.administrativeArea?.isNotEmpty == true) {
      parts.add(placemark.administrativeArea!);
    }
    if (placemark.country?.isNotEmpty == true) parts.add(placemark.country!);

    return parts.join(', ');
  }

  void _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String? logoUrl;

      // Upload profile image as salon logo if selected
      if (_profileImage != null) {
        try {
          setState(() {
            _isUploadingLogo = true;
            _logoUploadProgress = 0.0;
          });

          logoUrl = await _firebaseStorageService.uploadSalonLogo(
            _profileImage!,
            onProgress: (progress) {
              setState(() {
                _logoUploadProgress = progress;
              });
            },
          );

          log('Salon logo uploaded successfully: $logoUrl');
        } catch (e) {
          // Continue without logo if upload fails
          log('Salon logo upload failed: $e');
          Get.snackbar(
            'Warning',
            'Logo upload failed, continuing without it',
            backgroundColor: Colors.orange,
            colorText: AppConstants.primaryWhite,
          );
        } finally {
          setState(() {
            _isUploadingLogo = false;
            _logoUploadProgress = 0.0;
          });
        }
      }

      // Create OwnerRegistrationRequest for owner
      final request = OwnerRegistrationRequest(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        salonName: _salonNameController.text.trim(),
        salonDescription: _salonDescController.text.trim(),
        salonPhone: RegistrationService.formatPhoneNumber(
          _phoneController.text.trim(),
        ),
        salonEmail: RegistrationService.formatEmail(
          _businessEmailController.text.trim(),
        ),
        area: _localityController.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        country: _selectedCountry?.name ?? 'India',
        pinCode: _pincodeController.text.trim(),
        salonRegid: _salonRegIdController.text.trim(),
        shopNo: '', // Add shop number field if needed
        logoUrl: logoUrl,
        actualaddress: _actualAddressController.text.trim().isNotEmpty
            ? _actualAddressController.text.trim()
            : null,
        lat: _latitude,
        long: _longitude,
      );

      // Debug print location data
      log('Location data - Address: ${_actualAddressController.text.trim()}');
      log('Location data - Latitude: $_latitude');
      log('Location data - Longitude: $_longitude');

      // Validate request data
      final validationError = RegistrationService.validateOwnerData(request);
      if (validationError != null) {
        Get.snackbar(
          'Validation Error',
          validationError,
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
        );
        return;
      }

      // Submit owner registration to API
      final response = await RegistrationService.registerOwner(request);

      if (response.success) {
        Get.snackbar(
          'Success',
          response.message.isNotEmpty
              ? response.message
              : 'Salon registration completed successfully!',
          backgroundColor: Colors.green,
          colorText: AppConstants.primaryWhite,
        );

        // Update profile completion status and navigate
        await SharedPreferencesService.saveProfileCompletionStatus(true);
        _authController.onProfileCompleted();
      } else {
        Get.snackbar(
          'Error',
          response.message.isNotEmpty
              ? response.message
              : 'Failed to register salon',
          backgroundColor: Colors.red,
          colorText: AppConstants.primaryWhite,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Something went wrong. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
      );
      log('Salon registration error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryWhite,
        elevation: 0,
        title: Text(
          'Salon Owner Details',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(context),

                const SizedBox(height: AppConstants.paddingMedium),

                // Salon Logo Picker
                Center(
                  child: Column(
                    children: [
                      Text(
                        'Salon Logo',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      ProfileImagePicker(
                        onImagePicked: _handleImagePicked,
                        currentImage: _profileImage,
                      ),
                      if (_isUploadingLogo) ...[
                        const SizedBox(height: AppConstants.paddingSmall),
                        _buildUploadProgress(
                          'Uploading salon logo...',
                          _logoUploadProgress,
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Personal Information Section
                _buildSectionHeader(context, 'Personal Information'),

                const SizedBox(height: AppConstants.paddingMedium),

                // First Name
                CustomTextField(
                  controller: _firstNameController,
                  labelText: 'First Name *',
                  hintText: 'Enter your first name',
                  focusNode: _firstNameFocus,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.firstNameRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Last Name
                CustomTextField(
                  controller: _lastNameController,
                  labelText: 'Last Name *',
                  hintText: 'Enter your last name',
                  focusNode: _lastNameFocus,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.lastNameRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Phone Number
                CustomPhoneField(
                  controller: _phoneController,
                  labelText: 'Phone Number *',
                  hintText: 'Enter your phone number',
                  onCountryChanged: (Country country) {
                    setState(() {
                      _selectedCountry = country;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.phoneRequired;
                    }
                    // Validate based on selected country's phone number length
                    if (_selectedCountry != null) {
                      // For India (default), expect 10 digits
                      if (_selectedCountry!.phoneCode == '91') {
                        if (value.length != 10 ||
                            !RegExp(r'^[0-9]+$').hasMatch(value)) {
                          return 'Please enter a valid 10-digit phone number';
                        }
                      } else {
                        // For other countries, basic validation
                        if (value.length < 7 ||
                            value.length > 15 ||
                            !RegExp(r'^[0-9]+$').hasMatch(value)) {
                          return 'Please enter a valid phone number';
                        }
                      }
                    } else {
                      // Default validation
                      if (value.length < 7 ||
                          value.length > 15 ||
                          !RegExp(r'^[0-9]+$').hasMatch(value)) {
                        return 'Please enter a valid phone number';
                      }
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Locality
                CustomTextField(
                  controller: _localityController,
                  labelText: 'Locality *',
                  hintText: 'Enter your locality',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.localityRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // City
                CustomTextField(
                  controller: _cityController,
                  labelText: 'City *',
                  hintText: 'Enter your city',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.cityRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // State
                CustomTextField(
                  controller: _stateController,
                  labelText: 'State *',
                  hintText: 'Enter your state',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.stateRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Pincode
                CustomTextField(
                  controller: _pincodeController,
                  labelText: 'Pincode *',
                  hintText: 'Enter your pincode',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.pincodeRequired;
                    }
                    if (value.length != 6) {
                      return AppConstants.pincodeInvalid;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Location Section
                _buildSectionHeader(context, 'Salon Location'),

                const SizedBox(height: AppConstants.paddingMedium),

                // Map Container
                _buildMapSection(),

                const SizedBox(height: AppConstants.paddingMedium),

                // Address Text Field
                CustomTextField(
                  controller: _actualAddressController,
                  labelText: 'Exact Address',
                  hintText: 'Click the button on map to get exact location',
                  maxLines: 2,
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Business Information Section
                _buildSectionHeader(context, 'Business Information'),

                const SizedBox(height: AppConstants.paddingMedium),

                // Salon Name
                CustomTextField(
                  controller: _salonNameController,
                  labelText: 'Salon Name *',
                  hintText: 'Enter your salon name',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.salonNameRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Salon Registration ID
                CustomTextField(
                  controller: _salonRegIdController,
                  labelText: 'Salon Registration ID *',
                  hintText: 'Enter your salon registration ID',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.salonRegIdRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Business Email
                CustomTextField(
                  controller: _businessEmailController,
                  labelText: 'Business Email *',
                  hintText: 'Enter your business email',
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.businessEmailRequired;
                    }
                    if (!GetUtils.isEmail(value)) {
                      return AppConstants.emailInvalid;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Salon Description
                CustomTextField(
                  controller: _salonDescController,
                  labelText: 'Salon Description *',
                  hintText: 'Describe your salon and services',
                  maxLines: 4,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppConstants.salonDescRequired;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Submit Button
                CustomButton(
                  text: 'Complete Registration',
                  onPressed: _handleSubmit,
                  isLoading: _isLoading,
                ),

                const SizedBox(height: AppConstants.paddingMedium),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMapSection() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppConstants.lightGrey),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Stack(
          children: [
            // Map
            FlutterMap(
              options: MapOptions(
                initialCenter:
                    _selectedLocation ??
                    const LatLng(28.6139, 77.2090), // Default to Delhi
                initialZoom: _selectedLocation != null ? 15.0 : 10.0,
                onTap: (tapPosition, point) {
                  // Handle map tap if needed
                },
              ),
              children: [
                TileLayer(
                  urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                  userAgentPackageName: 'com.iktworkspvt.barbrandco',
                ),
                if (_selectedLocation != null)
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: _selectedLocation!,
                        width: 40,
                        height: 40,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppConstants.primaryBlack,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppConstants.primaryWhite,
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.location_on,
                            color: AppConstants.primaryWhite,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),

            // Get Location Button
            Positioned(
              bottom: 16,
              right: 16,
              child: FloatingActionButton(
                heroTag: "get_location",
                onPressed: _isLoadingLocation ? null : _getCurrentLocation,
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                elevation: 4,
                mini: true,
                child: _isLoadingLocation
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppConstants.primaryWhite,
                        ),
                      )
                    : const Icon(Icons.my_location, size: 20),
              ),
            ),

            // Loading overlay
            if (_isLoadingLocation)
              Container(
                color: AppConstants.primaryBlack.withValues(alpha: 0.3),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryWhite,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingSmall),
                        Text(
                          'Getting location...',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome Salon Owner!',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Text(
          'Please provide your personal and business details to complete your salon registration',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: AppConstants.mediumGrey,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppConstants.primaryBlack,
      ),
    );
  }

  /// Build upload progress widget
  Widget _buildUploadProgress(String message, double progress) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Column(
        children: [
          Text(
            message,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppConstants.mediumGrey.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(
              AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '${(progress * 100).toInt()}%',
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w400,
              color: AppConstants.mediumGrey,
            ),
          ),
        ],
      ),
    );
  }
}
