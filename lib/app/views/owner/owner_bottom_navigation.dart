import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../controllers/owner/owner_navigation_controller.dart';
import 'home/owner_home_screen.dart';
import 'bookings/owner_bookings_screen.dart';
import 'add_service/add_service_screen.dart';
import 'add_barber/add_barber_screen.dart';
import 'profile/owner_profile_screen.dart';

class OwnerBottomNavigation extends StatelessWidget {
  const OwnerBottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    final OwnerNavigationController controller = Get.put(OwnerNavigationController());

    final List<Widget> pages = [
      const OwnerHomeScreen(),
      const OwnerBookingsScreen(),
      const AddServiceScreen(),
      const AddBarberScreen(),
      const OwnerProfileScreen(),
    ];

    return Scaffold(
      body: Obx(() => pages[controller.currentIndex.value]),
      bottomNavigationBar: Obx(
        () => Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeIndex,
            type: BottomNavigationBarType.fixed,
            backgroundColor: AppConstants.primaryWhite,
            selectedItemColor: AppConstants.primaryBlack,
            unselectedItemColor: AppConstants.mediumGrey,
            selectedLabelStyle: const TextStyle(
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: const TextStyle(
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
            elevation: 0,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.dashboard_outlined),
                activeIcon: Icon(Icons.dashboard),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.calendar_today_outlined),
                activeIcon: Icon(Icons.calendar_today),
                label: 'Bookings',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.add_business_outlined),
                activeIcon: Icon(Icons.add_business),
                label: 'Add Service',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_add_outlined),
                activeIcon: Icon(Icons.person_add),
                label: 'Add Barber',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.store_outlined),
                activeIcon: Icon(Icons.store),
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
