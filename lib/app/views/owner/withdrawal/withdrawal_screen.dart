import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/withdrawal_controller.dart';
import '../../../models/withdrawal_models.dart';

class WithdrawalScreen extends StatelessWidget {
  final double availableBalance;

  const WithdrawalScreen({super.key, required this.availableBalance});

  @override
  Widget build(BuildContext context) {
    final WithdrawalController controller = Get.put(WithdrawalController());

    // Set available balance
    controller.setAvailableBalance(availableBalance);

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(),
      body: _buildBody(controller),
    );
  }

  // App Bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppConstants.primaryBlack,
          size: 20,
        ),
      ),
      title: Text(
        'Withdraw Funds',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  // Body
  Widget _buildBody(WithdrawalController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildLoadingState();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Available Balance Card
              _buildBalanceCard(controller),

              const SizedBox(height: AppConstants.paddingLarge),

              // Withdrawal Method Selection
              _buildWithdrawalMethodSection(controller),

              const SizedBox(height: AppConstants.paddingLarge),

              // Amount Field
              _buildAmountField(controller),

              const SizedBox(height: AppConstants.paddingMedium),

              // Bank Details Section
              _buildBankDetailsSection(controller),

              const SizedBox(height: AppConstants.paddingLarge),

              // Submit Button
              _buildSubmitButton(controller),

              const SizedBox(height: AppConstants.paddingMedium),

              // Terms and Conditions
              _buildTermsText(),
            ],
          ),
        ),
      );
    });
  }

  // Loading State
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              AppConstants.primaryBlack,
            ),
          ),
          SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Processing withdrawal request...',
            style: TextStyle(fontSize: 16, color: AppConstants.mediumGrey),
          ),
        ],
      ),
    );
  }

  // Available Balance Card
  Widget _buildBalanceCard(WithdrawalController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryBlack,
            AppConstants.primaryBlack.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Balance',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryWhite.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Obx(
            () => Text(
              controller.formatCurrency(controller.availableBalance.value),
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.w700,
                color: AppConstants.primaryWhite,
              ),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Enter the amount you want to withdraw',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppConstants.primaryWhite.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Withdrawal Method Selection
  Widget _buildWithdrawalMethodSection(WithdrawalController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Withdrawal Method',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Obx(
          () => Row(
            children: WithdrawalMethod.values.map((method) {
              final isSelected = controller.selectedMethod.value == method;
              return Expanded(
                child: GestureDetector(
                  onTap: () => controller.changeWithdrawalMethod(method),
                  child: Container(
                    margin: const EdgeInsets.only(
                      right: AppConstants.paddingSmall,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppConstants.primaryBlack
                          : AppConstants.lightGrey,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                      border: Border.all(
                        color: isSelected
                            ? AppConstants.primaryBlack
                            : AppConstants.mediumGrey,
                      ),
                    ),
                    child: Text(
                      method.displayName,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? AppConstants.primaryWhite
                            : AppConstants.primaryBlack,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // Amount Field
  Widget _buildAmountField(WithdrawalController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Withdrawal Amount *',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller.amountController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          validator: controller.validateAmount,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
          decoration: InputDecoration(
            hintText: 'Enter amount to withdraw',
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            prefixIcon: const Icon(
              Icons.currency_rupee,
              color: AppConstants.mediumGrey,
              size: 20,
            ),
            filled: true,
            fillColor: AppConstants.lightGrey.withValues(alpha: 0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.primaryBlack),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ],
    );
  }

  // Bank Details Section
  Widget _buildBankDetailsSection(WithdrawalController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Bank Account Details',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        // Account Holder Name
        _buildTextField(
          controller: controller.accountNameController,
          label: 'Account Holder Name *',
          hint: 'Enter account holder name',
          icon: Icons.person,
          validator: (value) =>
              controller.validateRequired(value, 'Account holder name'),
          textCapitalization: TextCapitalization.words,
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        // Bank Account Number
        _buildTextField(
          controller: controller.bankAccountController,
          label: 'Bank Account Number *',
          hint: 'Enter bank account number',
          icon: Icons.account_balance,
          keyboardType: TextInputType.number,
          validator: controller.validateBankAccount,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        // IFSC Code
        _buildTextField(
          controller: controller.ifscCodeController,
          label: 'IFSC Code *',
          hint: 'Enter IFSC code (e.g., SBIN0000123)',
          icon: Icons.code,
          validator: controller.validateIfscCode,
          textCapitalization: TextCapitalization.characters,
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        // UPI ID (Optional)
        _buildTextField(
          controller: controller.upiIdController,
          label: 'UPI ID (Optional)',
          hint: 'Enter UPI ID (e.g., user@paytm)',
          icon: Icons.payment,
          validator: controller.validateUpiId,
        ),
      ],
    );
  }

  // Text Field Builder
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization textCapitalization = TextCapitalization.none,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          textCapitalization: textCapitalization,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.primaryBlack,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            prefixIcon: Icon(icon, color: AppConstants.mediumGrey, size: 20),
            filled: true,
            fillColor: AppConstants.lightGrey.withValues(alpha: 0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.primaryBlack),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ],
    );
  }

  // Submit Button
  Widget _buildSubmitButton(WithdrawalController controller) {
    return Obx(
      () => SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton(
          onPressed: controller.isLoading.value
              ? null
              : controller.submitWithdrawalRequest,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryBlack,
            foregroundColor: AppConstants.primaryWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            elevation: 2,
          ),
          child: controller.isLoading.value
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppConstants.primaryWhite,
                    ),
                  ),
                )
              : Text(
                  'Submit Withdrawal Request',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }

  // Terms and Conditions Text
  Widget _buildTermsText() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Important Notes:',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '• Minimum withdrawal amount: ₹100\n'
            '• Maximum withdrawal amount: ₹50,000 per transaction\n'
            '• Processing time: 1-3 business days\n'
            '• Ensure bank details are correct to avoid delays\n'
            '• Withdrawal requests cannot be cancelled once submitted',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppConstants.darkGrey,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
