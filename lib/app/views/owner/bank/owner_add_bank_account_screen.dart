import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/owner_add_bank_account_controller.dart';
import '../../../models/bank_account_models.dart';

class OwnerAddBankAccountScreen extends StatelessWidget {
  const OwnerAddBankAccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final OwnerAddBankAccountController controller = Get.put(
      OwnerAddBankAccountController(),
    );

    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Error message display
          Obx(() {
            if (controller.showErrorOnScreen.value &&
                controller.errorMessage.value.isNotEmpty) {
              return _buildErrorMessage(controller);
            }
            return const SizedBox.shrink();
          }),

          // Form content
          Expanded(child: _buildForm(controller)),
        ],
      ),
      bottomNavigationBar: _buildSaveButton(controller),
    );
  }

  // App Bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      foregroundColor: AppConstants.primaryBlack,
      elevation: 0,
      title: Text(
        'Add Bank Account',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppConstants.primaryBlack,
        ),
      ),
    );
  }

  // Error Message Display
  Widget _buildErrorMessage(OwnerAddBankAccountController controller) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600, size: 24),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Text(
              controller.errorMessage.value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red.shade700,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              controller.showErrorOnScreen.value = false;
              controller.errorMessage.value = '';
            },
            icon: Icon(Icons.close, color: Colors.red.shade600, size: 20),
          ),
        ],
      ),
    );
  }

  // Form
  Widget _buildForm(OwnerAddBankAccountController controller) {
    return Form(
      key: controller.formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Holder Information
            _buildSectionHeader('Account Holder Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.accountHolderNameController,
              label: 'Account Holder Name',
              hint: 'Enter full name as per bank records',
              icon: Icons.person,
              validator: BankAccountValidator.validateAccountHolderName,
              textCapitalization: TextCapitalization.words,
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.mobileNumberController,
              label: 'Linked Mobile Number',
              hint: 'Enter mobile number linked to account',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              validator: BankAccountValidator.validateMobileNumber,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Bank Information
            _buildSectionHeader('Bank Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.bankNameController,
              label: 'Bank Name',
              hint: 'Enter bank name (e.g., HDFC Bank)',
              icon: Icons.account_balance,
              validator: BankAccountValidator.validateBankName,
              textCapitalization: TextCapitalization.words,
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.accountNumberController,
              label: 'Account Number',
              hint: 'Enter bank account number',
              icon: Icons.credit_card,
              keyboardType: TextInputType.number,
              validator: BankAccountValidator.validateAccountNumber,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(18),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.confirmAccountNumberController,
              label: 'Confirm Account Number',
              hint: 'Re-enter account number',
              icon: Icons.credit_card,
              keyboardType: TextInputType.number,
              validator: controller.validateConfirmAccountNumber,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(18),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.ifscCodeController,
              label: 'IFSC Code',
              hint: 'Enter IFSC code (e.g., HDFC0000123)',
              icon: Icons.qr_code,
              validator: BankAccountValidator.validateIFSC,
              textCapitalization: TextCapitalization.characters,
              onChanged: controller.formatIFSCCode,
              inputFormatters: [
                LengthLimitingTextInputFormatter(11),
                FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // UPI Information
            _buildSectionHeader('UPI Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.upiIdController,
              label: 'UPI ID',
              hint: 'Enter UPI ID (e.g., user@bank)',
              icon: Icons.payment,
              validator: BankAccountValidator.validateUPI,
              keyboardType: TextInputType.emailAddress,
              onChanged: controller.formatUPIId,
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Preferred Withdrawal Method
            _buildSectionHeader('Preferred Withdrawal Method'),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildWithdrawalMethodSelector(controller),

            const SizedBox(height: 100), // Space for bottom button
          ],
        ),
      ),
    );
  }

  // Section Header
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppConstants.primaryBlack,
      ),
    );
  }

  // Text Field
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization? textCapitalization,
    void Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          inputFormatters: inputFormatters,
          textCapitalization: textCapitalization ?? TextCapitalization.none,
          onChanged: onChanged,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.primaryBlack,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            prefixIcon: Icon(icon, color: AppConstants.mediumGrey, size: 20),
            filled: true,
            fillColor: AppConstants.primaryWhite,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(
                color: AppConstants.primaryBlack,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ],
    );
  }

  // Withdrawal Method Selector
  Widget _buildWithdrawalMethodSelector(
    OwnerAddBankAccountController controller,
  ) {
    return Obx(
      () => Column(
        children: controller.withdrawalMethods.map((method) {
          final isSelected =
              controller.selectedWithdrawalMethod.value == method;
          return GestureDetector(
            onTap: () => controller.changeWithdrawalMethod(method),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppConstants.primaryBlack.withValues(alpha: 0.05)
                    : AppConstants.primaryWhite,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                border: Border.all(
                  color: isSelected
                      ? AppConstants.primaryBlack
                      : AppConstants.lightGrey,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected
                            ? AppConstants.primaryBlack
                            : AppConstants.mediumGrey,
                        width: 2,
                      ),
                      color: isSelected
                          ? AppConstants.primaryBlack
                          : AppConstants.primaryWhite,
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            size: 12,
                            color: AppConstants.primaryWhite,
                          )
                        : null,
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          method.displayName,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                        Text(
                          method == WithdrawalMethod.upi
                              ? 'Instant transfer via UPI'
                              : 'Transfer to bank account (1-2 business days)',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: AppConstants.mediumGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    method == WithdrawalMethod.upi
                        ? Icons.flash_on
                        : Icons.account_balance,
                    color: isSelected
                        ? AppConstants.primaryBlack
                        : AppConstants.mediumGrey,
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // Save Button
  Widget _buildSaveButton(OwnerAddBankAccountController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Obx(
        () => ElevatedButton(
          onPressed: controller.isSaving.value
              ? null
              : controller.saveBankAccount,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryBlack,
            foregroundColor: AppConstants.primaryWhite,
            padding: const EdgeInsets.symmetric(
              vertical: AppConstants.paddingMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            elevation: 0,
          ),
          child: controller.isSaving.value
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Adding Account...',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                )
              : Text(
                  'Add Bank Account',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }
}
