import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/add_service_controller.dart';
import '../../../models/owner_service_models.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_text_field.dart';

class AddServiceScreen extends StatefulWidget {
  const AddServiceScreen({super.key});

  @override
  State<AddServiceScreen> createState() => _AddServiceScreenState();
}

class _AddServiceScreenState extends State<AddServiceScreen>
    with TickerProviderStateMixin {
  late final AddServiceController controller;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize controller with fresh instance
    Get.delete<AddServiceController>();
    controller = Get.put(AddServiceController());

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    Get.delete<AddServiceController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              // Service List with RefreshIndicator
              Expanded(
                child: RefreshIndicator(
                  onRefresh: controller.loadServices,
                  color: AppConstants.primaryBlack,
                  child: Obx(
                    () => controller.isLoading.value
                        ? _buildLoadingShimmer()
                        : _buildServiceList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build app bar with gradient action button
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      centerTitle: false,
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      title: Text(
        'Manage Services',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: AppConstants.primaryBlack,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: AppConstants.paddingMedium),
          child: GestureDetector(
            onTap: () {
              controller.showAddServiceModal();
              _showAddServiceModal();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: AppConstants.primaryBlack,
                // gradient: const LinearGradient(
                //   colors: [AppConstants.primaryBlack, AppConstants.mediumGrey],
                //   begin: Alignment.topLeft,
                //   end: Alignment.bottomRight,
                // ),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.add,
                    color: AppConstants.primaryWhite,
                    size: 18,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    'Add Service',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryWhite,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build service list
  Widget _buildServiceList() {
    if (controller.services.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingSmall),
      itemCount: controller.services.length,
      itemBuilder: (context, index) {
        final service = controller.services[index];
        return _buildServiceTile(service, index);
      },
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.design_services_outlined,
            size: 80,
            color: AppConstants.mediumGrey,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'No Services Added Yet',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Add your first service to get started',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
          ),
          const SizedBox(height: AppConstants.paddingXLarge),
          CustomButton(
            text: 'Add Service',
            onPressed: () {
              controller.showAddServiceModal();
              _showAddServiceModal();
            },
            width: 200,
          ),
        ],
      ),
    );
  }

  /// Build service tile with animations - Production level design
  Widget _buildServiceTile(GetAllServiceModel service, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.paddingLarge),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.04),
              blurRadius: 6,
              offset: const Offset(0, 1),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // Service Image with enhanced styling
              _buildEnhancedServiceImage(service),

              const SizedBox(width: 16),

              // Service Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Service Name
                    Text(
                      service.displayName,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppConstants.primaryBlack,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 6),

                    // Category Badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppConstants.lightGrey,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        service.displayCategory,
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: AppConstants.mediumGrey,
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Price and Duration Row
                    Row(
                      children: [
                        // Price
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: AppConstants.primaryBlack,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            service.displayPrice,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppConstants.primaryWhite,
                            ),
                          ),
                        ),

                        const SizedBox(width: 12),

                        // Duration
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: AppConstants.mediumGrey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              service.displayDuration,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: AppConstants.mediumGrey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // Action Buttons
              _buildEnhancedActionButtons(service),
            ],
          ),
        ),
      ),
    );
  }

  /// Build enhanced service image for production design
  Widget _buildEnhancedServiceImage(GetAllServiceModel service) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppConstants.lightGrey,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: service.hasImage && _isValidImageUrl(service.images)
          ? ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: CachedNetworkImage(
                imageUrl: service.images,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  decoration: BoxDecoration(
                    color: AppConstants.lightGrey,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.design_services_outlined,
                    color: AppConstants.mediumGrey,
                    size: 32,
                  ),
                ),
                errorWidget: (context, url, error) {
                  log('CachedNetworkImage error for URL: $url, Error: $error');
                  return Container(
                    decoration: BoxDecoration(
                      color: AppConstants.lightGrey,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.broken_image_outlined,
                      color: AppConstants.mediumGrey,
                      size: 32,
                    ),
                  );
                },
              ),
            )
          : Container(
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.design_services_outlined,
                color: AppConstants.mediumGrey,
                size: 32,
              ),
            ),
    );
  }

  /// Build enhanced action buttons for production design
  Widget _buildEnhancedActionButtons(GetAllServiceModel service) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Edit button
        GestureDetector(
          onTap: () {
            controller.editService(service);
            _showAddServiceModal();
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppConstants.primaryBlack,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.edit_outlined,
              color: AppConstants.primaryWhite,
              size: 20,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Delete button
        Obx(
          () => GestureDetector(
            onTap: controller.isDeletingService.value
                ? null
                : () => controller.deleteService(service),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: controller.isDeletingService.value
                    ? AppConstants.mediumGrey
                    : const Color(0xFFFF4757),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color:
                        (controller.isDeletingService.value
                                ? AppConstants.mediumGrey
                                : const Color(0xFFFF4757))
                            .withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: controller.isDeletingService.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppConstants.primaryWhite,
                        ),
                      ),
                    )
                  : const Icon(
                      Icons.delete_outline,
                      color: AppConstants.primaryWhite,
                      size: 20,
                    ),
            ),
          ),
        ),
      ],
    );
  }

  /// Show add service modal bottom sheet
  void _showAddServiceModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAddServiceModal(),
    );
  }

  /// Build add service modal
  Widget _buildAddServiceModal() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppConstants.mediumGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    controller.isEditing.value
                        ? 'Edit Service'
                        : 'Add New Service',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(
                    Icons.close,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ],
            ),
          ),

          // Form content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image selection section
                  _buildImageSelectionSection(),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Form fields
                  _buildFormFields(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Add/Update button
                  Obx(
                    () => CustomButton(
                      text: controller.isEditing.value
                          ? 'Update Service'
                          : 'Add Service',
                      onPressed: controller.isEditing.value
                          ? _handleUpdateService
                          : _handleAddService,
                      isLoading: controller.isAddingService.value,
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading shimmer effect
  Widget _buildLoadingShimmer() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.lightGrey,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          ),
          child: Row(
            children: [
              // Image placeholder
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppConstants.mediumGrey,
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              // Content placeholder
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 16,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppConstants.mediumGrey,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    Container(
                      height: 14,
                      width: 120,
                      decoration: BoxDecoration(
                        color: AppConstants.mediumGrey,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),
              // Action buttons placeholder
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppConstants.mediumGrey,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppConstants.mediumGrey,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build image selection section
  Widget _buildImageSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Image',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        // Predefined images
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount:
                controller.predefinedImages.length +
                2, // +2 for camera and gallery
            itemBuilder: (context, index) {
              if (index == 0) {
                // Camera option
                return _buildImageOption(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: () => controller.selectImage(ImageSource.camera),
                );
              } else if (index == 1) {
                // Gallery option
                return _buildImageOption(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () => controller.selectImage(ImageSource.gallery),
                );
              } else {
                // Predefined images
                final imageIndex = index - 2;
                return _buildPredefinedImageOption(imageIndex);
              }
            },
          ),
        ),

        // Selected image preview with upload progress
        Obx(() {
          if (controller.isUploadingImage.value) {
            return _buildUploadProgressWidget();
          } else if (controller.selectedImage.value != null) {
            return _buildImagePreview(
              child: Image.file(
                controller.selectedImage.value!,
                fit: BoxFit.cover,
              ),
            );
          } else if (controller.selectedImageUrl.value.isNotEmpty &&
              _isValidImageUrl(controller.selectedImageUrl.value)) {
            return _buildImagePreview(
              child: CachedNetworkImage(
                imageUrl: controller.selectedImageUrl.value,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildImagePlaceholder(),
                errorWidget: (context, url, error) {
                  log('Selected image error for URL: $url, Error: $error');
                  return _buildImageError();
                },
              ),
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  /// Build image option (camera/gallery)
  Widget _buildImageOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
        width: 80,
        decoration: BoxDecoration(
          color: AppConstants.lightGrey,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(color: AppConstants.mediumGrey),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: AppConstants.primaryBlack, size: 24),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppConstants.primaryBlack,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build predefined image option
  Widget _buildPredefinedImageOption(int index) {
    return Obx(
      () => GestureDetector(
        onTap: () => controller.selectPredefinedImage(index),
        child: Container(
          margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            border: Border.all(
              color: controller.selectedPredefinedImageIndex.value == index
                  ? AppConstants.primaryBlack
                  : AppConstants.mediumGrey,
              width: controller.selectedPredefinedImageIndex.value == index
                  ? 2
                  : 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            child: _isValidImageUrl(controller.predefinedImages[index])
                ? CachedNetworkImage(
                    imageUrl: controller.predefinedImages[index],
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppConstants.lightGrey,
                      child: const Icon(
                        Icons.image,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                    errorWidget: (context, url, error) {
                      log(
                        'Predefined image error for URL: $url, Error: $error',
                      );
                      return Container(
                        color: AppConstants.lightGrey,
                        child: const Icon(
                          Icons.broken_image,
                          color: AppConstants.mediumGrey,
                        ),
                      );
                    },
                  )
                : Container(
                    color: AppConstants.lightGrey,
                    child: const Icon(
                      Icons.image_not_supported,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  /// Build form fields
  Widget _buildFormFields() {
    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          // Service Name
          CustomTextField(
            controller: controller.serviceNameController,
            focusNode: controller.serviceNameFocus,
            labelText: 'Service Name *',
            hintText: 'Enter service name (e.g., Hair Cut, Beard Trim)',
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) => controller.categoryFocus.requestFocus(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Service name is required';
              }
              if (value.trim().length < 2) {
                return 'Service name must be at least 2 characters';
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // Category Name
          CustomTextField(
            controller: controller.categoryController,
            focusNode: controller.categoryFocus,
            labelText: 'Category Name *',
            hintText: 'Enter category name (e.g., Hair, Beard, Facial)',
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) => controller.priceFocus.requestFocus(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Category name is required';
              }
              if (value.trim().length < 2) {
                return 'Category name must be at least 2 characters';
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // Price and Duration Row
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: controller.priceController,
                  focusNode: controller.priceFocus,
                  labelText: 'Price (₹) *',
                  hintText: '500',
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) =>
                      controller.durationFocus.requestFocus(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Price is required';
                    }
                    final price = double.tryParse(value);
                    if (price == null) {
                      return 'Enter valid price';
                    }
                    if (price <= 0) {
                      return 'Price must be greater than 0';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(width: AppConstants.paddingMedium),

              Expanded(
                child: CustomTextField(
                  controller: controller.durationController,
                  focusNode: controller.durationFocus,
                  labelText: 'Duration (min) *',
                  hintText: '30',
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.done,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Duration is required';
                    }
                    final duration = int.tryParse(value);
                    if (duration == null) {
                      return 'Enter valid duration';
                    }
                    if (duration <= 0) {
                      return 'Duration must be greater than 0';
                    }
                    if (duration > 480) {
                      return 'Duration cannot exceed 480 minutes';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Handle add service with modal closing
  Future<void> _handleAddService() async {
    log('AddServiceScreen: Handling add service');

    try {
      await controller.addService();

      // The controller already handles modal closing and list refresh
      // No need to manually close the modal here as it's handled in the controller
      log('AddServiceScreen: Add service completed');
    } catch (e) {
      log('AddServiceScreen: Error adding service: $e');
      // Error handling is already done in the controller
    }
  }

  /// Handle update service with modal closing
  Future<void> _handleUpdateService() async {
    log('AddServiceScreen: Handling update service');

    try {
      await controller.updateService();

      // The controller already handles modal closing and list refresh
      // No need to manually close the modal here as it's handled in the controller
      log('AddServiceScreen: Update service completed');
    } catch (e) {
      log('AddServiceScreen: Error updating service: $e');
      // Error handling is already done in the controller
    }
  }

  /// Build upload progress widget
  Widget _buildUploadProgressWidget() {
    return Container(
      margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(color: AppConstants.primaryBlack, width: 2),
        color: AppConstants.lightGrey.withValues(alpha: 0.3),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.cloud_upload,
            size: 32,
            color: AppConstants.primaryBlack,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Uploading Image...',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Obx(() {
            return Container(
              width: 200,
              height: 6,
              decoration: BoxDecoration(
                color: AppConstants.mediumGrey,
                borderRadius: BorderRadius.circular(3),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: controller.uploadProgress.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppConstants.primaryBlack,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
            );
          }),
          const SizedBox(height: AppConstants.paddingSmall),
          Obx(() {
            return Text(
              '${(controller.uploadProgress.value * 100).toInt()}%',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppConstants.mediumGrey,
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Build image preview container
  Widget _buildImagePreview({required Widget child}) {
    return Container(
      margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
      height: 120,
      width: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(color: AppConstants.primaryBlack, width: 2),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        child: child,
      ),
    );
  }

  /// Build image placeholder
  Widget _buildImagePlaceholder() {
    return Container(
      color: AppConstants.lightGrey,
      child: const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryBlack,
          strokeWidth: 2,
        ),
      ),
    );
  }

  /// Build image error widget
  Widget _buildImageError() {
    return Container(
      color: AppConstants.lightGrey,
      child: const Center(
        child: Icon(
          Icons.broken_image_outlined,
          color: AppConstants.mediumGrey,
          size: 32,
        ),
      ),
    );
  }

  /// Validate if the image URL is valid
  bool _isValidImageUrl(String url) {
    if (url.isEmpty) return false;

    // Check if it's a valid HTTP/HTTPS URL
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.hasAuthority;
    } catch (e) {
      log('Invalid URL: $url, Error: $e');
      return false;
    }
  }
}
