import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/owner_earnings_history_controller.dart';
import '../../../models/earnings_history_models.dart';

class OwnerEarningsHistoryScreen extends StatelessWidget {
  const OwnerEarningsHistoryScreen({super.key});

  //––– DESIGN CONSTANTS ––––––––––––––––––––––––––––––––––––––
  static const double kFontXS = 10;
  static const double kFontSM = 12;
  static const double kFontMD = 14;
  static const double kHPad = 10;

  @override
  Widget build(BuildContext context) {
    final OwnerEarningsHistoryController controller = Get.put(
      OwnerEarningsHistoryController(),
    );

    return Scaffold(
      backgroundColor: AppConstants.light<PERSON>rey,
      appBar: _buildAppBar(controller),
      body: Obx(() {
        if (controller.isLoading.value) return _buildLoadingState();
        if (controller.hasError.value) return _buildErrorState(controller);
        if (!controller.hasData) return _buildEmptyState();
        return _buildContent(controller);
      }),
    );
  }

  //––– APP-BAR ––––––––––––––––––––––––––––––––––––––––––––––––
  PreferredSizeWidget _buildAppBar(OwnerEarningsHistoryController c) {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      foregroundColor: AppConstants.primaryBlack,
      elevation: 0,
      title: Text(
        'Earnings History',
        style: GoogleFonts.poppins(
          fontSize: kFontMD,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.arrow_back_ios, size: 18),
      ),
      actions: [
        PopupMenuButton<String>(
          icon: const Icon(Icons.download, size: 18),
          onSelected: (v) {
            if (v == 'previous_slip') _showPreviousSlipDialog(c);
            if (v == 'monthly_report') _showMonthlyReportDialog(c);
          },
          itemBuilder: (context) => [
            _buildMenuItem(Icons.receipt, 'Previous Slip', 'previous_slip'),
            _buildMenuItem(
              Icons.calendar_month,
              'Monthly Report',
              'monthly_report',
            ),
          ],
        ),
      ],
    );
  }

  PopupMenuItem<String> _buildMenuItem(IconData i, String t, String v) {
    return PopupMenuItem(
      value: v,
      child: Row(
        children: [
          Icon(i, size: 14),
          const SizedBox(width: 6),
          Text(
            t,
            style: GoogleFonts.poppins(
              fontSize: kFontSM,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  //––– STATES ––––––––––––––––––––––––––––––––––––––––––––––––
  Widget _buildLoadingState() => const Center(
    child: CircularProgressIndicator(color: AppConstants.primaryBlack),
  );

  Widget _buildErrorState(OwnerEarningsHistoryController c) => _StateCard(
    icon: Icons.error_outline,
    title: 'Error Loading Data',
    subtitle: c.errorMessage.value,
    btnLabel: 'Retry',
    onPressed: c.loadEarningsHistory,
  );

  Widget _buildEmptyState() => _StateCard(
    icon: Icons.account_balance_wallet_outlined,
    title: 'No Earnings History',
    subtitle:
        'Your earnings history will appear here once you start receiving payments.',
  );

  //––– MAIN CONTENT ––––––––––––––––––––––––––––––––––––––––––
  Widget _buildContent(OwnerEarningsHistoryController c) {
    return RefreshIndicator(
      onRefresh: c.refreshEarningsHistory,
      color: AppConstants.primaryBlack,
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(child: _buildTotalEarningsCard(c)),
          SliverToBoxAdapter(child: _buildTabBar(c)),
          Obx(
            () => c.hasTransactions
                ? _buildTransactionList(c)
                : SliverToBoxAdapter(child: _buildEmptyTransactionsList(c)),
          ),
          const SliverToBoxAdapter(child: SizedBox(height: kHPad * 2)),
        ],
      ),
    );
  }

  //––– TOTAL CARD ––––––––––––––––––––––––––––––––––––––––––––
  Widget _buildTotalEarningsCard(OwnerEarningsHistoryController c) {
    return Container(
      margin: const EdgeInsets.all(kHPad),
      padding: const EdgeInsets.all(kHPad),
      decoration: BoxDecoration(
        gradient: AppConstants.primaryGradient,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 8)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _title('Total Earnings'),
              const Icon(Icons.trending_up, color: Colors.white, size: 16),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            c.formatCurrency(c.totalEarnings),
            style: GoogleFonts.poppins(
              fontSize: kFontMD,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _stat(
                'Total',
                c.totalTransactions.toString(),
                Icons.receipt_long,
              ),
              _stat(
                'Completed',
                c.completedCount.toString(),
                Icons.check_circle,
              ),
              _stat('Pending', c.pendingCount.toString(), Icons.access_time),
              _stat('Rejected', c.rejectedCount.toString(), Icons.cancel),
            ],
          ),
        ],
      ),
    );
  }

  Widget _title(String t) => Text(
    t,
    style: GoogleFonts.poppins(
      fontSize: kFontSM,
      fontWeight: FontWeight.w500,
      color: Colors.white,
    ),
  );

  Widget _stat(String l, String v, IconData i) {
    return Expanded(
      child: Column(
        children: [
          Icon(i, size: 14, color: Colors.white70),
          const SizedBox(height: 2),
          Text(
            v,
            style: GoogleFonts.poppins(
              fontSize: kFontSM,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          Text(
            l,
            style: GoogleFonts.poppins(
              fontSize: kFontXS,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  //––– TAB BAR (overflow-safe) –––––––––––––––––––––––––––––––
  Widget _buildTabBar(OwnerEarningsHistoryController c) {
    return Container(
      margin: const EdgeInsets.only(left: 0, right: 0), // remove extra left gap
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4)],
      ),
      child: TabBar(
        isScrollable: true,
        controller: c.tabController,

        // ✅ Remove the default underline
        indicatorColor: Colors.transparent,

        // ✅ Remove the bottom divider
        dividerColor: Colors.transparent,

        // ✅ Align tabs to start from left
        tabAlignment: TabAlignment.start,

        // ✅ Custom gradient background for active tab
        indicator: BoxDecoration(
          gradient: AppConstants.primaryGradient,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),

        labelColor: Colors.white,
        unselectedLabelColor: Colors.black87,

        labelStyle: GoogleFonts.poppins(
          fontSize: kFontSM,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontSize: kFontSM,
          fontWeight: FontWeight.w500,
        ),

        tabs: TransactionStatus.values.map((s) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(c.getStatusIcon(s), size: 14),
                  const SizedBox(width: 4),
                  Text(s.displayName),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  //––– TRANSACTION LIST ––––––––––––––––––––––––––––––––––––––
  SliverList _buildTransactionList(OwnerEarningsHistoryController c) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, i) => _buildTransactionItem(c.filteredTransactions[i]),
        childCount: c.filteredTransactions.length,
      ),
    );
  }

  Widget _buildTransactionItem(WithdrawalTransaction t) {
    final statusColor = _getStatusColor(t.transactionStatus);
    return Container(
      margin: const EdgeInsets.fromLTRB(kHPad, 6, kHPad, 6),
      padding: const EdgeInsets.all(kHPad),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 3)],
      ),
      child: Row(
        children: [
          // icon
          CircleAvatar(
            radius: 20,
            backgroundColor: statusColor.withOpacity(.1),
            child: Icon(
              _getStatusIcon(t.transactionStatus),
              color: statusColor,
              size: 16,
            ),
          ),
          const SizedBox(width: kHPad),
          // details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '₹${t.withDrawalAmount.toStringAsFixed(0)}',
                      style: GoogleFonts.poppins(
                        fontSize: kFontMD,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        t.status.toUpperCase(),
                        style: GoogleFonts.poppins(
                          fontSize: kFontXS,
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.calendar_today,
                      size: 12,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      t.formattedDate,
                      style: GoogleFonts.poppins(
                        fontSize: kFontXS,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Icon(Icons.access_time, size: 12, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      t.formattedTime,
                      style: GoogleFonts.poppins(
                        fontSize: kFontXS,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.account_balance,
                      size: 12,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      t.paymentMethod,
                      style: GoogleFonts.poppins(
                        fontSize: kFontXS,
                        color: Colors.grey,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      t.referenceNumber,
                      style: GoogleFonts.poppins(
                        fontSize: kFontXS,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTransactionsList(OwnerEarningsHistoryController c) =>
      _StateCard(
        icon: Icons.receipt_long_outlined,
        title: 'No ${c.selectedStatus.value.displayName} Transactions',
        subtitle: 'No transactions found for the selected filter.',
      );

  //––– DIALOGS (unchanged except for text styles) ––––––––––––

  void _showPreviousSlipDialog(OwnerEarningsHistoryController c) {
    /* ... */
  }
  void _showMonthlyReportDialog(OwnerEarningsHistoryController c) {
    /* ... */
  }

  //––– HELPERS –––––––––––––––––––––––––––––––––––––––––––––––
  Color _getStatusColor(TransactionStatus s) => switch (s) {
    TransactionStatus.completed => Colors.green,
    TransactionStatus.pending => Colors.orange,
    TransactionStatus.rejected => Colors.red,
    _ => Colors.grey,
  };

  IconData _getStatusIcon(TransactionStatus s) => switch (s) {
    TransactionStatus.completed => Icons.check_circle,
    TransactionStatus.pending => Icons.access_time,
    TransactionStatus.rejected => Icons.cancel,
    _ => Icons.list,
  };
}

//––– GENERIC STATE CARD (loading / error / empty) ––––––––––––
class _StateCard extends StatelessWidget {
  const _StateCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.btnLabel,
    this.onPressed,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final String? btnLabel;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(OwnerEarningsHistoryScreen.kHPad * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: AppConstants.mediumGrey),
            const SizedBox(height: 10),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: OwnerEarningsHistoryScreen.kFontMD,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: OwnerEarningsHistoryScreen.kFontSM,
                color: AppConstants.mediumGrey,
              ),
            ),
            if (btnLabel != null) ...[
              const SizedBox(height: 12),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                onPressed: onPressed,
                child: Text(
                  btnLabel!,
                  style: GoogleFonts.poppins(
                    fontSize: OwnerEarningsHistoryScreen.kFontSM,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
