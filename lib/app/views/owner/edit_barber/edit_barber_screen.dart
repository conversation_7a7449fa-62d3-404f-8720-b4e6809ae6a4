import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/edit_barber_controller.dart';
import '../../../widgets/enhanced_barber_image_picker.dart';

class EditBarberScreen extends StatelessWidget {
  final String barberId;

  const EditBarberScreen({super.key, required this.barberId});

  @override
  Widget build(BuildContext context) {
    // Initialize controller with barber ID
    final controller = Get.put(EditBarberController());

    // Initialize with barber ID
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.initializeWithBarberId(barberId);
    });

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(controller),
      body: Obx(() {
        if (controller.isLoading.value) {
          return _buildLoadingState();
        }

        if (controller.currentBarber.value == null) {
          return _buildErrorState(controller);
        }

        return _buildEditForm(controller);
      }),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading barber details...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(EditBarberController controller) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.black87),
        onPressed: () => Get.back(),
      ),
      title: Text(
        'Edit Barber',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildErrorState(EditBarberController controller) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error Icon with placeholder image
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(60),
              border: Border.all(color: AppConstants.primaryGrey, width: 2),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person_off,
                  size: 48,
                  color: AppConstants.mediumGrey,
                ),
                const SizedBox(height: 8),
                Text(
                  'No Image',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppConstants.mediumGrey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.paddingXLarge),

          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.withValues(alpha: 0.7),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          Text(
            'Failed to load barber details',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          Obx(
            () => Text(
              controller.errorMessage.value.isNotEmpty
                  ? controller.errorMessage.value
                  : 'Unable to fetch barber information. Please check your connection and try again.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          Text(
            'Barber ID: $barberId',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppConstants.mediumGrey,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.paddingXLarge),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () => Get.back(),
                icon: const Icon(Icons.arrow_back),
                label: Text(
                  'Go Back',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.mediumGrey,
                  foregroundColor: AppConstants.primaryWhite,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => controller.loadBarberDetails(),
                icon: const Icon(Icons.refresh),
                label: Text(
                  'Retry',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryBlack,
                  foregroundColor: AppConstants.primaryWhite,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditForm(EditBarberController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Image
          Center(
            child: Obx(
              () => EnhancedBarberImagePicker(
                onImagePicked: controller.setProfileImage,
                currentImage: controller.selectedImage.value,
                imageUrl: controller.selectedImageUrl.value.isNotEmpty
                    ? controller.selectedImageUrl.value
                    : null,
                isUploading: controller.isUploadingImage.value,
                uploadProgress: controller.uploadProgress.value,
                size: 120,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Form Fields
          _buildTextField(
            controller: controller.firstNameController,
            label: 'First Name',
            hint: 'Enter first name',
            icon: Icons.person,
            isRequired: true,
          ),

          const SizedBox(height: 16),

          _buildTextField(
            controller: controller.lastNameController,
            label: 'Last Name',
            hint: 'Enter last name',
            icon: Icons.person_outline,
            isRequired: true,
          ),

          const SizedBox(height: 16),

          _buildTextField(
            controller: controller.mobileController,
            label: 'Mobile Number',
            hint: 'Enter mobile number (optional)',
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
          ),

          const SizedBox(height: 16),

          _buildTextField(
            controller: controller.bioController,
            label: 'Bio',
            hint: 'Enter bio/specialization',
            icon: Icons.description,
            maxLines: 3,
          ),

          const SizedBox(height: 20),

          // Availability Toggle
          _buildAvailabilityToggle(controller),

          const SizedBox(height: 20),

          // Services Selection
          _buildServicesSelection(controller),

          const SizedBox(height: 32),

          // Update Button
          _buildUpdateButton(controller),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildAvailabilityToggle(EditBarberController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.access_time, color: Colors.green[700], size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Availability Status',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Obx(
                  () => Text(
                    controller.isAvailable.value
                        ? 'Available for bookings'
                        : 'Not available',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: controller.isAvailable.value
                          ? Colors.green[700]
                          : Colors.red[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Obx(
            () => Switch(
              value: controller.isAvailable.value,
              onChanged: (value) => controller.isAvailable.value = value,
              activeColor: Colors.green,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServicesSelection(EditBarberController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.content_cut,
                  color: Colors.blue[700],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Services',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Select services this barber can provide',
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          Obx(() {
            if (controller.availableServices.isEmpty) {
              return Text(
                'No services available',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppConstants.mediumGrey,
                ),
              );
            }

            return Wrap(
              spacing: 8,
              runSpacing: 8,
              children: controller.availableServices.map((service) {
                return Obx(() {
                  final isSelected = controller.selectedServiceIds.contains(
                    service.id,
                  );
                  return FilterChip(
                    label: Text(service.name),
                    selected: isSelected,
                    onSelected: (_) =>
                        controller.toggleServiceSelection(service.id),
                    selectedColor: AppConstants.primaryBlack.withValues(
                      alpha: 0.1,
                    ),
                    checkmarkColor: AppConstants.primaryBlack,
                  );
                });
              }).toList(),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildUpdateButton(EditBarberController controller) {
    return Obx(
      () => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: controller.isUpdating.value
              ? null
              : controller.updateBarber,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black87,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: controller.isUpdating.value
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  'Update Barber',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines,
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.black87),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              prefixIcon: Icon(icon, color: Colors.grey[600], size: 20),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
