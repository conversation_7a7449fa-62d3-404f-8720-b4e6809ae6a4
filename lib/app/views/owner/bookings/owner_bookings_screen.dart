import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../widgets/custom_booking_app_bar.dart';
import '../../../controllers/appointment_management_controller.dart';
import '../../../widgets/appointment_tabs.dart';

class OwnerBookingsScreen extends StatefulWidget {
  const OwnerBookingsScreen({super.key});

  @override
  State<OwnerBookingsScreen> createState() => _OwnerBookingsScreenState();
}

class _OwnerBookingsScreenState extends State<OwnerBookingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AppointmentManagementController controller = Get.put(
      AppointmentManagementController(),
    );

    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      appBar: CustomBookingAppBar(
        title: 'Bookings Management',
        showBackButton: false,
        controller: controller,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Custom Tab Bar
            _buildCustomTabBar(controller),

            // Tab Content
            Expanded(
              child: Obx(() {
                if (controller.hasError.value) {
                  return _buildErrorState(controller);
                }

                return RefreshIndicator(
                  onRefresh: controller.refreshAllData,
                  color: AppConstants.primaryBlack,
                  backgroundColor: AppConstants.primaryWhite,
                  displacement: 40,
                  strokeWidth: 2,
                  child: TabBarView(
                    controller: controller.tabController,
                    children: [
                      PendingAppointmentsTab(controller: controller),
                      ApprovedAppointmentsTab(controller: controller),
                      OngoingAppointmentsTab(controller: controller),
                      CompletedAppointmentsTab(controller: controller),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  // Custom Tab Bar
  Widget _buildCustomTabBar(AppointmentManagementController controller) {
    return Container(
      color: AppConstants.primaryWhite,
      child: Column(
        children: [
          // Refresh indicator bar
          Obx(() {
            if (controller.isRefreshing.value) {
              return SizedBox(
                height: 2,
                child: LinearProgressIndicator(
                  backgroundColor: AppConstants.lightGrey,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppConstants.primaryBlack,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
          // Tab bar
          Obx(
            () => TabBar(
              controller: controller.tabController,
              labelColor: AppConstants.primaryBlack,
              unselectedLabelColor: AppConstants.mediumGrey,
              indicatorColor: AppConstants.primaryBlack,
              indicatorWeight: 2,
              isScrollable: true, // Allow scrolling to prevent overflow
              tabAlignment: TabAlignment.start, // Align tabs to start from left
              labelStyle: GoogleFonts.poppins(
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: GoogleFonts.poppins(
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
              dividerColor: Colors.transparent, // Remove divider line
              tabs: [
                _buildTabWithBadge(
                  'Pending',
                  controller.pendingAppointments.length,
                  Colors.orange,
                ),
                _buildTabWithBadge(
                  'Approved',
                  controller.approvedAppointments.length,
                  AppConstants.primaryBlack,
                ),
                _buildTabWithBadge(
                  'Ongoing',
                  controller.ongoingAppointments.length,
                  Colors.green,
                ),
                _buildTabWithBadge(
                  'Completed',
                  controller.completedAppointments.length,
                  Colors.grey,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Tab with badge
  Widget _buildTabWithBadge(String title, int count, Color badgeColor) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
              decoration: BoxDecoration(
                color: badgeColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                count.toString(),
                style: GoogleFonts.poppins(
                  fontSize: 9,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryWhite,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Error State
  Widget _buildErrorState(AppointmentManagementController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: AppConstants.mediumGrey),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Something went wrong',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              controller.errorMessage.value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            ElevatedButton(
              onPressed: controller.refreshAllData,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                  vertical: AppConstants.paddingMedium,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                ),
              ),
              child: Text(
                'Try Again',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
