import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/owner_edit_profile_controller.dart';

class OwnerEditProfileScreen extends StatelessWidget {
  const OwnerEditProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final OwnerEditProfileController controller = Get.put(
      OwnerEditProfileController(),
    );

    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      appBar: _buildAppBar(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return _buildLoadingState();
        }
        return _buildEditForm(controller);
      }),
      bottomNavigationBar: _buildSaveButton(controller),
    );
  }

  // App Bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      foregroundColor: AppConstants.primaryBlack,
      elevation: 0,
      title: Text(
        'Edit Profile',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppConstants.primaryBlack,
        ),
      ),
    );
  }

  // Loading State
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: AppConstants.primaryBlack),
    );
  }

  // Edit Form
  Widget _buildEditForm(OwnerEditProfileController controller) {
    return Form(
      key: controller.formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Personal Information Section
            _buildSectionHeader('Personal Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: controller.firstNameController,
                    label: 'First Name',
                    hint: 'Enter first name',
                    icon: Icons.person,
                    validator: (value) =>
                        controller.validateRequired(value, 'First name'),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildTextField(
                    controller: controller.lastNameController,
                    label: 'Last Name',
                    hint: 'Enter last name',
                    icon: Icons.person_outline,
                    validator: (value) =>
                        controller.validateRequired(value, 'Last name'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildVerifiedTextField(
              controller: controller.salonEmailController,
              label: 'Email Address',
              hint: 'Enter email address',
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              validator: controller.validateEmail,
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Business Information Section
            _buildSectionHeader('Business Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.salonNameController,
              label: 'Salon Name',
              hint: 'Enter salon name',
              icon: Icons.store,
              validator: (value) =>
                  controller.validateRequired(value, 'Salon name'),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.salonDescriptionController,
              label: 'Salon Description',
              hint: 'Enter salon description',
              icon: Icons.description,
              maxLines: 3,
              validator: (value) =>
                  controller.validateRequired(value, 'Salon description'),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildVerifiedTextField(
              controller: controller.salonPhoneController,
              label: 'Phone Number',
              hint: 'Enter phone number',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              validator: controller.validatePhone,
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Registration Information Section
            _buildSectionHeader('Registration Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: controller.salonRegIdController,
                    label: 'Salon Registration ID',
                    hint: 'Enter registration ID',
                    icon: Icons.business,
                    validator: (value) =>
                        controller.validateRequired(value, 'Registration ID'),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildTextField(
                    controller: controller.shopNoController,
                    label: 'Shop Number',
                    hint: 'Enter shop number',
                    icon: Icons.store,
                    validator: (value) =>
                        controller.validateRequired(value, 'Shop number'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Working Hours Section
            _buildSectionHeader('Working Hours'),
            const SizedBox(height: AppConstants.paddingMedium),

            // Off Day Selection
            _buildOffDaySelector(controller),

            const SizedBox(height: AppConstants.paddingMedium),

            // Time Selection
            Row(
              children: [
                Expanded(
                  child: _buildTimeSelector(
                    controller,
                    'Start Time',
                    controller.startTime,
                    () => controller.selectStartTime(Get.context!),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildTimeSelector(
                    controller,
                    'End Time',
                    controller.endTime,
                    () => controller.selectEndTime(Get.context!),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Address Section
            _buildSectionHeader('Address Information'),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildTextField(
              controller: controller.areaController,
              label: 'Area',
              hint: 'Enter area/locality',
              icon: Icons.location_on,
              validator: (value) => controller.validateRequired(value, 'Area'),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: controller.cityController,
                    label: 'City',
                    hint: 'Enter city',
                    icon: Icons.location_city,
                    validator: (value) =>
                        controller.validateRequired(value, 'City'),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildTextField(
                    controller: controller.stateController,
                    label: 'State',
                    hint: 'Enter state',
                    icon: Icons.map,
                    validator: (value) =>
                        controller.validateRequired(value, 'State'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: controller.countryController,
                    label: 'Country',
                    hint: 'Enter country',
                    icon: Icons.public,
                    validator: (value) =>
                        controller.validateRequired(value, 'Country'),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildTextField(
                    controller: controller.pincodeController,
                    label: 'Pincode',
                    hint: 'Enter pincode',
                    icon: Icons.pin_drop,
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 100), // Space for bottom button
          ],
        ),
      ),
    );
  }

  // Section Header
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: AppConstants.primaryBlack,
      ),
    );
  }

  // Verified Text Field (for email and phone)
  Widget _buildVerifiedTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.verified, size: 14, color: Colors.green.shade600),
                  const SizedBox(width: 4),
                  Text(
                    'Verified',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          enabled: false, // Disable editing
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            prefixIcon: Icon(icon, color: AppConstants.mediumGrey, size: 20),
            filled: true,
            fillColor: AppConstants.lightGrey.withValues(alpha: 0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ],
    );
  }

  // Text Field
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int? maxLines,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          maxLines: maxLines ?? 1,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.primaryBlack,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            prefixIcon: Icon(icon, color: AppConstants.mediumGrey, size: 20),
            filled: true,
            fillColor: AppConstants.primaryWhite,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(color: AppConstants.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: BorderSide(
                color: AppConstants.primaryBlack,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ],
    );
  }

  // Off Day Selector
  Widget _buildOffDaySelector(OwnerEditProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Off Day',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            border: Border.all(color: AppConstants.lightGrey),
          ),
          child: Obx(
            () => DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: controller.selectedOffDay.value,
                icon: const Icon(
                  Icons.keyboard_arrow_down,
                  color: AppConstants.mediumGrey,
                ),
                isExpanded: true,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppConstants.primaryBlack,
                ),
                items: controller.offDaysOptions.map((String day) {
                  return DropdownMenuItem<String>(value: day, child: Text(day));
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    controller.selectedOffDay.value = newValue;
                  }
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Time Selector
  Widget _buildTimeSelector(
    OwnerEditProfileController controller,
    String label,
    Rx<TimeOfDay> timeValue,
    VoidCallback onTap,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.primaryWhite,
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              border: Border.all(color: AppConstants.lightGrey),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.access_time,
                  color: AppConstants.mediumGrey,
                  size: 20,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: Obx(
                    () => Text(
                      controller.formatTimeForDisplay(timeValue.value),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ),
                ),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: AppConstants.mediumGrey,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Save Button
  Widget _buildSaveButton(OwnerEditProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Obx(
        () => ElevatedButton(
          onPressed: controller.isSaving.value ? null : controller.saveChanges,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryBlack,
            foregroundColor: AppConstants.primaryWhite,
            padding: const EdgeInsets.symmetric(
              vertical: AppConstants.paddingMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            elevation: 0,
          ),
          child: controller.isSaving.value
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Saving...',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                )
              : Text(
                  'Save Changes',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }
}
