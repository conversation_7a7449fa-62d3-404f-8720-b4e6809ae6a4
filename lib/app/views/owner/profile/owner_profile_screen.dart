import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/owner_profile_controller.dart';
import '../../../widgets/profile_image_gallery.dart';
import '../../../widgets/business_info_card.dart';
import '../../../models/owner_profile_model.dart';

class OwnerProfileScreen extends StatelessWidget {
  const OwnerProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Force fresh controller instance to avoid type conflicts
    Get.delete<OwnerProfileController>(force: true);
    final OwnerProfileController controller = Get.put(
      OwnerProfileController(),
      permanent: false,
    );

    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      body: Obx(() {
        if (controller.isLoadingProfile.value) {
          return _buildLoadingState();
        }

        return RefreshIndicator(
          onRefresh: controller.refreshProfileData,
          color: AppConstants.primaryBlack,
          backgroundColor: AppConstants.primaryWhite,
          child: CustomScrollView(
            slivers: [
              _buildAppBar(controller),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(
                    AppConstants.paddingMedium,
                    AppConstants.paddingSmall,
                    AppConstants.paddingMedium,
                    AppConstants.paddingMedium,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Image Gallery Section
                      _buildImageGallerySection(controller),

                      const SizedBox(height: AppConstants.paddingMedium),

                      // Owner Details Section
                      _buildOwnerDetailsSection(controller),

                      const SizedBox(height: AppConstants.paddingLarge),

                      // Business Information Cards
                      _buildBusinessInformationSection(controller),

                      const SizedBox(height: AppConstants.paddingLarge),

                      // Earnings Overview Section
                      _buildEarningsOverviewSection(controller),

                      const SizedBox(height: AppConstants.paddingLarge),

                      // Bank Details Card
                      _buildBankDetailsSection(controller),

                      const SizedBox(height: AppConstants.paddingLarge),

                      // Settings Section
                      _buildSettingsSection(controller),

                      const SizedBox(height: AppConstants.paddingLarge),

                      // App Version
                      _buildAppVersion(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  // Loading state
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryBlack),
      ),
    );
  }

  // App Bar
  Widget _buildAppBar(OwnerProfileController controller) {
    return SliverAppBar(
      expandedHeight: 60,
      floating: false,
      pinned: true,
      backgroundColor: AppConstants.primaryWhite,
      foregroundColor: AppConstants.primaryBlack,
      elevation: 0,
      // leading: Padding(
      //   padding: const EdgeInsets.only(left: 0, bottom: 5),
      //   child: CircleAvatar(
      //     child: ClipRRect(
      //       borderRadius: BorderRadiusGeometry.circular(20),
      //       child: Image.network(
      //         controller.salonProfile.toString(),
      //         fit: BoxFit.cover,
      //         height: Get.height,
      //         width: Get.width,
      //       ),
      //     ),
      //   ),
      // ),
      title: Text(
        'Profile',
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: false,
      actions: [
        IconButton(
          onPressed: controller.navigateToEditProfile,
          icon: const Icon(
            Icons.edit_outlined,
            color: AppConstants.primaryBlack,
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(gradient: AppConstants.lightGradient),
        ),
      ),
    );
  }

  // Image Gallery Section
  Widget _buildImageGallerySection(OwnerProfileController controller) {
    return Obx(() {
      // Ensure we have the correct type
      final List<SaloonImage> images = List<SaloonImage>.from(
        controller.galleryImages,
      );
      return ProfileImageGallery(
        networkImages: images,
        localImages: controller.selectedGalleryImages,
        onAddImages: controller.pickGalleryImages,
        onRemoveImage: controller.removeGalleryImage,
        onDeleteNetworkImage: controller.deleteNetworkImage,
        isUploading: controller.isUploadingImages.value,
      );
    });
  }

  // Owner Details Section
  Widget _buildOwnerDetailsSection(OwnerProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Salon Name with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppConstants.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.store,
                  color: AppConstants.primaryWhite,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Obx(
                  () => Text(
                    controller.salonName.value.isNotEmpty
                        ? controller.salonName.value
                        : 'Elite Hair Studio',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Owner Name with better styling
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: AppConstants.mediumGrey,
                size: 18,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Obx(
                () => Text(
                  'Owned by ${controller.ownerName.value.isNotEmpty ? controller.ownerName.value : "Atif Ansari"}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.mediumGrey,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Description with better formatting
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.lightGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              border: Border.all(
                color: AppConstants.primaryGrey.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.description_outlined,
                      color: AppConstants.mediumGrey,
                      size: 16,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'About Our Salon',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Obx(
                  () => Text(
                    controller.salonDescription.value.isNotEmpty
                        ? controller.salonDescription.value
                        : 'Welcome to our premium salon! We offer professional hair styling, cutting, coloring, and beauty treatments. Our experienced stylists use the latest techniques and high-quality products to ensure you look and feel your best.',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.darkGrey,
                      height: 1.6,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Business Information Section
  Widget _buildBusinessInformationSection(OwnerProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Business Information',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        Obx(
          () => BusinessInfoCard(
            icon: Icons.access_time,
            title: 'Working Hours',
            subtitle: controller.workingHours.value,
            iconColor: Colors.blue,
          ),
        ),

        Obx(
          () => BusinessInfoCard(
            icon: Icons.calendar_today,
            title: 'Working Days',
            subtitle: controller.workingDays.value,
            iconColor: Colors.green,
          ),
        ),

        Obx(
          () => BusinessInfoCard(
            icon: Icons.phone,
            title: 'Contact Information',
            subtitle: controller.ownerPhone.value.isNotEmpty
                ? controller.ownerPhone.value
                : 'Add phone number',
            iconColor: Colors.orange,
          ),
        ),

        Obx(
          () => BusinessInfoCard(
            icon: Icons.location_on,
            title: 'Address',
            subtitle: controller.salonAddress.value,
            iconColor: Colors.red,
          ),
        ),
      ],
    );
  }

  // Earnings Overview Section
  Widget _buildEarningsOverviewSection(OwnerProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Earnings Overview',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        Row(
          children: [
            Expanded(
              child: Obx(
                () => EarningsCard(
                  title: 'Total Collected',
                  amount: controller.formatCurrency(
                    controller.totalCollected.value,
                  ),
                  subtitle: 'This month',
                  icon: Icons.account_balance_wallet,
                ),
              ),
            ),

            const SizedBox(width: AppConstants.paddingMedium),

            Expanded(
              child: Obx(
                () => EarningsCard(
                  title: 'Available Balance',
                  amount: controller.formatCurrency(
                    controller.availableBalance.value,
                  ),
                  subtitle: 'Ready to withdraw',
                  icon: Icons.savings,
                  showButton: true,
                  buttonText: 'View History',
                  onTap: controller.navigateToEarningsHistory,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Bank Details Section
  Widget _buildBankDetailsSection(OwnerProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bank Details',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          Obx(() {
            if (controller.hasBankAccount.value &&
                controller.bankAccountData.value != null) {
              return Column(
                children: [
                  BusinessInfoCard(
                    icon: Icons.account_balance,
                    title: 'Bank Account',
                    subtitle: controller.bankAccountData.value!.displayName,
                    iconColor: Colors.purple,
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  if (controller.bankAccountData.value!.hasUpiDetails)
                    BusinessInfoCard(
                      icon: Icons.payment,
                      title: 'UPI ID',
                      subtitle: controller.bankAccountData.value!.upiId!,
                      iconColor: Colors.blue,
                    ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  if (controller.bankAccountData.value!.hasUpiDetails)
                    Container(
                      padding: const EdgeInsets.all(AppConstants.paddingSmall),
                      decoration: BoxDecoration(
                        color: controller.bankAccountData.value!.upiVerified
                            ? Colors.green.shade50
                            : Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(
                          AppConstants.radiusSmall,
                        ),
                        border: Border.all(
                          color: controller.bankAccountData.value!.upiVerified
                              ? Colors.green.shade200
                              : Colors.orange.shade200,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            controller.bankAccountData.value!.upiVerified
                                ? Icons.verified
                                : Icons.pending,
                            color: controller.bankAccountData.value!.upiVerified
                                ? Colors.green.shade600
                                : Colors.orange.shade600,
                            size: 16,
                          ),
                          const SizedBox(width: AppConstants.paddingSmall),
                          Text(
                            controller.bankAccountData.value!.upiVerified
                                ? 'UPI Verified'
                                : 'UPI Verification Pending',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color:
                                  controller.bankAccountData.value!.upiVerified
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              );
            } else {
              return BusinessInfoCard(
                icon: Icons.add_card,
                title: 'Add Bank Account',
                subtitle: 'Set up your bank details for payments',
                iconColor: Colors.green,
                showArrow: true,
                onTap: controller.navigateToBankDetails,
              );
            }
          }),
        ],
      ),
    );
  }

  // Settings Section
  Widget _buildSettingsSection(OwnerProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          SettingsListTile(
            icon: Icons.privacy_tip_outlined,
            title: 'Privacy Policy',
            subtitle: 'Read our privacy policy',
            onTap: () => Get.snackbar(
              'Coming Soon',
              'Privacy policy will be available soon',
            ),
          ),

          SettingsListTile(
            icon: Icons.description_outlined,
            title: 'Terms & Conditions',
            subtitle: 'Read terms and conditions',
            onTap: () => Get.snackbar(
              'Coming Soon',
              'Terms & conditions will be available soon',
            ),
          ),

          SettingsListTile(
            icon: Icons.help_outline,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () => Get.snackbar(
              'Coming Soon',
              'Help & support will be available soon',
            ),
          ),

          SettingsListTile(
            icon: Icons.star_outline,
            title: 'Rate Us',
            subtitle: 'Rate our app on the store',
            onTap: () => Get.snackbar(
              'Coming Soon',
              'Rate us feature will be available soon',
            ),
          ),

          SettingsListTile(
            icon: Icons.logout,
            title: 'Logout',
            subtitle: 'Sign out of your account',
            iconColor: Colors.red,
            showDivider: false,
            onTap: controller.showLogoutDialog,
          ),
        ],
      ),
    );
  }

  // App Version
  Widget _buildAppVersion() {
    return Center(
      child: Text(
        'Version ${AppConstants.appVersion}',
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: AppConstants.mediumGrey,
        ),
      ),
    );
  }
}
