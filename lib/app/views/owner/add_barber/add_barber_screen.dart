import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/owner/add_barber_controller.dart';
import '../../../widgets/enhanced_barber_list_tile.dart';
import '../../../widgets/add_barber_modal.dart';
import '../../../widgets/barber_details_modal.dart';
import '../../../models/barber_models.dart';
import '../../../services/barber_service.dart';
import '../edit_barber/edit_barber_screen.dart';

class AddBarberScreen extends StatelessWidget {
  const AddBarberScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize controller with lazy loading
    Get.lazyPut(() => AddBarberController());
    final controller = Get.find<AddBarberController>();

    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      appBar: _buildAppBar(),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : controller.allBarbers.isEmpty
            ? _buildEmptyState()
            : _buildBarbersList(controller),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      automaticallyImplyLeading: false,
      centerTitle: false,
      title: Text(
        'Barbers',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: AppConstants.primaryBlack,
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.only(
            right: AppConstants.paddingMedium,
            top: AppConstants.paddingSmall,
            bottom: AppConstants.paddingSmall,
          ),
          child: ElevatedButton.icon(
            onPressed: _showAddBarberModal,
            icon: const Icon(
              Icons.add,
              size: 14,
              color: AppConstants.primaryWhite,
            ),
            label: Text(
              'Add Barber',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryWhite,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryBlack,
              foregroundColor: AppConstants.primaryWhite,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingXLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_add_outlined,
              size: 80,
              color: AppConstants.mediumGrey,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              'No Barbers Added Yet',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Add your first barber to start managing your salon team.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingXLarge),
            ElevatedButton.icon(
              onPressed: _showAddBarberModal,
              icon: const Icon(Icons.add, color: AppConstants.primaryWhite),
              label: Text(
                'Add First Barber',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryWhite,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingXLarge,
                  vertical: AppConstants.paddingMedium,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBarbersList(AddBarberController controller) {
    return RefreshIndicator(
      onRefresh: controller.loadAllBarbers, // Use enhanced method
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        itemCount: controller.allBarbers.length, // Use enhanced list
        itemBuilder: (context, index) {
          final barber = controller.allBarbers[index]; // Use enhanced list
          return EnhancedBarberListTile(
            barber: barber,
            onTap: () => _showBarberDetailsEnhanced(barber),
            onEdit: () => _showEditBarberModalEnhanced(barber),
            onDelete: () => _deleteBarberEnhanced(controller, barber),
            onAvailabilityToggle: (value) => _toggleBarberAvailabilityEnhanced(
              controller,
              index,
              barber,
              value,
            ),
            isLoading: controller.isDeletingBarber.value,
          );
        },
      ),
    );
  }

  // Enhanced methods for BarberModel
  void _showBarberDetailsEnhanced(BarberModel barber) {
    // Convert to BarberData for existing modal
    final barberData = _convertToBarberData(barber);
    Get.bottomSheet(
      BarberDetailsModal(barber: barberData),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }

  void _showEditBarberModalEnhanced(BarberModel barber) async {
    // Navigate to edit screen with slide animation
    final result = await Get.to(
      () => EditBarberScreen(barberId: barber.id ?? ''),
      transition: Transition.rightToLeft,
      duration: const Duration(milliseconds: 300),
    );

    // Refresh list if barber was updated or deleted
    if (result == true || result == 'deleted') {
      final controller = Get.find<AddBarberController>();
      await controller.loadAllBarbers();
    }
  }

  void _deleteBarberEnhanced(
    AddBarberController controller,
    BarberModel barber,
  ) async {
    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: Text(
          'Delete Barber',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Are you sure you want to delete ${barber.name}? This action cannot be undone.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(color: AppConstants.mediumGrey),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(
              'Delete',
              style: GoogleFonts.poppins(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await BarberService.deleteBarberEnhanced(
          barber.id ?? '',
        );
        if (success) {
          Get.snackbar(
            'Success',
            'Barber deleted successfully',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
          await controller.loadAllBarbers(); // Refresh list
        } else {
          Get.snackbar(
            'Error',
            'Failed to delete barber',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } catch (e) {
        Get.snackbar(
          'Error',
          'An error occurred while deleting barber',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  void _toggleBarberAvailabilityEnhanced(
    AddBarberController controller,
    int index,
    BarberModel barber,
    bool newAvailability,
  ) async {
    // Use the state management method for better UX
    await controller.updateBarberAvailabilityWithState(
      barber.id ?? '',
      newAvailability,
    );
  }

  // Helper method to convert BarberModel to BarberData for UI compatibility
  BarberData _convertToBarberData(BarberModel barber) {
    return BarberData(
      id: barber.id ?? '',
      firstname: barber.firstname,
      lastname: barber.lastname,
      phone: '', // BarberModel doesn't have phone field
      email: '', // BarberModel doesn't have email field
      bio: barber.bio ?? '',
      available: barber.available,
      profileImage: barber.profileimage,
      serviceIds: barber.assignedServices ?? [],
      saloonId: barber.saloonid ?? '',
      isDelete: barber.isDelete ?? false,
      createdAt: barber.createdAt?.toIso8601String() ?? '',
      updatedAt: barber.updatedAt?.toIso8601String() ?? '',
      totalStars: 0, // Default values for missing fields
      totalReviews: 0,
    );
  }

  void _showAddBarberModal() {
    final controller = Get.find<AddBarberController>();
    // Clear form for new barber
    controller.isEditing.value = false;
    controller.editingBarberId.value = '';

    Get.bottomSheet(
      const AddBarberModal(isEditing: false),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}
