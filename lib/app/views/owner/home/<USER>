import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../widgets/earning_chart.dart';
import '../../../controllers/owner/owner_home_controller.dart';
import '../../../controllers/owner/owner_navigation_controller.dart';
import '../withdrawal/withdrawal_screen.dart';

class OwnerHomeScreen extends StatelessWidget {
  const OwnerHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final OwnerHomeController controller = Get.put(OwnerHomeController());

    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      body: Obx(() {
        if (controller.hasError.value) {
          return _buildErrorState(context, controller);
        }

        return RefreshIndicator(
          onRefresh: controller.refreshData,
          color: AppConstants.primaryBlack,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Custom App Bar
                _buildCustomAppBar(context, controller),

                const SizedBox(height: AppConstants.paddingLarge),

                // Statistics Cards Row
                _buildStatisticsRow(context, controller),

                const SizedBox(height: AppConstants.paddingLarge),

                // Earnings Chart
                _buildEarningsChart(context, controller),

                const SizedBox(height: AppConstants.paddingLarge),

                // Recent Appointments
                _buildRecentAppointments(context, controller),

                const SizedBox(height: AppConstants.paddingLarge),

                // Bank Balance Section
                _buildBankBalanceSection(context, controller),

                const SizedBox(height: AppConstants.paddingXLarge),
              ],
            ),
          ),
        );
      }),
    );
  }

  // Custom App Bar
  Widget _buildCustomAppBar(
    BuildContext context,
    OwnerHomeController controller,
  ) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + AppConstants.paddingSmall,
        left: AppConstants.paddingMedium,
        right: AppConstants.paddingMedium,
        bottom: AppConstants.paddingMedium,
      ),
      color: AppConstants.primaryWhite,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(
                  () => Text(
                    'Hello, ${controller.ownerName.value}',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),

                const SizedBox(height: 2),

                Obx(
                  () => Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 14,
                        color: AppConstants.mediumGrey,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          controller.salonLocation.value,
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            color: AppConstants.mediumGrey,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Notification Icon
          Container(
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(6),
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () {
                  // TODO: Navigate to notifications
                },
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Icon(
                    Icons.notifications_outlined,
                    size: 20,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Statistics Cards Row
  Widget _buildStatisticsRow(
    BuildContext context,
    OwnerHomeController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Row(
        children: [
          // Weekly Earnings Card
          Expanded(
            child: _buildStatCard(
              context,
              'Weekly Earnings',
              '₹${controller.weeklyEarnings.value.toStringAsFixed(0)}',
              Icons.trending_up,
              controller.earningsGrowth.value >= 0,
              controller.earningsGrowth.value,
            ),
          ),

          const SizedBox(width: AppConstants.paddingMedium),

          // Weekly Bookings Card
          Expanded(
            child: _buildStatCard(
              context,
              'Weekly Bookings',
              controller.weeklyBookings.value.toString(),
              Icons.calendar_today,
              controller.bookingsGrowth.value >= 0,
              controller.bookingsGrowth.value,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    bool isPositive,
    double growthPercentage,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() {
        if (Get.find<OwnerHomeController>().isLoadingStats.value) {
          return _buildStatCardLoading();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppConstants.lightGrey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, size: 16, color: AppConstants.primaryBlack),
                ),
                const Spacer(),
                AnimatedRotation(
                  turns: isPositive ? 0.0 : 0.5,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.trending_up,
                    color: isPositive ? Colors.green : Colors.red,
                    size: 14,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 11,
                color: AppConstants.mediumGrey,
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 4),

            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: AppConstants.primaryBlack,
              ),
              child: Text(value),
            ),

            const SizedBox(height: 4),

            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              style: GoogleFonts.poppins(
                fontSize: 11,
                color: isPositive ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
              child: Text(
                '${isPositive ? '+' : ''}${growthPercentage.toStringAsFixed(1)}%',
              ),
            ),
          ],
        );
      }),
    );
  }

  // Loading state for stat cards
  Widget _buildStatCardLoading() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: AppConstants.mediumGrey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const Spacer(),
            Container(
              width: 14,
              height: 14,
              decoration: BoxDecoration(
                color: AppConstants.mediumGrey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Container(
          height: 11,
          width: 80,
          decoration: BoxDecoration(
            color: AppConstants.mediumGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          height: 16,
          width: 60,
          decoration: BoxDecoration(
            color: AppConstants.mediumGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          height: 11,
          width: 40,
          decoration: BoxDecoration(
            color: AppConstants.mediumGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  // Earnings Chart
  Widget _buildEarningsChart(
    BuildContext context,
    OwnerHomeController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Obx(
        () => EarningChartWidget(
          monthlyEarnings: controller.chartData,
          totalEarnings: controller.weeklyEarnings.value,
          growthPercentage: controller.earningsGrowth.value,
          lastUpdated: 'Today',
          timeFilter: controller.chartTimeFilter.value,
          labels: controller.chartTimeFilter.value == 'Weekly'
              ? ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
              : [
                  'Jan',
                  'Feb',
                  'Mar',
                  'Apr',
                  'May',
                  'Jun',
                  'Jul',
                  'Aug',
                  'Sep',
                  'Oct',
                  'Nov',
                  'Dec',
                ],
          onTimeFilterChanged: (String? newFilter) {
            if (newFilter != null) {
              controller.onChartFilterChanged(newFilter);
            }
          },
          isLoading:
              controller.isLoadingStats.value ||
              controller.isLoadingChart.value,
        ),
      ),
    );
  }

  // Recent Appointments
  Widget _buildRecentAppointments(
    BuildContext context,
    OwnerHomeController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Appointments',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(4),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () {
                      final navController =
                          Get.find<OwnerNavigationController>();
                      navController.goToBookings();
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      child: Text(
                        'View All',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: AppConstants.primaryBlack,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Appointment Cards
            Obx(() {
              if (controller.isLoadingStats.value) {
                return _buildAppointmentLoadingState();
              }

              if (controller.recentAppointments.isEmpty) {
                return _buildNoAppointmentsState();
              }

              return Column(
                children: controller.recentAppointments.map((appointment) {
                  return Padding(
                    padding: const EdgeInsets.only(
                      bottom: AppConstants.paddingMedium,
                    ),
                    child: _buildAppointmentCard(
                      appointment.displayCustomer,
                      appointment.displayServices,
                      appointment.displayTime,
                      appointment.status,
                      _getStatusColor(appointment.status),
                    ),
                  );
                }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  // Bank Balance Section
  Widget _buildBankBalanceSection(
    BuildContext context,
    OwnerHomeController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.account_balance_wallet,
                size: 24,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(width: AppConstants.paddingMedium),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bank Balance',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppConstants.mediumGrey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 4),

                  Obx(
                    () => Text(
                      '₹${controller.bankBalance.value.toStringAsFixed(0)}',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            Material(
              color: AppConstants.primaryBlack,
              borderRadius: BorderRadius.circular(8),
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () {
                  // Navigate to withdrawal screen
                  _navigateToWithdrawalScreen();
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: Text(
                    'Withdraw',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryWhite,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Appointment Card Helper
  Widget _buildAppointmentCard(
    String customerName,
    String services,
    String time,
    String status,
    Color statusColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppConstants.primaryWhite,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(Icons.person, color: AppConstants.mediumGrey, size: 20),
          ),

          const SizedBox(width: AppConstants.paddingMedium),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customerName,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.primaryBlack,
                  ),
                ),

                const SizedBox(height: 2),

                Text(
                  services,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppConstants.mediumGrey,
                  ),
                ),

                const SizedBox(height: 2),

                Text(
                  time,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ],
            ),
          ),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              status,
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get status color
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'completed':
        return Colors.green;
      case 'in progress':
      case 'ongoing':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return AppConstants.mediumGrey;
    }
  }

  // Loading state for appointments
  Widget _buildAppointmentLoadingState() {
    return Column(
      children: List.generate(3, (index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppConstants.mediumGrey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 14,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppConstants.mediumGrey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        height: 12,
                        width: 150,
                        decoration: BoxDecoration(
                          color: AppConstants.mediumGrey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 20,
                  width: 60,
                  decoration: BoxDecoration(
                    color: AppConstants.mediumGrey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  // No appointments state
  Widget _buildNoAppointmentsState() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        children: [
          Icon(
            Icons.calendar_today_outlined,
            size: 48,
            color: AppConstants.mediumGrey,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No recent appointments',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Your recent appointments will appear here',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Error State Widget
  Widget _buildErrorState(
    BuildContext context,
    OwnerHomeController controller,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppConstants.mediumGrey),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Something went wrong',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              controller.errorMessage.value.isNotEmpty
                  ? controller.errorMessage.value
                  : 'Unable to load dashboard data',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Material(
              color: AppConstants.primaryBlack,
              borderRadius: BorderRadius.circular(8),
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                onTap: controller.retryLoadData,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  child: Text(
                    'Retry',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryWhite,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Navigate to withdrawal screen
  void _navigateToWithdrawalScreen() {
    final OwnerHomeController controller = Get.find<OwnerHomeController>();

    // Get available balance from controller
    final availableBalance = controller.bankBalance.value;

    // Navigate to withdrawal screen
    Get.to(
      () => WithdrawalScreen(availableBalance: availableBalance),
      transition: Transition.rightToLeft,
      duration: const Duration(milliseconds: 300),
    );
  }
}
