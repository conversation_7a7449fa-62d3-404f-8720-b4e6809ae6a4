import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../../constants/app_constants.dart';
import '../../controllers/location_controller.dart';

class LocationSelectionScreen extends StatefulWidget {
  const LocationSelectionScreen({super.key});

  @override
  State<LocationSelectionScreen> createState() =>
      _LocationSelectionScreenState();
}

class _LocationSelectionScreenState extends State<LocationSelectionScreen> {
  late final MapController _mapController;
  late final LocationController _locationController;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
    _locationController = Get.put(LocationController());

    // Listen to location changes and move map accordingly
    _locationController.currentLocation.listen((location) {
      if (location != null) {
        _mapController.move(location, 15.0);
      }
    });

    _locationController.selectedLocation.listen((location) {
      if (location != null) {
        _mapController.move(location, 15.0);
      }
    });
  }

  @override
  void dispose() {
    _mapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          // Search Bar
          _buildSearchBar(_locationController),

          // Map Container
          Expanded(
            child: Stack(
              children: [
                _buildMap(_locationController),
                _buildSearchResults(_locationController),
                _buildLocationButtons(_locationController),
                _buildLoadingOverlay(_locationController),
              ],
            ),
          ),

          // Bottom Action Bar
          _buildBottomActionBar(_locationController),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(
          Icons.arrow_back_ios_new,
          color: AppConstants.primaryBlack,
          size: 20,
        ),
        style: IconButton.styleFrom(
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
      ),
      title: Text(
        'Select Location',
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildSearchBar(LocationController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: TextField(
        controller: controller.searchController,
        onChanged: (value) {
          if (value.length > 2) {
            controller.debouncedSearch(value);
          } else {
            controller.showSearchResults.value = false;
            controller.searchResults.clear();
          }
        },
        decoration: InputDecoration(
          hintText: 'Search for a location (e.g., Vashi, Bandra)...',
          hintStyle: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: AppConstants.mediumGrey,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppConstants.mediumGrey,
            size: 18,
          ),
          suffixIcon: Obx(
            () => controller.isSearching.value
                ? const SizedBox(
                    width: 18,
                    height: 18,
                    child: Padding(
                      padding: EdgeInsets.all(12),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
          filled: true,
          fillColor: AppConstants.lightGrey.withValues(alpha: 0.3),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppConstants.primaryBlack,
              width: 1,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12.0,
            vertical: 10.0,
          ),
        ),
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: AppConstants.primaryBlack,
        ),
      ),
    );
  }

  Widget _buildMap(LocationController controller) {
    return Obx(() {
      final currentLoc = controller.currentLocation.value;
      final selectedLoc = controller.selectedLocation.value;

      return FlutterMap(
        mapController: _mapController,
        options: MapOptions(
          initialCenter:
              selectedLoc ??
              currentLoc ??
              const LatLng(28.6139, 77.2090), // Default to Delhi
          initialZoom: selectedLoc != null || currentLoc != null ? 15.0 : 10.0,
          minZoom: 3.0,
          maxZoom: 18.0,
          onTap: (tapPosition, point) {
            controller.selectLocationOnMap(point);
          },
          interactionOptions: const InteractionOptions(
            flags: InteractiveFlag.all,
          ),
        ),
        children: [
          TileLayer(
            urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
            userAgentPackageName: 'com.saloon.app',
            maxZoom: 18,
            subdomains: const ['a', 'b', 'c'],
            additionalOptions: const {
              'attribution': '© OpenStreetMap contributors',
            },
          ),
          MarkerLayer(
            markers: [
              // Current location marker (blue)
              if (currentLoc != null)
                Marker(
                  point: currentLoc,
                  width: 44,
                  height: 44,
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppConstants.primaryWhite,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.my_location,
                      color: AppConstants.primaryWhite,
                      size: 18,
                    ),
                  ),
                ),
              // Selected location marker (black) - only show if different from current
              if (selectedLoc != null && selectedLoc != currentLoc)
                Marker(
                  point: selectedLoc,
                  width: 44,
                  height: 44,
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppConstants.primaryBlack,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppConstants.primaryWhite,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryBlack.withValues(
                            alpha: 0.3,
                          ),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: AppConstants.primaryWhite,
                      size: 18,
                    ),
                  ),
                ),
            ],
          ),
        ],
      );
    });
  }

  Widget _buildSearchResults(LocationController controller) {
    return Obx(() {
      if (!controller.showSearchResults.value ||
          controller.searchResults.isEmpty) {
        return const SizedBox.shrink();
      }

      return Positioned(
        top: 0,
        left: AppConstants.paddingLarge,
        right: AppConstants.paddingLarge,
        child: Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            itemCount: controller.searchResults.length,
            separatorBuilder: (context, index) =>
                Divider(height: 1, color: AppConstants.lightGrey),
            itemBuilder: (context, index) {
              final placemark = controller.searchResults[index];
              return ListTile(
                leading: Icon(
                  Icons.location_on,
                  color: AppConstants.mediumGrey,
                  size: 20,
                ),
                title: Text(
                  placemark.name ?? 'Unknown',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                subtitle: Text(
                  '${placemark.locality ?? ''}, ${placemark.administrativeArea ?? ''}',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.mediumGrey,
                  ),
                ),
                onTap: () => controller.selectSearchResult(placemark),
              );
            },
          ),
        ),
      );
    });
  }

  Widget _buildLocationButtons(LocationController controller) {
    return Positioned(
      right: 16.0,
      bottom: 120,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Single location button that handles both getting and using current location
          Container(
            decoration: BoxDecoration(
              color: AppConstants.primaryWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.15),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () async {
                  await controller.getCurrentLocation();
                  // After getting location, automatically use it as selected
                  if (controller.currentLocation.value != null) {
                    controller.useCurrentLocation();
                  }
                },
                child: Container(
                  width: 48,
                  height: 48,
                  padding: const EdgeInsets.all(12),
                  child: Obx(
                    () => controller.isLoadingLocation.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppConstants.primaryBlack,
                            ),
                          )
                        : Icon(
                            controller.currentLocation.value != null
                                ? Icons.gps_fixed
                                : Icons.my_location,
                            color: AppConstants.primaryBlack,
                            size: 20,
                          ),
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Zoom controls
          Container(
            decoration: BoxDecoration(
              color: AppConstants.primaryWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.15),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Zoom in
                Material(
                  color: Colors.transparent,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  child: InkWell(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                    onTap: () {
                      final zoom = _mapController.camera.zoom;
                      _mapController.move(
                        _mapController.camera.center,
                        zoom + 1,
                      );
                    },
                    child: Container(
                      width: 48,
                      height: 40,
                      padding: const EdgeInsets.all(8),
                      child: const Icon(
                        Icons.add,
                        color: AppConstants.primaryBlack,
                        size: 18,
                      ),
                    ),
                  ),
                ),

                Container(
                  height: 1,
                  color: AppConstants.lightGrey.withValues(alpha: 0.3),
                ),

                // Zoom out
                Material(
                  color: Colors.transparent,
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(12),
                  ),
                  child: InkWell(
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(12),
                    ),
                    onTap: () {
                      final zoom = _mapController.camera.zoom;
                      _mapController.move(
                        _mapController.camera.center,
                        zoom - 1,
                      );
                    },
                    child: Container(
                      width: 48,
                      height: 40,
                      padding: const EdgeInsets.all(8),
                      child: const Icon(
                        Icons.remove,
                        color: AppConstants.primaryBlack,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay(LocationController controller) {
    return Obx(() {
      if (controller.isLoadingAddress.value) {
        return Positioned(
          top: 50,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: AppConstants.primaryBlack.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppConstants.primaryWhite,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    'Getting address...',
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.primaryWhite,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  Widget _buildBottomActionBar(LocationController controller) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Selected address display
          Obx(() {
            final address = controller.selectedAddress.value;
            if (address.isNotEmpty) {
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12.0),
                margin: const EdgeInsets.only(bottom: 12.0),
                decoration: BoxDecoration(
                  color: AppConstants.lightGrey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected Location:',
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      address,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),

          // Error message
          Obx(() {
            if (controller.hasError.value) {
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12.0),
                margin: const EdgeInsets.only(bottom: 12.0),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  controller.errorMessage.value,
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w400,
                    color: Colors.red.shade700,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),

          // Confirm button
          SizedBox(
            width: double.infinity,
            height: 48,
            child: Obx(
              () => ElevatedButton(
                onPressed: controller.selectedLocation.value != null
                    ? controller.confirmLocation
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryBlack,
                  foregroundColor: AppConstants.primaryWhite,
                  disabledBackgroundColor: AppConstants.lightGrey,
                  disabledForegroundColor: AppConstants.mediumGrey,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Confirm Location',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
