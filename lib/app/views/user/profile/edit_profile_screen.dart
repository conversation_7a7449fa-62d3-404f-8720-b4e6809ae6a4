import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/user/user_profile_controller.dart';
import '../../../models/user_profile_models.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  // Text controllers for form fields
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _localityController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();

  // Expansion state for inline editing
  final Map<String, bool> _expandedStates = {
    'firstName': false,
    'lastName': false,
    'address': false,
    'locality': false,
    'city': false,
    'state': false,
    'country': false,
  };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeFormData();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _slideController.forward();
  }

  void _initializeFormData() {
    final controller = Get.find<UserProfileController>();
    final profile = controller.userProfile.value;

    if (profile != null) {
      _firstNameController.text = profile.firstName ?? '';
      _lastNameController.text = profile.lastName ?? '';
      _addressController.text = profile.address?.street ?? '';
      _localityController.text = profile.address?.area ?? '';
      _cityController.text = profile.address?.city ?? '';
      _stateController.text = profile.address?.state ?? '';
      _countryController.text = profile.address?.country ?? '';
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _addressController.dispose();
    _localityController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  void _onItemTap() {
    // Simple tap feedback - no animation needed for inline editing
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<UserProfileController>();

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryWhite,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.primaryBlack),
          onPressed: () {
            Get.back();
            // Refresh profile data when going back
            controller.refreshProfile();
          },
        ),
        title: Text(
          'Edit Profile',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),
        centerTitle: true,
        actions: [
          Obx(
            () => TextButton(
              onPressed: controller.isUpdating.value ? null : _saveProfile,
              child: controller.isUpdating.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppConstants.primaryBlack,
                      ),
                    )
                  : Text(
                      'Save',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppConstants.darkGrey,
                      ),
                    ),
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: controller.refreshProfile,
        color: AppConstants.primaryBlack,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(10.0),
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _slideController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Photo Section
                  _buildProfilePhotoSection(controller),

                  const SizedBox(height: 20.0),

                  // Editable Fields Section
                  _buildEditableFieldsSection(controller),

                  const SizedBox(height: 20.0),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfilePhotoSection(UserProfileController controller) {
    return Center(
      child: Column(
        children: [
          // Profile Photo
          Stack(
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: AppConstants.lightGrey, width: 4),
                  boxShadow: [
                    BoxShadow(
                      color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipOval(
                  child:
                      controller.hasProfileImage &&
                          controller.profileImageUrl != null
                      ? CachedNetworkImage(
                          imageUrl: controller.profileImageUrl!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: AppConstants.lightGrey,
                            child: const Icon(
                              Icons.person,
                              size: 50,
                              color: AppConstants.mediumGrey,
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: AppConstants.lightGrey,
                            child: Center(
                              child: Text(
                                controller.userInitials,
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.primaryBlack,
                                ),
                              ),
                            ),
                          ),
                        )
                      : Container(
                          color: AppConstants.lightGrey,
                          child: Center(
                            child: Text(
                              controller.userInitials,
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppConstants.primaryBlack,
                              ),
                            ),
                          ),
                        ),
                ),
              ),

              // Camera/Edit Button
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => _showImagePickerDialog(controller),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppConstants.primaryBlack,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppConstants.primaryWhite,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryBlack.withValues(
                            alpha: 0.2,
                          ),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: AppConstants.primaryWhite,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 10.0),

          // Upload Progress or Status
          Obx(() {
            if (controller.isUploadingImage.value) {
              return Column(
                children: [
                  const CircularProgressIndicator(
                    color: AppConstants.primaryBlack,
                    strokeWidth: 2,
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    'Uploading photo...',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ],
              );
            } else {
              return Text(
                'Tap camera icon to change photo',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.mediumGrey,
                ),
              );
            }
          }),
        ],
      ),
    );
  }

  Widget _buildEditableFieldsSection(UserProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 10),
          child: Text(
            'Personal Information',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
        ),

        // Fields Container
        Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.08),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildEditableListTile(
                icon: Icons.person_outline,
                title: 'First Name',
                controller: _firstNameController,
                hint: 'Enter your first name',
                isRequired: true,
              ),

              _buildDivider(),

              _buildEditableListTile(
                icon: Icons.person_outline,
                title: 'Last Name',
                controller: _lastNameController,
                hint: 'Enter your last name',
                isRequired: true,
              ),

              _buildDivider(),

              _buildEditableListTile(
                icon: Icons.home_outlined,
                title: 'Address',
                controller: _addressController,
                hint: 'Enter your street address',
              ),

              _buildDivider(),

              _buildEditableListTile(
                icon: Icons.location_on_outlined,
                title: 'Locality',
                controller: _localityController,
                hint: 'Enter your locality/area',
              ),

              _buildDivider(),

              _buildEditableListTile(
                icon: Icons.location_city_outlined,
                title: 'City',
                controller: _cityController,
                hint: 'Enter your city',
              ),

              _buildDivider(),

              _buildEditableListTile(
                icon: Icons.map_outlined,
                title: 'State',
                controller: _stateController,
                hint: 'Enter your state',
              ),

              _buildDivider(),

              _buildEditableListTile(
                icon: Icons.public_outlined,
                title: 'Country',
                controller: _countryController,
                hint: 'Enter your country',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEditableListTile({
    required IconData icon,
    required String title,
    required TextEditingController controller,
    required String hint,
    bool isRequired = false,
  }) {
    final fieldKey = _getFieldKey(title);
    final isExpanded = _expandedStates[fieldKey] ?? false;

    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              _onItemTap();
              setState(() {
                // Close all other expanded fields
                _expandedStates.updateAll((key, value) => false);
                // Toggle current field
                _expandedStates[fieldKey] = !isExpanded;
              });
            },
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 10.0,
              ),
              child: Row(
                children: [
                  // Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppConstants.lightGrey,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      icon,
                      color: AppConstants.primaryBlack,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Title and Value
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              title,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: AppConstants.primaryBlack,
                              ),
                            ),
                            if (isRequired)
                              Text(
                                ' *',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          controller.text.isEmpty ? hint : controller.text,
                          style: GoogleFonts.poppins(
                            fontSize: 11,
                            fontWeight: FontWeight.w400,
                            color: controller.text.isEmpty
                                ? AppConstants.mediumGrey
                                : AppConstants.primaryBlack,
                            fontStyle: controller.text.isEmpty
                                ? FontStyle.italic
                                : FontStyle.normal,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Edit Icon
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.edit_outlined,
                    size: 18,
                    color: AppConstants.mediumGrey,
                  ),
                ],
              ),
            ),
          ),
        ),

        // Inline Edit Field
        if (isExpanded)
          Container(
            padding: const EdgeInsets.fromLTRB(10.0, 0, 10.0, 10.0),
            child: _buildInlineEditField(
              controller,
              hint,
              isRequired,
              fieldKey,
            ),
          ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: 10.0),
      color: AppConstants.lightGrey.withValues(alpha: 0.5),
    );
  }

  String _getFieldKey(String title) {
    switch (title.toLowerCase()) {
      case 'first name':
        return 'firstName';
      case 'last name':
        return 'lastName';
      case 'address':
        return 'address';
      case 'locality':
        return 'locality';
      case 'city':
        return 'city';
      case 'state':
        return 'state';
      case 'country':
        return 'country';
      default:
        return title.toLowerCase().replaceAll(' ', '');
    }
  }

  Widget _buildInlineEditField(
    TextEditingController controller,
    String hint,
    bool isRequired,
    String fieldKey,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppConstants.lightGrey, width: 1),
      ),
      child: Column(
        children: [
          TextField(
            controller: controller,
            autofocus: true,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppConstants.primaryBlack,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(10.0),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 10.0,
              vertical: 6.0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _expandedStates[fieldKey] = false;
                    });
                  },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    minimumSize: Size.zero,
                  ),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    if (isRequired && controller.text.trim().isEmpty) {
                      Get.snackbar(
                        'Required Field',
                        '${_getDisplayTitle(fieldKey)} is required',
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                        snackPosition: SnackPosition.TOP,
                      );
                      return;
                    }
                    setState(() {
                      _expandedStates[fieldKey] = false;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryBlack,
                    foregroundColor: AppConstants.primaryWhite,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    minimumSize: Size.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  child: Text(
                    'Save',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getDisplayTitle(String fieldKey) {
    switch (fieldKey) {
      case 'firstName':
        return 'First Name';
      case 'lastName':
        return 'Last Name';
      case 'address':
        return 'Address';
      case 'locality':
        return 'Locality';
      case 'city':
        return 'City';
      case 'state':
        return 'State';
      case 'country':
        return 'Country';
      default:
        return fieldKey;
    }
  }

  void _showImagePickerDialog(UserProfileController controller) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: const BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppConstants.radiusMedium),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Profile Photo',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImagePickerOption(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: () {
                    Get.back();
                    _pickImage(ImageSource.camera, controller);
                  },
                ),
                _buildImagePickerOption(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () {
                    Get.back();
                    _pickImage(ImageSource.gallery, controller);
                  },
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            TextButton(
              onPressed: () => Get.back(),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            ),
            child: Icon(icon, color: AppConstants.primaryBlack, size: 28),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(
    ImageSource source,
    UserProfileController controller,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final File imageFile = File(image.path);
        await controller.uploadProfileImage(imageFile);
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to pick image: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  Future<void> _saveProfile() async {
    final controller = Get.find<UserProfileController>();

    // Validate required fields
    if (_firstNameController.text.trim().isEmpty) {
      Get.snackbar(
        'Validation Error',
        'First name is required',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    if (_lastNameController.text.trim().isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Last name is required',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    try {
      // Get current profile and update with new values
      final currentProfile = controller.userProfile.value;
      if (currentProfile == null) {
        Get.snackbar(
          'Error',
          'Profile data not available',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );
        return;
      }

      // Create updated address
      final updatedAddress = Address(
        street: _addressController.text.trim(),
        area: _localityController.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        country: _countryController.text.trim(),
        pinCode: currentProfile.address?.pinCode,
        latitude: currentProfile.address?.latitude,
        longitude: currentProfile.address?.longitude,
      );

      // Create updated profile with new values
      final updatedProfile = currentProfile.copyWith(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        address: updatedAddress,
      );

      // Call the update profile method
      await controller.updateUserProfile(updatedProfile);

      // Show success message
      Get.snackbar(
        'Success',
        'Profile updated successfully',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      // Go back to profile screen
      Get.back();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update profile: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
