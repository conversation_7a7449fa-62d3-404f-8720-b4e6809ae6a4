import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/user/user_profile_controller.dart';
import '../../../routes/app_routes.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _bounceController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _bounceAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.elasticOut),
    );

    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  void _onItemTap() {
    _bounceController.forward().then((_) {
      _bounceController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<UserProfileController>();

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return _buildLoadingState();
          } else if (controller.hasError.value) {
            return _buildErrorState(controller);
          } else {
            return _buildProfileContent(controller);
          }
        }),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: AppConstants.primaryBlack,
            strokeWidth: 3,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'Loading Profile...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppConstants.mediumGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(UserProfileController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.error_outline,
                size: 40,
                color: Colors.red,
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            Text(
              'Something went wrong',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Text(
              controller.errorMessage.value,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            ElevatedButton(
              onPressed: controller.refreshProfile,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                ),
                elevation: 2,
              ),
              child: Text(
                'Try Again',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(UserProfileController controller) {
    return RefreshIndicator(
      onRefresh: controller.refreshProfile,
      color: AppConstants.primaryBlack,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        child: SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _slideController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile Header
                _buildProfileHeader(controller),

                const SizedBox(height: AppConstants.paddingXLarge),

                // Basic Details Section
                _buildBasicDetailsSection(controller),

                const SizedBox(height: AppConstants.paddingLarge),

                // Settings Section
                _buildSettingsSection(controller),

                const SizedBox(height: AppConstants.paddingLarge),

                // App Version
                _buildAppVersion(controller),

                const SizedBox(height: AppConstants.paddingXLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader(UserProfileController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryBlack,
            AppConstants.primaryBlack.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Avatar
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppConstants.primaryWhite, width: 4),
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipOval(
              child: controller.hasProfileImage
                  ? CachedNetworkImage(
                      imageUrl: controller.profileImageUrl!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppConstants.lightGrey,
                        child: const Icon(
                          Icons.person,
                          size: 50,
                          color: AppConstants.mediumGrey,
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: AppConstants.lightGrey,
                        child: Center(
                          child: Text(
                            controller.userInitials,
                            style: GoogleFonts.poppins(
                              fontSize: 32,
                              fontWeight: FontWeight.w600,
                              color: AppConstants.primaryBlack,
                            ),
                          ),
                        ),
                      ),
                    )
                  : Container(
                      color: AppConstants.lightGrey,
                      child: Center(
                        child: Text(
                          controller.userInitials,
                          style: GoogleFonts.poppins(
                            fontSize: 22,
                            fontWeight: FontWeight.w500,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                      ),
                    ),
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // User Name
          Text(
            controller.displayName,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryWhite,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // User Email
          Text(
            controller.userEmail,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.primaryWhite.withValues(alpha: 0.8),
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Edit Profile Button
          ElevatedButton.icon(
            onPressed: () {
              try {
                // Ensure profile data is loaded before navigating
                if (controller.userProfile.value != null &&
                    !controller.isLoading.value) {
                  // Debug: Test if button tap works
                  Get.snackbar(
                    'Debug',
                    'Button tapped! Profile: ${controller.userProfile.value?.firstName ?? 'null'}',
                    backgroundColor: Colors.blue,
                    colorText: Colors.white,
                    snackPosition: SnackPosition.TOP,
                  );
                  // Try navigation
                  Get.to(() => const EditProfileScreen());
                } else if (controller.isLoading.value) {
                  Get.snackbar(
                    'Please Wait',
                    'Profile data is still loading...',
                    backgroundColor: AppConstants.primaryBlack,
                    colorText: AppConstants.primaryWhite,
                    snackPosition: SnackPosition.TOP,
                  );
                } else {
                  Get.snackbar(
                    'Error',
                    'Profile data not available. Please refresh.',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                    snackPosition: SnackPosition.TOP,
                  );
                }
              } catch (e) {
                Get.snackbar(
                  'Error',
                  'Navigation failed: ${e.toString()}',
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                  snackPosition: SnackPosition.TOP,
                );
              }
            },
            icon: const Icon(Icons.edit_outlined, size: 18),
            label: Text(
              'Edit Profile',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryWhite,
              foregroundColor: AppConstants.primaryBlack,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingSmall,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              elevation: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicDetailsSection(UserProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 16),
          child: Text(
            'Basic Details',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),
        ),

        // Details Container
        Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.08),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildAnimatedListTile(
                icon: Icons.email_outlined,
                title: 'Email',
                subtitle: controller.userEmail,
                trailing: controller.userProfile.value?.isEmailVerified == true
                    ? const Icon(Icons.verified, color: Colors.green, size: 20)
                    : null,
                onTap: () {
                  _onItemTap();
                  // Navigate to email settings
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.phone_outlined,
                title: 'Phone Number',
                subtitle: controller.userProfile.value?.phone ?? 'Not provided',
                onTap: () {
                  _onItemTap();
                  // Navigate to phone settings
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.location_on_outlined,
                title: 'Address',
                subtitle:
                    controller.userProfile.value?.address?.fullAddress ??
                    'Not provided',
                onTap: () {
                  _onItemTap();
                  // Navigate to address settings
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsSection(UserProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 16),
          child: Text(
            'Settings',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),
        ),

        // Settings Container
        Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.08),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildAnimatedListTile(
                icon: Icons.payment_outlined,
                title: 'Payment Methods',
                subtitle: 'Manage your payment options',
                onTap: () {
                  _onItemTap();
                  // Navigate to payment methods
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.notifications_outlined,
                title: 'Notifications',
                subtitle: 'Manage notification preferences',
                onTap: () {
                  _onItemTap();
                  // Navigate to notifications
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.favorite_outline,
                title: 'Favorite Barber',
                subtitle: 'Your preferred barbers',
                onTap: () {
                  _onItemTap();
                  // Navigate to favorite barbers
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.history_outlined,
                title: 'Appointment History',
                subtitle: 'View past appointments',
                onTap: () {
                  _onItemTap();
                  // Navigate to appointment history
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.language_outlined,
                title: 'Language',
                subtitle: 'English',
                onTap: () {
                  _onItemTap();
                  // Navigate to language settings
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.help_outline,
                title: 'Help and Support',
                subtitle: 'Get help and contact support',
                onTap: () {
                  _onItemTap();
                  controller.navigateToSecurityNotes();
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.info_outline,
                title: 'About Us',
                subtitle: 'Learn more about our app',
                onTap: () {
                  _onItemTap();
                  // Navigate to about us
                },
              ),

              _buildDivider(),

              _buildAnimatedListTile(
                icon: Icons.logout,
                title: 'Logout',
                subtitle: 'Sign out of your account',
                isLogout: true,
                onTap: () {
                  _onItemTap();
                  controller.showLogoutConfirmation();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAppVersion(UserProfileController controller) {
    return Center(
      child: Obx(
        () => Text(
          'App Version ${controller.appVersion.value}',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppConstants.mediumGrey,
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    bool isLogout = false,
    required VoidCallback onTap,
  }) {
    return ScaleTransition(
      scale: _bounceAnimation,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingLarge,
              vertical: AppConstants.paddingSmall,
            ),
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isLogout
                    ? Colors.red.withValues(alpha: 0.1)
                    : AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                icon,
                color: isLogout ? Colors.red : AppConstants.primaryBlack,
                size: 20,
              ),
            ),
            title: Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isLogout ? Colors.red : AppConstants.primaryBlack,
              ),
            ),
            subtitle: Text(
              subtitle,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppConstants.mediumGrey,
              ),
            ),
            trailing:
                trailing ??
                (isLogout
                    ? null
                    : const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: AppConstants.mediumGrey,
                      )),
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      color: AppConstants.lightGrey.withValues(alpha: 0.5),
    );
  }
}
