import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/user/user_appointment_controller.dart';
import '../../../models/user_appointment_models.dart';
import '../../../widgets/custom_back_button.dart';

class MyAppointmentsScreen extends StatelessWidget {
  const MyAppointmentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(UserAppointmentController());

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Tab Bar
          _buildTabBar(controller),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              children: [
                _buildUpcomingTab(controller),
                _buildCompletedTab(controller),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      // leading: Padding(
      //   padding: const EdgeInsets.all(AppConstants.paddingSmall),
      //   child: CustomBackButton(),
      // ),
      title: Text(
        'My Appointments',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildTabBar(UserAppointmentController controller) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: TabBar(
        controller: controller.tabController,
        indicator: BoxDecoration(
          color: AppConstants.primaryBlack,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: AppConstants.primaryWhite,
        unselectedLabelColor: AppConstants.mediumGrey,
        labelStyle: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontSize: 10,
          fontWeight: FontWeight.w300,
        ),
        tabs: [
          Obx(() => Tab(text: 'Upcoming (${controller.upcomingCount})')),
          Obx(() => Tab(text: 'Completed (${controller.completedCount})')),
        ],
      ),
    );
  }

  Widget _buildUpcomingTab(UserAppointmentController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildLoadingState();
      }

      if (controller.hasError.value) {
        return _buildErrorState(controller);
      }

      if (controller.upcomingAppointments.isEmpty) {
        return _buildEmptyState(
          'No Upcoming Appointments',
          'You don\'t have any upcoming appointments.',
          Icons.calendar_today_outlined,
        );
      }

      return RefreshIndicator(
        onRefresh: controller.refreshAppointments,
        child: ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingSmall),
          itemCount: controller.upcomingAppointments.length,
          itemBuilder: (context, index) {
            final appointment = controller.upcomingAppointments[index];
            return _buildUpcomingAppointmentCard(appointment, controller);
          },
        ),
      );
    });
  }

  Widget _buildCompletedTab(UserAppointmentController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildLoadingState();
      }

      if (controller.hasError.value) {
        return _buildErrorState(controller);
      }

      if (controller.completedAppointments.isEmpty) {
        return _buildEmptyState(
          'No Completed Appointments',
          'Your completed appointments will appear here.',
          Icons.history,
        );
      }

      return RefreshIndicator(
        onRefresh: controller.refreshAppointments,
        child: ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingSmall),
          itemCount: controller.completedAppointments.length,
          itemBuilder: (context, index) {
            final appointment = controller.completedAppointments[index];
            return _buildCompletedAppointmentCard(appointment, controller);
          },
        ),
      );
    });
  }

  Widget _buildUpcomingAppointmentCard(
    UserAppointment appointment,
    UserAppointmentController controller,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with salon name, status, and menu
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  appointment.saloonName.isEmpty
                      ? 'Unknown Salon'
                      : appointment.saloonName,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(appointment.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      appointment.status.toUpperCase(),
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildAppointmentMenu(appointment, controller),
                ],
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Barber name
          Text(
            'Barber: ${appointment.barberName.isEmpty ? 'Not assigned' : appointment.barberName}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppConstants.mediumGrey,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Booking type
          Text(
            'Booking: ${appointment.bookingType.isEmpty ? 'Unknown' : appointment.bookingType}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppConstants.mediumGrey,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Date and time
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppConstants.mediumGrey,
              ),
              const SizedBox(width: 4),
              Text(
                _formatAppointmentDate(appointment.appointmentDate),
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Service duration
          Row(
            children: [
              Icon(Icons.access_time, size: 16, color: AppConstants.mediumGrey),
              const SizedBox(width: 4),
              Text(
                _getServiceDuration(appointment.startTime, appointment.endTime),
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Services and categories
          if (appointment.services.isNotEmpty) ...[
            Text(
              'Services: ${appointment.serviceNames}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
              ),
            ),
            if (appointment.categoryNames.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                'Categories: ${appointment.categoryNames}',
                style: GoogleFonts.poppins(
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ],
          ],

          if (appointment.otp != null && appointment.otp!.isNotEmpty) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            _buildOTPSection(appointment.otp!),
          ],
        ],
      ),
    );
  }

  Widget _buildCompletedAppointmentCard(
    UserAppointment appointment,
    UserAppointmentController controller,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with salon name, status, and menu
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  appointment.saloonName.isEmpty
                      ? 'Unknown Salon'
                      : appointment.saloonName,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(appointment.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      appointment.status.toUpperCase(),
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildCompletedAppointmentMenu(appointment, controller),
                ],
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Barber name
          Text(
            'Barber: ${appointment.barberName.isEmpty ? 'Not assigned' : appointment.barberName}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppConstants.mediumGrey,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Date and time
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppConstants.mediumGrey,
              ),
              const SizedBox(width: 4),
              Text(
                _formatAppointmentDate(appointment.appointmentDate),
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Service duration
          Row(
            children: [
              Icon(Icons.access_time, size: 16, color: AppConstants.mediumGrey),
              const SizedBox(width: 4),
              Text(
                _getServiceDuration(appointment.startTime, appointment.endTime),
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Services and categories
          if (appointment.services.isNotEmpty) ...[
            Text(
              'Services: ${appointment.serviceNames}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
              ),
            ),
            if (appointment.categoryNames.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                'Categories: ${appointment.categoryNames}',
                style: GoogleFonts.poppins(
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildOTPSection(String otp) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.black54, Colors.black],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.security,
            color: AppConstants.primaryWhite,
            size: 20,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'OTP: $otp',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.primaryWhite,
                    letterSpacing: 2,
                  ),
                ),
                Text(
                  'Show this OTP to barber only',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppConstants.primaryWhite.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: AppConstants.primaryBlack),
    );
  }

  Widget _buildErrorState(UserAppointmentController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppConstants.mediumGrey,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Something went wrong',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              controller.errorMessage.value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            ElevatedButton(
              onPressed: controller.loadAppointments,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
              ),
              child: Text(
                'Try Again',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryWhite,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64, color: AppConstants.mediumGrey),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              subtitle,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelDialog(
    UserAppointment appointment,
    UserAppointmentController controller,
  ) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppConstants.primaryWhite,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.paddingLarge),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                Icons.cancel_outlined,
                color: Colors.red.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Text(
                'Cancel Appointment',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Are you sure you want to cancel this appointment? This action cannot be undone.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
                height: 1.4,
              ),
            ),
            const SizedBox(height: AppConstants.paddingLarge),
          ],
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Get.back(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppConstants.lightGrey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                    'Keep Appointment',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Get.back();
                    controller.cancelAppointment(appointment.id);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: AppConstants.primaryWhite,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    elevation: 0,
                  ),
                  child: Text(
                    'Yes, Cancel',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        actionsPadding: const EdgeInsets.fromLTRB(
          AppConstants.paddingLarge,
          0,
          AppConstants.paddingLarge,
          AppConstants.paddingLarge,
        ),
      ),
    );
  }

  void _showReviewDialog(
    UserAppointment appointment,
    UserAppointmentController controller,
  ) {
    int selectedStars = 5;
    final commentController = TextEditingController();

    Get.dialog(
      AlertDialog(
        backgroundColor: AppConstants.primaryWhite,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.paddingLarge),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                Icons.star_outline,
                color: Colors.amber.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Text(
                'Rate & Review',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'How was your experience at ${appointment.saloonName.isEmpty ? 'the salon' : appointment.saloonName}?',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
                height: 1.4,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            StatefulBuilder(
              builder: (context, setState) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedStars = index + 1;
                        });
                      },
                      child: Icon(
                        Icons.star,
                        size: 32,
                        color: index < selectedStars
                            ? Colors.amber
                            : AppConstants.lightGrey,
                      ),
                    );
                  }),
                );
              },
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: commentController,
              decoration: InputDecoration(
                hintText: 'Write a comment (optional)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Get.back(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppConstants.lightGrey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Get.back();
                    controller.submitReview(
                      appointmentId: appointment.id,
                      stars: selectedStars,
                      comment: commentController.text.trim().isEmpty
                          ? null
                          : commentController.text.trim(),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryBlack,
                    foregroundColor: AppConstants.primaryWhite,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    elevation: 0,
                  ),
                  child: Text(
                    'Submit Review',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        actionsPadding: const EdgeInsets.fromLTRB(
          AppConstants.paddingLarge,
          0,
          AppConstants.paddingLarge,
          AppConstants.paddingLarge,
        ),
      ),
    );
  }

  // Build appointment menu with reschedule and cancel options
  Widget _buildAppointmentMenu(
    UserAppointment appointment,
    UserAppointmentController controller,
  ) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: AppConstants.mediumGrey, size: 20),
      onSelected: (value) {
        switch (value) {
          case 'reschedule':
            if (appointment.canRescheduled) {
              _navigateToReschedule(appointment);
            } else {
              _showCannotRescheduleDialog();
            }
            break;
          case 'cancel':
            _showCancelDialog(appointment, controller);
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'reschedule',
          child: Row(
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: appointment.canRescheduled
                    ? AppConstants.primaryBlack
                    : AppConstants.darkGrey,
              ),
              const SizedBox(width: 8),
              Text(
                'Reschedule',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: appointment.canRescheduled
                      ? AppConstants.primaryBlack
                      : AppConstants.darkGrey,
                ),
              ),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'cancel',
          child: Row(
            children: [
              Icon(Icons.cancel, size: 16, color: Colors.red),
              const SizedBox(width: 8),
              Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build completed appointment menu with rebook and review options
  Widget _buildCompletedAppointmentMenu(
    UserAppointment appointment,
    UserAppointmentController controller,
  ) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: AppConstants.mediumGrey, size: 20),
      onSelected: (value) {
        switch (value) {
          case 'rebook':
            controller.rebookAppointment(appointment);
            break;
          case 'review':
            _showReviewDialog(appointment, controller);
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'rebook',
          child: Row(
            children: [
              Icon(Icons.refresh, size: 16, color: AppConstants.primaryBlack),
              const SizedBox(width: 8),
              Text(
                'Rebook',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'review',
          child: Row(
            children: [
              Icon(
                Icons.star,
                size: 16,
                color: appointment.isReviewed == true
                    ? AppConstants.lightGrey
                    : Colors.amber,
              ),
              const SizedBox(width: 8),
              Text(
                appointment.isReviewed == true ? 'Reviewed' : 'Rate & Review',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: appointment.isReviewed == true
                      ? AppConstants.lightGrey
                      : AppConstants.primaryBlack,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Show dialog when reschedule is not allowed
  void _showCannotRescheduleDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppConstants.primaryWhite,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.paddingLarge),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                Icons.schedule_outlined,
                color: Colors.orange.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Text(
                'Cannot Reschedule',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'This appointment cannot be rescheduled at this time. Please contact the salon directly if you need to make changes.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
                height: 1.4,
              ),
            ),
            const SizedBox(height: AppConstants.paddingLarge),
          ],
        ),
        actions: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
                elevation: 0,
              ),
              child: Text(
                'Got it',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
        actionsPadding: const EdgeInsets.fromLTRB(
          AppConstants.paddingLarge,
          0,
          AppConstants.paddingLarge,
          AppConstants.paddingLarge,
        ),
      ),
    );
  }

  // Format appointment date to "28 Dec 2025" format
  String _formatAppointmentDate(String dateString) {
    if (dateString.isEmpty) {
      return 'Unknown date';
    }

    try {
      final date = DateTime.tryParse(dateString);
      if (date != null) {
        final months = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ];

        return '${date.day} ${months[date.month - 1]} ${date.year}';
      }
    } catch (e) {
      // If parsing fails, return the original string
    }

    return dateString;
  }

  // Calculate service duration from start and end time
  String _getServiceDuration(String startTime, String endTime) {
    if (startTime.isEmpty || endTime.isEmpty) {
      return 'Duration unknown';
    }

    try {
      // Parse time strings (assuming HH:MM:SS format)
      final startParts = startTime.split(':');
      final endParts = endTime.split(':');

      if (startParts.length >= 2 && endParts.length >= 2) {
        final startHour = int.parse(startParts[0]);
        final startMinute = int.parse(startParts[1]);
        final endHour = int.parse(endParts[0]);
        final endMinute = int.parse(endParts[1]);

        // Create DateTime objects for today with the given times
        final now = DateTime.now();
        final startDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          startHour,
          startMinute,
        );
        final endDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          endHour,
          endMinute,
        );

        // Calculate duration
        final duration = endDateTime.difference(startDateTime);
        final totalMinutes = duration.inMinutes;

        if (totalMinutes <= 0) {
          return 'Duration unknown';
        }

        final hours = totalMinutes ~/ 60;
        final minutes = totalMinutes % 60;

        if (hours > 0 && minutes > 0) {
          return 'Service duration: ${hours}h ${minutes}m';
        } else if (hours > 0) {
          return 'Service duration: ${hours}h';
        } else {
          return 'Service duration: ${minutes}m';
        }
      }
    } catch (e) {
      // If parsing fails, show the time range
    }

    // Fallback to showing time range in HH:MM format
    final formattedStart = _formatTime(startTime);
    final formattedEnd = _formatTime(endTime);
    return '$formattedStart - $formattedEnd';
  }

  // Format time to HH:MM format
  String _formatTime(String timeString) {
    if (timeString.isEmpty) {
      return 'Unknown';
    }

    try {
      final parts = timeString.split(':');
      if (parts.length >= 2) {
        final hour = parts[0].padLeft(2, '0');
        final minute = parts[1].padLeft(2, '0');
        return '$hour:$minute';
      }
    } catch (e) {
      // If parsing fails, return original
    }

    return timeString;
  }

  // Helper method to get status color
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.green;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return AppConstants.mediumGrey;
    }
  }

  // Navigate to reschedule screen
  void _navigateToReschedule(UserAppointment appointment) {
    Get.toNamed(
      '/reschedule-appointment',
      arguments: {
        'saloonId': appointment.saloonId,
        'barberId': appointment.barberId,
        'serviceIds': appointment.services.map((s) => s.serviceId).toList(),
      },
    );
  }
}
