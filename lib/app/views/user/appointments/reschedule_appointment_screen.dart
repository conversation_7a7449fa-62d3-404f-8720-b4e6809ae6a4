import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../constants/app_constants.dart';
import '../../../models/booking_models.dart';
import '../../../widgets/custom_back_button.dart';
import '../../../../controller/reschedule_controller.dart';

class RescheduleAppointmentScreen extends StatelessWidget {
  const RescheduleAppointmentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RescheduleController());

    // Debug: Print parameters when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.debugPrintParameters();
    });

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(),
      body: Obx(() {
        if (controller.hasError.value && !controller.canReschedule.value) {
          return _buildErrorState(controller);
        }

        return Column(
          children: [
            // Main Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Warning Section
                    _buildWarningSection(),

                    const SizedBox(height: AppConstants.paddingLarge),

                    // Date Selection Section
                    _buildDateSelectionSection(controller),

                    const SizedBox(height: AppConstants.paddingLarge),

                    // Time Slots Section
                    Obx(
                      () => controller.selectedDate.value != null
                          ? _buildTimeSlotsSection(controller)
                          : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom Action Bar
            _buildBottomActionBar(controller),
          ],
        );
      }),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      leading: const CustomBackButton(),
      title: Text(
        'Reschedule Appointment',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildWarningSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: Colors.orange.shade200, width: 1),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange.shade600,
            size: 24,
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Text(
              'After rescheduling, you won\'t be able to reschedule this appointment again',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.orange.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelectionSection(RescheduleController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select New Date',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Choose your preferred appointment date',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withOpacity(0.1),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TableCalendar<DateTime>(
            firstDay: DateTime.now(),
            lastDay: DateTime.now().add(const Duration(days: 30)),
            focusedDay: DateTime.now(),
            calendarFormat: CalendarFormat.month,
            startingDayOfWeek: StartingDayOfWeek.monday,
            selectedDayPredicate: (day) {
              final selectedDate = controller.selectedDate.value;
              return selectedDate != null && isSameDay(selectedDate, day);
            },
            onDaySelected: (selectedDay, focusedDay) {
              if (selectedDay.isAfter(
                DateTime.now().subtract(const Duration(days: 1)),
              )) {
                controller.selectDate(selectedDay);
              }
            },
            calendarStyle: CalendarStyle(
              outsideDaysVisible: false,
              weekendTextStyle: GoogleFonts.poppins(
                color: AppConstants.primaryBlack,
              ),
              holidayTextStyle: GoogleFonts.poppins(
                color: AppConstants.primaryBlack,
              ),
              defaultTextStyle: GoogleFonts.poppins(
                color: AppConstants.primaryBlack,
              ),
              selectedDecoration: BoxDecoration(
                color: AppConstants.primaryBlack,
                shape: BoxShape.circle,
              ),
              todayDecoration: BoxDecoration(
                color: AppConstants.mediumGrey,
                shape: BoxShape.circle,
              ),
              disabledTextStyle: GoogleFonts.poppins(
                color: AppConstants.lightGrey,
              ),
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
              leftChevronIcon: Icon(
                Icons.chevron_left,
                color: AppConstants.primaryBlack,
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right,
                color: AppConstants.primaryBlack,
              ),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppConstants.mediumGrey,
              ),
              weekendStyle: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppConstants.mediumGrey,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSlotsSection(RescheduleController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Available Time Slots',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: AppConstants.primaryBlack,
              ),
            ),
            const Spacer(),
            Obx(
              () => controller.isLoadingSlots.value
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppConstants.primaryBlack,
                      ),
                    )
                  : IconButton(
                      onPressed: controller.fetchAvailableSlots,
                      icon: Icon(
                        Icons.refresh,
                        color: AppConstants.primaryBlack,
                      ),
                      tooltip: 'Refresh slots',
                    ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Obx(() {
          final selectedDate = controller.selectedDate.value;
          return Text(
            'Select your preferred time slot for ${selectedDate != null ? _formatDate(selectedDate) : 'selected date'}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
          );
        }),
        const SizedBox(height: AppConstants.paddingMedium),

        Obx(() {
          if (controller.isLoadingSlots.value) {
            return _buildLoadingSlotsState();
          }

          if (controller.availableTimeSlots.isEmpty) {
            return _buildEmptySlotsState();
          }

          return _buildTimeSlotsGrid(controller);
        }),
      ],
    );
  }

  Widget _buildLoadingSlotsState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withOpacity(0.5),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppConstants.primaryBlack),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Loading available slots...',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptySlotsState() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withOpacity(0.5),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          Icon(Icons.schedule, size: 48, color: AppConstants.mediumGrey),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No available slots',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.mediumGrey,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please select a different date',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotsGrid(RescheduleController controller) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2.5,
        crossAxisSpacing: AppConstants.paddingSmall,
        mainAxisSpacing: AppConstants.paddingSmall,
      ),
      itemCount: controller.availableTimeSlots.length,
      itemBuilder: (context, index) {
        final timeSlot = controller.availableTimeSlots[index];
        return _buildTimeSlotCard(timeSlot, controller);
      },
    );
  }

  Widget _buildTimeSlotCard(
    TimeSlot timeSlot,
    RescheduleController controller,
  ) {
    return Obx(() {
      final isSelected = controller.isTimeSlotSelected(timeSlot);

      return Container(
        decoration: BoxDecoration(
          color: isSelected
              ? AppConstants.primaryBlack
              : AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(
            color: isSelected
                ? AppConstants.primaryBlack
                : AppConstants.lightGrey,
            width: 1,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            onTap: () => controller.selectTimeSlot(timeSlot),
            child: Center(
              child: Text(
                timeSlot.displayTime12Hour,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: isSelected
                      ? AppConstants.primaryWhite
                      : AppConstants.primaryBlack,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildBottomActionBar(RescheduleController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Obx(
        () => SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: controller.canProceedWithReschedule
                ? controller.rescheduleAppointment
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryBlack,
              foregroundColor: AppConstants.primaryWhite,
              disabledBackgroundColor: AppConstants.lightGrey,
              disabledForegroundColor: AppConstants.mediumGrey,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              elevation: 0,
            ),
            child: controller.isRescheduling.value
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppConstants.primaryWhite,
                    ),
                  )
                : Text(
                    'Reschedule Now',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(RescheduleController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppConstants.mediumGrey),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Cannot Reschedule',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              controller.errorMessage.value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
              ),
              child: Text(
                'Go Back',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
