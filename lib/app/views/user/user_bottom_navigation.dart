import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../controllers/user/user_navigation_controller.dart';
import '../../controllers/user/user_profile_controller.dart';
import 'home/home_screen.dart';
import 'bookings/bookings_screen.dart';
import 'profile/profile_screen.dart';
import 'search/search_screen.dart';
import 'appointments/my_appointments_screen.dart';

class UserBottomNavigation extends StatelessWidget {
  const UserBottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    final UserNavigationController controller = Get.put(
      UserNavigationController(),
    );

    // Initialize UserProfileController early to prevent navigation issues
    Get.put(UserProfileController());

    final List<Widget> pages = [
      const HomeScreen(),
      const SearchScreen(),
      const MyAppointmentsScreen(),
      // const BookingsScreen(),
      const ProfileScreen(),
    ];

    return Scaffold(
      body: Obx(() => pages[controller.currentIndex.value]),
      bottomNavigationBar: Obx(
        () => Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeIndex,
            type: BottomNavigationBarType.fixed,
            backgroundColor: AppConstants.primaryWhite,
            selectedItemColor: AppConstants.primaryBlack,
            unselectedItemColor: AppConstants.mediumGrey,
            selectedLabelStyle: const TextStyle(
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: const TextStyle(
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
            elevation: 0,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined),
                activeIcon: Icon(Icons.home),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.search_outlined),
                activeIcon: Icon(Icons.search),
                label: 'Search',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.event_note_outlined),
                activeIcon: Icon(Icons.event_note),
                label: 'Appointments',
              ),
              // BottomNavigationBarItem(
              //   icon: Icon(Icons.calendar_today_outlined),
              //   activeIcon: Icon(Icons.calendar_today),
              //   label: 'Bookings',
              // ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_outline),
                activeIcon: Icon(Icons.person),
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
