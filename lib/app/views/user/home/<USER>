import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../widgets/stack_home_page_banner.dart';
import '../../../widgets/home_salon_card.dart';
import '../../../widgets/salon_card.dart';
import '../../../widgets/custom_app_bar.dart';
import '../../../controllers/user/home_controller.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late final HomeController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(HomeController());

    // Note: Don't auto-show location selection bottom sheet
    // User should see "Select your location" in app bar first
    // Bottom sheet will be shown when user taps on location in app bar or location prompt
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.lightGrey,
      appBar: const HomeAppBar(),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // const SizedBox(height: AppConstants.paddingLarge),

            // Search Bar
            // _buildSearchBar(context),
            const SizedBox(height: AppConstants.paddingLarge),

            // Advertising Banner
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.0),
              child: StackHomePageBanner(),
            ),

            const SizedBox(height: AppConstants.paddingXLarge),

            // Debug button (temporary)
            // Container(
            //   margin: const EdgeInsets.symmetric(
            //     horizontal: AppConstants.paddingLarge,
            //   ),
            //   child: ElevatedButton(
            //     onPressed: () {
            //       controller.testDashboardLoad();
            //     },
            //     child: const Text('Test Load Dashboard Data'),
            //   ),
            // ),
            // const SizedBox(height: AppConstants.paddingMedium),

            // Error handling
            Obx(() {
              if (controller.hasError) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10.0),
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 32,
                      ),
                      const SizedBox(height: 12.0),
                      Text(
                        'Failed to load salon data',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8.0),
                      Text(
                        controller.errorMessage,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Colors.red.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (controller.canRetry) ...[
                        const SizedBox(height: 12.0),
                        SizedBox(
                          height: 44.0,
                          child: ElevatedButton(
                            onPressed: () => controller.retryLoading(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppConstants.primaryBlack,
                              foregroundColor: AppConstants.primaryWhite,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 12.0,
                              ),
                            ),
                            child: Text(
                              'Retry',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // Loading indicator
            Obx(() {
              if (controller.isLoading) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10.0),
                  padding: const EdgeInsets.all(24.0),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: AppConstants.primaryBlack,
                      strokeWidth: 2.0,
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // Home Salon Section
            Obx(() {
              if (controller.showHomeSalon && controller.homeSalon != null) {
                final homeSalon = controller.homeSalon!;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 10.0, top: 16.0),
                      child: Text(
                        'Home Salon',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                    ),

                    // const SizedBox(height: AppConstants.paddingMedium),
                    HomeSalonCard(
                      salonName: homeSalon.displayName,
                      salonAddress: homeSalon.fullAddress,
                      salonImage: homeSalon.image,
                      salonProfileImage:
                          homeSalon.image, // Using same image for profile
                      onRebook: () {
                        controller.rebookHomeSalon();
                      },
                      onTap: () {
                        controller.navigateToSalonDetails(homeSalon.id);
                      },
                    ),

                    const SizedBox(height: AppConstants.paddingXLarge),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Nearest Salons Section
            Obx(() {
              if (controller.hasSelectedLocation.value &&
                  controller.showNearestSalons) {
                // Show nearest salons when location is selected
                return Column(
                  children: [
                    _buildSalonSection(
                      context,
                      'Nearest Salons',
                      controller.nearestSalons,
                      () {
                        controller.viewAllNearestSalons();
                      },
                    ),
                    const SizedBox(height: AppConstants.paddingXLarge),
                  ],
                );
              } else if (!controller.hasSelectedLocation.value) {
                // Show location selection prompt when no location is selected
                return Column(
                  children: [
                    _buildLocationPromptSection(context),
                    const SizedBox(height: AppConstants.paddingXLarge),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Popular Salons Section
            Obx(() {
              if (controller.showPopularSalons) {
                return _buildSalonSection(
                  context,
                  'Popular Salons',
                  controller.popularSalons,
                  () {
                    controller.viewAllPopularSalons();
                  },
                );
              }
              return const SizedBox.shrink();
            }),

            const SizedBox(height: 24.0),

            // Watermark
            _buildWatermark(),

            const SizedBox(height: 16.0),
          ],
        ),
      ),
    );
  }

  Widget _buildWatermark() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
      child: Center(
        child: Text(
          'Made with ❤️ by 2ezFlutterMasterAtif',
          style: GoogleFonts.poppins(
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: AppConstants.mediumGrey,
          ),
        ),
      ),
    );
  }

  Widget _buildSalonSection(
    BuildContext context,
    String title,
    List salons,
    VoidCallback onViewAll,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryBlack,
                ),
              ),
              TextButton(
                onPressed: onViewAll,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12.0,
                    vertical: 8.0,
                  ),
                  minimumSize: const Size(0, 36),
                ),
                child: Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        // Horizontal Salon List
        SizedBox(
          height: 240,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            itemCount: salons.length,
            itemBuilder: (context, index) {
              final salon = salons[index];
              return SalonCard(
                salonName: salon.displayName,
                salonAddress: salon.displayLocation,
                salonImage: salon.image,
                rating: salon.stars,
                distance: salon.displayDistance,
                shopNo: salon.shopNo,
                distanceKm: salon.displayDistanceKm,
                isHorizontal: true,
                onTap: () {
                  Get.find<HomeController>().navigateToSalonDetails(salon.id);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocationPromptSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10.0),
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Location icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.location_on,
              size: 30,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: 16),

          // Title
          Text(
            'Find Nearest Salons',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            'Select your location to discover salons near you',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Select Location Button
          SizedBox(
            width: double.infinity,
            height: 60,
            child: ElevatedButton(
              onPressed: () {
                controller.showLocationSelectionBottomSheet();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: AppConstants.primaryWhite,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Select Location',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryWhite,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
