import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/user/search_controller.dart' as search;
import '../../../models/search_salon_models.dart';

class SearchScreen extends StatelessWidget {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(search.SearchController());

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      body: Column(
        children: [
          SizedBox(height: 30),
          // Search Header
          _buildSearchHeader(controller),

          // Filter Chips
          _buildFilterChips(controller),

          // Content Area
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return _buildLoadingState();
              } else if (controller.shouldShowSearchHistory) {
                return _buildSearchHistoryView(controller);
              } else if (controller.shouldShowResults) {
                return _buildSearchResults(controller);
              } else if (controller.shouldShowEmptyState) {
                return _buildEmptyState(controller);
              } else {
                return _buildInitialState(controller);
              }
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader(search.SearchController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Search',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              border: Border.all(
                color: controller.isSearchFocused.value
                    ? AppConstants.primaryBlack
                    : Colors.transparent,
                width: 1,
              ),
            ),
            child: TextField(
              controller: controller.searchTextController,
              focusNode: controller.searchFocusNode,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: AppConstants.primaryBlack,
              ),
              decoration: InputDecoration(
                hintText: 'Search salons, services, or barbers...',
                hintStyle: GoogleFonts.poppins(
                  fontSize: 16,
                  color: AppConstants.mediumGrey,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppConstants.mediumGrey,
                  size: 24,
                ),
                suffixIcon: Obx(
                  () => controller.searchQuery.value.isNotEmpty
                      ? IconButton(
                          onPressed: controller.clearSearch,
                          icon: const Icon(
                            Icons.clear,
                            color: AppConstants.mediumGrey,
                            size: 20,
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingMedium,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips(search.SearchController controller) {
    return Obx(
      () => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: controller.showFilters.value ? 60 : 0,
        child: controller.showFilters.value
            ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                  vertical: AppConstants.paddingSmall,
                ),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.filterOptions.length,
                  itemBuilder: (context, index) {
                    final filter = controller.filterOptions[index];
                    final isSelected =
                        controller.selectedFilter.value == filter;

                    return Padding(
                      padding: const EdgeInsets.only(
                        right: AppConstants.paddingSmall,
                      ),
                      child: FilterChip(
                        label: Text(
                          filter.displayName,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isSelected
                                ? AppConstants.primaryWhite
                                : AppConstants.primaryBlack,
                          ),
                        ),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            controller.changeFilter(filter);
                          }
                        },
                        backgroundColor: AppConstants.lightGrey,
                        selectedColor: AppConstants.primaryBlack,
                        checkmarkColor: AppConstants.primaryWhite,
                        side: BorderSide.none,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            AppConstants.radiusLarge,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              )
            : const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: AppConstants.primaryBlack),
    );
  }

  Widget _buildSearchHistoryView(search.SearchController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches Header
          if (controller.searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Searches',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                TextButton(
                  onPressed: controller.clearSearchHistory,
                  child: Text(
                    'Clear All',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Recent Search Items
            ...controller.searchHistory.map(
              (item) => _buildHistoryItem(item, controller),
            ),

            const SizedBox(height: AppConstants.paddingLarge),
          ],

          // Popular Suggestions
          Text(
            'Popular Searches',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Popular Search Chips
          Wrap(
            spacing: AppConstants.paddingSmall,
            runSpacing: AppConstants.paddingSmall,
            children: controller.popularSuggestions.map((suggestion) {
              return ActionChip(
                label: Text(
                  suggestion.text,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                avatar: Icon(
                  suggestion.icon,
                  size: 14,
                  color: AppConstants.mediumGrey,
                ),
                onPressed: () => controller.onSuggestionTap(suggestion.text),
                backgroundColor: AppConstants.lightGrey,
                side: BorderSide.none,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // Trending Searches
          Text(
            'Trending',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Trending Search Items
          ...controller.trendingSearches.asMap().entries.map((entry) {
            final index = entry.key;
            final search = entry.value;
            return _buildTrendingItem(search, index + 1, controller);
          }),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(
    SearchHistoryItem item,
    search.SearchController controller,
  ) {
    return ListTile(
      leading: const Icon(
        Icons.history,
        color: AppConstants.mediumGrey,
        size: 20,
      ),
      title: Text(
        item.query,
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: AppConstants.primaryBlack,
        ),
      ),
      subtitle: Text(
        item.filterType.displayName,
        style: GoogleFonts.poppins(
          fontSize: 12,
          color: AppConstants.mediumGrey,
        ),
      ),
      trailing: IconButton(
        onPressed: () => controller.removeHistoryItem(item),
        icon: const Icon(Icons.close, color: AppConstants.mediumGrey, size: 18),
      ),
      onTap: () => controller.onHistoryItemTap(item),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildTrendingItem(
    String search,
    int rank,
    search.SearchController controller,
  ) {
    return ListTile(
      leading: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: rank <= 3 ? AppConstants.primaryBlack : AppConstants.lightGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text(
            rank.toString(),
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: rank <= 3
                  ? AppConstants.primaryWhite
                  : AppConstants.mediumGrey,
            ),
          ),
        ),
      ),
      title: Text(
        search,
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: AppConstants.primaryBlack,
        ),
      ),
      trailing: const Icon(
        Icons.trending_up,
        color: AppConstants.mediumGrey,
        size: 18,
      ),
      onTap: () => controller.onSuggestionTap(search),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSearchResults(search.SearchController controller) {
    return RefreshIndicator(
      onRefresh: controller.refreshSearch,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        itemCount: controller.searchResults.length,
        itemBuilder: (context, index) {
          final salon = controller.searchResults[index];
          return _buildSalonCard(salon);
        },
      ),
    );
  }

  Widget _buildSalonCard(SearchSalon salon) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          // Navigate to salon detail page
          if (salon.id != null) {
            Get.toNamed('/salon-detail', arguments: {'salonId': salon.id});
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Row(
          children: [
            // Salon Image
            ClipRRect(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              child: salon.hasImage
                  ? CachedNetworkImage(
                      imageUrl: salon.image!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        width: 80,
                        height: 80,
                        color: AppConstants.lightGrey,
                        child: const Icon(
                          Icons.image,
                          color: AppConstants.mediumGrey,
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 80,
                        height: 80,
                        color: AppConstants.lightGrey,
                        child: const Icon(
                          Icons.store,
                          color: AppConstants.mediumGrey,
                        ),
                      ),
                    )
                  : Container(
                      width: 80,
                      height: 80,
                      color: AppConstants.lightGrey,
                      child: const Icon(
                        Icons.store,
                        color: AppConstants.mediumGrey,
                        size: 32,
                      ),
                    ),
            ),

            const SizedBox(width: AppConstants.paddingMedium),

            // Salon Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Salon Name
                  Text(
                    salon.name,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Address
                  Text(
                    salon.shortAddress,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppConstants.mediumGrey,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Rating and Reviews
                  if (salon.hasRating) ...[
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          salon.formattedRating,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          salon.reviewText,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: AppConstants.mediumGrey,
                          ),
                        ),
                      ],
                    ),
                  ] else ...[
                    Text(
                      'No reviews yet',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Arrow Icon
            const Icon(
              Icons.arrow_forward_ios,
              color: AppConstants.mediumGrey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(search.SearchController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              controller.hasError.value
                  ? Icons.error_outline
                  : Icons.search_off,
              size: 50,
              color: AppConstants.mediumGrey,
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            Text(
              controller.hasError.value ? 'Search Failed' : 'No Results Found',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            Text(
              controller.emptyStateText,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),

            if (controller.hasError.value) ...[
              const SizedBox(height: AppConstants.paddingLarge),
              ElevatedButton(
                onPressed: controller.refreshSearch,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryBlack,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusSmall,
                    ),
                  ),
                ),
                child: Text(
                  'Try Again',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.primaryWhite,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState(search.SearchController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Welcome Message
          Icon(Icons.search, size: 50, color: AppConstants.mediumGrey),

          // const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'Find Your Perfect Salon',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          Text(
            'Search for salons, services, or barbers near you',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.paddingLarge * 2),

          // Quick Actions
          Text(
            'Quick Search',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Quick Action Buttons
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  'Salons',
                  Icons.store,
                  () => controller.searchByType(SearchFilterType.salon),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: _buildQuickActionButton(
                  'Services',
                  Icons.content_cut,
                  () => controller.searchByType(SearchFilterType.service),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: _buildQuickActionButton(
                  'Barbers',
                  Icons.person,
                  () => controller.searchByType(SearchFilterType.barber),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.lightGrey,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: AppConstants.primaryBlack),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryBlack,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
