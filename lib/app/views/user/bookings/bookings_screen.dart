import 'package:flutter/material.dart';
import '../../../constants/app_constants.dart';

class BookingsScreen extends StatelessWidget {
  const BookingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today,
              size: 80,
              color: AppConstants.mediumGrey,
            ),
            SizedBox(height: AppConstants.paddingLarge),
            Text(
              'Bookings Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Your booking history and upcoming appointments will be shown here',
              style: TextStyle(
                fontSize: 16,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
