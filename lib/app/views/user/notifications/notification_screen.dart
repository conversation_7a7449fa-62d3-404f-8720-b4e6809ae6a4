import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/app_constants.dart';
import '../../../controllers/notification_controller.dart';
import '../../../models/notification_model.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  late NotificationController controller;

  @override
  void initState() {
    super.initState();
    // Initialize controller and ensure API calls are made
    controller = Get.put(NotificationController());

    // Ensure the API is called after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  Future<void> _initializeData() async {
    // Force refresh to ensure API is called
    await controller.fetchNotifications(refresh: true);
    await controller.getUnreadCount();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(),
      body: GetBuilder<NotificationController>(
        builder: (controller) {
          return Obx(() {
            // Show loading state for initial load
            if (controller.isLoading.value &&
                controller.notifications.isEmpty) {
              return _buildLoadingState();
            }

            // Show error state if there's an error and no cached data
            if (controller.hasError.value && controller.notifications.isEmpty) {
              return _buildErrorState();
            }

            // Show empty state if no notifications
            if (!controller.isLoading.value &&
                controller.notifications.isEmpty) {
              return _buildEmptyState();
            }

            // Show notifications list
            return _buildNotificationsList();
          });
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(
          Icons.arrow_back_ios_new,
          color: AppConstants.primaryBlack,
          size: 20,
        ),
      ),
      title: Text(
        'Notifications',
        style: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
      actions: [
        Obx(() {
          // Show "Mark All Read" button only if there are unread notifications
          if (controller.notifications.any((n) => !n.isRead)) {
            return TextButton(
              onPressed: () async {
                await controller.markAllAsRead();
              },
              child: Text(
                'Mark All Read',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryBlack,
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        }),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppConstants.primaryBlack,
            strokeWidth: 2,
          ),
          SizedBox(height: 16),
          Text(
            'Loading notifications...',
            style: TextStyle(fontSize: 14, color: AppConstants.mediumGrey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppConstants.mediumGrey,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load notifications',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: 8),
            Obx(
              () => Text(
                controller.errorMessage.value.isNotEmpty
                    ? controller.errorMessage.value
                    : 'Something went wrong. Please try again.',
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                await controller.fetchNotifications(refresh: true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.notifications_none,
              size: 64,
              color: AppConstants.mediumGrey,
            ),
            const SizedBox(height: 16),
            Text(
              'No notifications yet',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You\'ll see your appointment updates and other notifications here',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                await controller.fetchNotifications(refresh: true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryBlack,
                foregroundColor: AppConstants.primaryWhite,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Refresh',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList() {
    return RefreshIndicator(
      onRefresh: () async {
        await controller.refreshNotifications();
      },
      color: AppConstants.primaryBlack,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        itemCount:
            controller.notifications.length +
            (controller.hasNextPage.value ? 1 : 0),
        itemBuilder: (context, index) {
          // Load more indicator
          if (index == controller.notifications.length) {
            if (controller.isLoadingMore.value) {
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: CircularProgressIndicator(
                    color: AppConstants.primaryBlack,
                    strokeWidth: 2,
                  ),
                ),
              );
            } else {
              // Trigger load more when reaching the end
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (controller.hasNextPage.value &&
                    !controller.isLoadingMore.value) {
                  controller.loadMoreNotifications();
                }
              });
              return const SizedBox(height: 20);
            }
          }

          final notification = controller.notifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification) {
    return Obx(() {
      final isMarkingAsRead = controller.isMarkingAsRead(notification.id);

      return Container(
        margin: const EdgeInsets.only(bottom: 12.0),
        decoration: BoxDecoration(
          color: notification.isRead
              ? AppConstants.primaryWhite
              : AppConstants.lightGrey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppConstants.lightGrey.withValues(alpha: 0.5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppConstants.lightGrey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: InkWell(
          onTap: () async {
            // Mark as read when tapped if not already read
            if (!notification.isRead) {
              await controller.markAsRead(notification.id);
            }
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row with icon, title, and status
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Notification icon
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: notification.isRead
                            ? AppConstants.lightGrey.withValues(alpha: 0.5)
                            : AppConstants.primaryBlack,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.check,
                          color: notification.isRead
                              ? AppConstants.primaryBlack
                              : AppConstants.lightGrey,
                        ),
                        // child: Text(
                        //   notification.notificationIcon,
                        //   style: TextStyle(
                        //     fontSize: 16,
                        //     color: notification.isRead
                        //         ? AppConstants.mediumGrey
                        //         : AppConstants.primaryWhite,
                        //   ),
                        // ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Title and content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  notification.title,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppConstants.primaryBlack,
                                  ),
                                ),
                              ),
                              // Unread indicator
                              if (!notification.isRead)
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: AppConstants.primaryBlack,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Text(
                            notification.body,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppConstants.mediumGrey,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Mark as read button (only for unread notifications)
                    if (!notification.isRead)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        child: isMarkingAsRead
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppConstants.primaryBlack,
                                ),
                              )
                            : IconButton(
                                onPressed: () async {
                                  await controller.markAsRead(notification.id);
                                },
                                icon: const Icon(
                                  Icons.mark_email_read,
                                  color: AppConstants.primaryBlack,
                                  size: 20,
                                ),
                                tooltip: 'Mark as read',
                                constraints: const BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                                padding: EdgeInsets.zero,
                              ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Date and time row
                Row(
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 14,
                      color: AppConstants.mediumGrey,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '${notification.formattedDate} at ${notification.formattedTime}',
                      style: GoogleFonts.poppins(
                        fontSize: 11,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                    const Spacer(),
                    // Read status text
                    if (notification.isRead)
                      Text(
                        'Read',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: AppConstants.mediumGrey,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  @override
  void dispose() {
    // Clean up if needed
    super.dispose();
  }
}
