import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../controllers/booking_controller.dart';
import '../widgets/custom_back_button.dart';
import '../widgets/step_indicator.dart';
import 'booking/service_selection_step.dart';
import 'booking/booking_summary_step.dart';
import 'booking/confirmation_step.dart';

class BookingPage extends StatelessWidget {
  const BookingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(BookingController());

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(context, controller),
      body: Column(
        children: [
          // Step Indicator
          Obx(
            () =>
                BookingStepIndicator(currentStep: controller.currentStep.value),
          ),

          // Divider
          Container(
            height: 1,
            color: AppConstants.lightGrey,
            margin: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingLarge,
            ),
          ),

          // Step Content
          Expanded(
            child: AnimatedBuilder(
              animation: controller.slideAnimation,
              builder: (context, child) {
                return SlideTransition(
                  position: controller.slideAnimation,
                  child: Obx(() => _buildStepContent(controller)),
                );
              },
            ),
          ),

          // Bottom Action Bar
          _buildBottomActionBar(context, controller),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    BookingController controller,
  ) {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      leading: Obx(
        () => controller.currentStep.value == 2
            ? const SizedBox.shrink() // Hide back button on confirmation step
            : Padding(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                child: CustomBackButton(),
              ),
      ),
      title: Obx(
        () => Text(
          controller.currentStepTitle,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
        ),
      ),
      centerTitle: true,
      actions: [
        Obx(
          () => controller.currentStep.value == 0
              ? IconButton(
                  onPressed: () => _showBookingInfo(context),
                  icon: Icon(
                    Icons.info_outline,
                    color: AppConstants.primaryBlack,
                  ),
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildStepContent(BookingController controller) {
    switch (controller.currentStep.value) {
      case 0:
        return ServiceSelectionStep(controller: controller);
      case 1:
        return BookingSummaryStep(controller: controller);
      case 2:
        return ConfirmationStep(controller: controller);
      default:
        return ServiceSelectionStep(controller: controller);
    }
  }

  Widget _buildBottomActionBar(
    BuildContext context,
    BookingController controller,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Obx(
          () => Row(
            children: [
              // Previous Button
              if (controller.canGoToPreviousStep)
                Expanded(
                  child: OutlinedButton(
                    onPressed: controller.previousStep,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppConstants.primaryBlack),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppConstants.radiusMedium,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(
                      'Previous',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ),
                ),

              if (controller.canGoToPreviousStep)
                const SizedBox(width: AppConstants.paddingMedium),

              // Next/Complete Button
              Expanded(
                flex: controller.canGoToPreviousStep ? 1 : 2,
                child: ElevatedButton(
                  onPressed: controller.canProceedToNextStep
                      ? () => _handleNextStep(controller)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryBlack,
                    foregroundColor: AppConstants.primaryWhite,
                    disabledBackgroundColor: AppConstants.mediumGrey,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusMedium,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    elevation: 2,
                  ),
                  child: Text(
                    _getNextButtonText(controller.currentStep.value),
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getNextButtonText(int currentStep) {
    switch (currentStep) {
      case 0:
        return 'Continue to Summary';
      case 1:
        return 'Proceed to Payment';
      case 2:
        return 'Done';
      default:
        return 'Next';
    }
  }

  void _handleNextStep(BookingController controller) {
    if (controller.currentStep.value == 1) {
      // Handle proceed to payment from summary step
      controller.handleProceedToPayment();
    } else if (controller.currentStep.value == 2) {
      // Handle final booking completion - navigate to home
      Get.offAllNamed('/user-bottom-navigation');
    } else {
      controller.nextStep();
    }
  }

  void _showBookingInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Booking Information',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Follow these steps to complete your booking:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildInfoStep('1', 'Select your desired services'),
            _buildInfoStep('2', 'Choose your preferred date'),
            _buildInfoStep('3', 'Pick an available time slot'),
            _buildInfoStep('4', 'Review your booking details'),
            _buildInfoStep('5', 'Complete payment'),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Note: Time slots are updated in real-time and may change based on availability.',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppConstants.mediumGrey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Got it',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoStep(String number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppConstants.primaryBlack,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryWhite,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.primaryBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
