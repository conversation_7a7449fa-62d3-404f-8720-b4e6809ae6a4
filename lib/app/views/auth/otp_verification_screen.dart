import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';
import '../../controllers/auth_controller.dart';

class OtpVerificationScreen extends StatefulWidget {
  const OtpVerificationScreen({super.key});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final List<TextEditingController> _otpControllers = List.generate(
    4,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  bool _isResendEnabled = false;
  int _resendTimer = AppConstants.otpResendTimer;

  String identifier = '';
  bool fromLogin = false;
  bool fromRegistration = false;
  bool needsVerification = false;

  // Get auth controller
  final AuthController _authController = Get.find<AuthController>();

  @override
  void initState() {
    super.initState();
    _getArguments();
    _startResendTimer();
  }

  void _getArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    identifier = args?['identifier'] ?? args?['email'] ?? args?['phone'] ?? '';
    fromLogin = args?['fromLogin'] ?? false;
    fromRegistration = args?['fromRegistration'] ?? false;
    needsVerification = args?['needsVerification'] ?? false;

    // For backward compatibility
    if (identifier.isEmpty) {
      identifier = args?['email'] ?? args?['phone'] ?? '';
    }
  }

  void _startResendTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _resendTimer > 0) {
        setState(() {
          _resendTimer--;
        });
        _startResendTimer();
      } else if (mounted) {
        setState(() {
          _isResendEnabled = true;
        });
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _handleVerifyOtp() {
    final otp = _otpControllers.map((controller) => controller.text).join();

    if (otp.length != 4) {
      Get.snackbar('Error', 'Please enter a valid 4-digit OTP');
      return;
    }

    if (identifier.isEmpty) {
      Get.snackbar('Error', 'Invalid verification data');
      return;
    }

    _authController.verifyOtp(identifier: identifier, otp: otp);
  }

  void _handleResendOtp() {
    if (!_isResendEnabled) return;

    setState(() {
      _isResendEnabled = false;
      _resendTimer = AppConstants.otpResendTimer;
    });

    _startResendTimer();

    if (identifier.isNotEmpty) {
      _authController.resendOtp(identifier);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify OTP'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  kToolbarHeight -
                  AppConstants.paddingLarge * 2,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  const SizedBox(height: AppConstants.paddingLarge),

                  // Header
                  _buildHeader(context),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // OTP Input Fields
                  _buildOtpInputFields(),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Resend OTP
                  _buildResendSection(context),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Verify Button
                  Obx(
                    () => CustomButton(
                      text: 'Verify OTP',
                      onPressed: _handleVerifyOtp,
                      isLoading: _authController.isLoading.value,
                    ),
                  ),

                  const Spacer(),

                  // Change Email
                  _buildChangeEmailLink(context),

                  const SizedBox(height: AppConstants.paddingLarge),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppConstants.lightGrey,
            borderRadius: BorderRadius.circular(AppConstants.radiusXLarge),
          ),
          child: const Icon(
            Icons.email_outlined,
            size: 40,
            color: AppConstants.primaryBlack,
          ),
        ),

        const SizedBox(height: AppConstants.paddingLarge),

        Text(
          needsVerification
              ? 'Email Verification Required'
              : 'Verify Your Email',
          style: Theme.of(
            context,
          ).textTheme.displaySmall?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        Text(
          needsVerification
              ? 'Please verify your email before logging in. Click "Resend OTP" to get a new verification code.'
              : 'We have sent a 4-digit verification code to',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: AppConstants.mediumGrey),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Text(
          identifier,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOtpInputFields() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(4, (index) {
        return SizedBox(
          width: 50,
          height: 60,
          child: TextFormField(
            controller: _otpControllers[index],
            focusNode: _focusNodes[index],
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            maxLength: 1,
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            decoration: InputDecoration(
              counterText: '',
              filled: true,
              fillColor: AppConstants.lightGrey,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                borderSide: const BorderSide(
                  color: AppConstants.primaryBlack,
                  width: 2,
                ),
              ),
            ),
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            onChanged: (value) {
              if (value.isNotEmpty && index < 3) {
                _focusNodes[index + 1].requestFocus();
              } else if (value.isEmpty && index > 0) {
                _focusNodes[index - 1].requestFocus();
              }
            },
          ),
        );
      }),
    );
  }

  Widget _buildResendSection(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Didn't receive the code? ",
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppConstants.mediumGrey),
        ),
        GestureDetector(
          onTap: _handleResendOtp,
          child: Text(
            _isResendEnabled ? 'Resend OTP' : 'Resend in ${_resendTimer}s',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: _isResendEnabled
                  ? AppConstants.primaryBlack
                  : AppConstants.mediumGrey,
              fontWeight: FontWeight.w600,
              decoration: _isResendEnabled ? TextDecoration.underline : null,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChangeEmailLink(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.back();
      },
      child: Text(
        'Change Email Address',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppConstants.primaryBlack,
          fontWeight: FontWeight.w600,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }
}
