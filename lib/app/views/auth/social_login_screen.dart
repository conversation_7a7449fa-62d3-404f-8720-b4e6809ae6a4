import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_constants.dart';
import '../../routes/app_routes.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/social_login_button.dart';

class SocialLoginScreen extends StatelessWidget {
  const SocialLoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              // Top spacing
              const SizedBox(height: AppConstants.paddingXLarge * 2),

              // App Logo and Welcome Text
              _buildHeader(context),

              const SizedBox(height: AppConstants.paddingXLarge * 2),

              // Social Login Buttons
              _buildSocialLoginSection(),

              const SizedBox(height: AppConstants.paddingXLarge),

              // Divider
              _buildDivider(),

              const SizedBox(height: AppConstants.paddingXLarge),

              // Email Login/Register Buttons
              _buildEmailSection(),

              const SizedBox(height: AppConstants.paddingXLarge * 2),

              // Terms and Privacy
              _buildTermsAndPrivacy(context),

              const SizedBox(height: AppConstants.paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // App Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: AppConstants.primaryGradient,
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          child: const Icon(
            Icons.content_cut,
            size: 40,
            color: AppConstants.primaryWhite,
          ),
        ),

        const SizedBox(height: AppConstants.paddingLarge),

        // Welcome Text
        Text(
          'Welcome to',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(color: AppConstants.mediumGrey),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Text(
          AppConstants.appName,
          style: Theme.of(
            context,
          ).textTheme.displaySmall?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        Text(
          'Book your salon appointments with ease',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: AppConstants.mediumGrey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSocialLoginSection() {
    return Column(
      children: [
        SocialLoginButton(
          icon: Icons.g_mobiledata,
          text: 'Continue with Google',
          onPressed: () {
            // TODO: Implement Google login
            Get.snackbar('Info', 'Google login will be implemented');
          },
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        SocialLoginButton(
          icon: Icons.facebook,
          text: 'Continue with Facebook',
          backgroundColor: const Color(0xFF1877F2),
          onPressed: () {
            // TODO: Implement Facebook login
            Get.snackbar('Info', 'Facebook login will be implemented');
          },
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Row(
      children: [
        const Expanded(
          child: Divider(color: AppConstants.lightGrey, thickness: 1),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: Text(
            'OR',
            style: TextStyle(
              color: AppConstants.mediumGrey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const Expanded(
          child: Divider(color: AppConstants.lightGrey, thickness: 1),
        ),
      ],
    );
  }

  Widget _buildEmailSection() {
    return Column(
      children: [
        CustomButton(
          text: 'Login with Email',
          onPressed: () {
            Get.toNamed(AppRoutes.emailLogin);
          },
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        CustomButton(
          text: 'Register with Email',
          isOutlined: true,
          onPressed: () {
            Get.toNamed(AppRoutes.emailRegister);
          },
        ),
      ],
    );
  }

  Widget _buildTermsAndPrivacy(BuildContext context) {
    return Text.rich(
      TextSpan(
        text: 'By continuing, you agree to our ',
        style: Theme.of(context).textTheme.bodySmall,
        children: [
          TextSpan(
            text: 'Terms of Service',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.primaryBlack,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.underline,
            ),
          ),
          const TextSpan(text: ' and '),
          TextSpan(
            text: 'Privacy Policy',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.primaryBlack,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }
}
