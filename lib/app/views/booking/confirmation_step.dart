import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../constants/app_constants.dart';
import '../../controllers/booking_controller.dart';

class ConfirmationStep extends StatelessWidget {
  final BookingController controller;

  const ConfirmationStep({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final paymentConfirmation = controller.paymentConfirmationData.value;

      if (paymentConfirmation == null) {
        return _buildLoadingState();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Success Icon
            _buildSuccessIcon(),

            const SizedBox(height: AppConstants.paddingLarge),

            // Success Message
            _buildSuccessMessage(),

            const SizedBox(height: AppConstants.paddingLarge),

            // OTP Card
            _buildOTPCard(paymentConfirmation['otp'] as String?),

            const SizedBox(height: AppConstants.paddingLarge),

            // Booking Details Card
            _buildBookingDetailsCard(),

            const SizedBox(height: AppConstants.paddingLarge),

            // Payment Details Card
            _buildPaymentDetailsCard(),

            const SizedBox(height: AppConstants.paddingLarge),
          ],
        ),
      );
    });
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryBlack),
      ),
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.green,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: const Icon(
        Icons.check,
        color: AppConstants.primaryWhite,
        size: 40,
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Column(
      children: [
        Text(
          'Payment Successful!',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Your appointment has been confirmed',
          style: GoogleFonts.poppins(
            fontSize: 16,
            color: AppConstants.mediumGrey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOTPCard(String? otp) {
    if (otp == null || otp.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green, Colors.green.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.security,
            color: AppConstants.primaryWhite,
            size: 32,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Your Appointment OTP',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppConstants.primaryWhite,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingLarge,
              vertical: AppConstants.paddingMedium,
            ),
            decoration: BoxDecoration(
              color: AppConstants.primaryWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              border: Border.all(
                color: AppConstants.primaryWhite.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Text(
              otp,
              style: GoogleFonts.poppins(
                fontSize: 32,
                fontWeight: FontWeight.w900,
                color: AppConstants.primaryWhite,
                letterSpacing: 6,
              ),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '⚠️ Show this OTP at the salon for your appointment',
            style: GoogleFonts.poppins(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryWhite.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingDetailsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Booking Details',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Appointment ID
          Obx(() {
            final bookingData = controller.bookingData.value;
            if (bookingData != null) {
              return _buildDetailRow(
                'Appointment ID',
                bookingData.appointmentId.substring(0, 8).toUpperCase(),
              );
            }
            return const SizedBox.shrink();
          }),

          const SizedBox(height: AppConstants.paddingSmall),

          // Barber Name
          Obx(
            () =>
                _buildDetailRow('Barber', controller.selectedBarberName.value),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Date & Time
          Obx(() {
            final date = controller.selectedDate.value;
            final timeSlot = controller.selectedTimeSlot.value;

            if (date != null && timeSlot != null) {
              return _buildDetailRow(
                'Date & Time',
                '${controller.formatDate(date)} at ${timeSlot.displayTime12Hour}',
              );
            }
            return const SizedBox.shrink();
          }),

          const SizedBox(height: AppConstants.paddingSmall),

          // Services
          Obx(() {
            final services = controller.selectedServices;
            return _buildDetailRow(
              'Services',
              '${services.length} service${services.length > 1 ? 's' : ''} selected',
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPaymentDetailsCard() {
    return Obx(() {
      final bookingData = controller.bookingData.value;
      final paymentData = controller.paymentConfirmationData.value;

      if (bookingData == null) {
        return const SizedBox.shrink();
      }

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildDetailRow('Payment Method', 'Razorpay Gateway'),

            const SizedBox(height: AppConstants.paddingSmall),

            _buildDetailRow('Amount Paid', bookingData.formattedAmount),

            const SizedBox(height: AppConstants.paddingSmall),

            _buildDetailRow('Razorpay Order ID', bookingData.razorpayOrderId),

            if (paymentData != null && paymentData['paymentId'] != null) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              _buildDetailRow(
                'Razorpay Payment ID',
                paymentData['paymentId'] as String,
              ),
            ],

            if (paymentData != null && paymentData['signature'] != null) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              _buildDetailRow(
                'Payment Signature',
                '${(paymentData['signature'] as String).substring(0, 16)}...',
              ),
            ],

            const SizedBox(height: AppConstants.paddingSmall),

            _buildDetailRow('Payment Status', 'Completed ✓', isSuccess: true),
          ],
        ),
      );
    });
  }

  Widget _buildDetailRow(String label, String value, {bool isSuccess = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isSuccess ? Colors.green : AppConstants.primaryBlack,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}
