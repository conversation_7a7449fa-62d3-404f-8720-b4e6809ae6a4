import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../constants/app_constants.dart';
import '../../controllers/booking_controller.dart';
import '../../models/booking_models.dart';

class ServiceSelectionStep extends StatelessWidget {
  final BookingController controller;

  const ServiceSelectionStep({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Services Section
          _buildServicesSection(),

          const SizedBox(height: AppConstants.paddingLarge),

          // Date Selection Section
          _buildDateSelectionSection(),

          const SizedBox(height: AppConstants.paddingLarge),

          // Time Slots Section
          Obx(
            () => controller.selectedDate.value != null
                ? _buildTimeSlotsSection()
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildServicesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Services',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Choose the services you want to book',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        Obx(
          () => controller.availableServices.isEmpty
              ? _buildEmptyServicesState()
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: controller.availableServices.length,
                  itemBuilder: (context, index) {
                    final service = controller.availableServices[index];
                    return _buildServiceCard(service);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyServicesState() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withOpacity(0.5),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          Icon(Icons.design_services, size: 48, color: AppConstants.mediumGrey),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No services available',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.mediumGrey,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please go back and select a salon with available services',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(service) {
    return Obx(() {
      final isSelected = controller.isServiceSelected(service.id);

      return AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          border: Border.all(
            color: isSelected
                ? AppConstants.primaryBlack
                : AppConstants.lightGrey,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            onTap: () => controller.toggleServiceSelection(service.id),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  // Service Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppConstants.lightGrey,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                      child: service.hasImage
                          ? CachedNetworkImage(
                              imageUrl: service.serviceimage,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppConstants.primaryBlack,
                                ),
                              ),
                              errorWidget: (context, url, error) => Icon(
                                Icons.design_services,
                                color: AppConstants.mediumGrey,
                              ),
                            )
                          : Icon(
                              Icons.design_services,
                              color: AppConstants.mediumGrey,
                            ),
                    ),
                  ),

                  const SizedBox(width: AppConstants.paddingMedium),

                  // Service Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          service.displayName,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          service.displayCategory,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: AppConstants.mediumGrey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: AppConstants.mediumGrey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              service.displayDuration,
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: AppConstants.primaryBlack,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Price and Selection
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        service.displayPrice,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                      const SizedBox(height: 8),
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppConstants.primaryBlack
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected
                                ? AppConstants.primaryBlack
                                : AppConstants.mediumGrey,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: isSelected
                            ? Icon(
                                Icons.check,
                                size: 16,
                                color: AppConstants.primaryWhite,
                              )
                            : null,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildDateSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Date',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Choose your preferred appointment date',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withOpacity(0.1),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TableCalendar<DateTime>(
            firstDay: DateTime.now(),
            lastDay: DateTime.now().add(const Duration(days: 30)),
            focusedDay: DateTime.now(),
            calendarFormat: CalendarFormat.month,
            startingDayOfWeek: StartingDayOfWeek.monday,
            selectedDayPredicate: (day) {
              final selectedDate = controller.selectedDate.value;
              return selectedDate != null && isSameDay(selectedDate, day);
            },
            onDaySelected: (selectedDay, focusedDay) {
              if (selectedDay.isAfter(
                DateTime.now().subtract(const Duration(days: 1)),
              )) {
                controller.selectDate(selectedDay);
              }
            },
            calendarStyle: CalendarStyle(
              outsideDaysVisible: false,
              weekendTextStyle: GoogleFonts.poppins(
                color: AppConstants.primaryBlack,
              ),
              holidayTextStyle: GoogleFonts.poppins(
                color: AppConstants.primaryBlack,
              ),
              defaultTextStyle: GoogleFonts.poppins(
                color: AppConstants.primaryBlack,
              ),
              selectedDecoration: BoxDecoration(
                color: AppConstants.primaryBlack,
                shape: BoxShape.circle,
              ),
              todayDecoration: BoxDecoration(
                color: AppConstants.mediumGrey.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              disabledTextStyle: GoogleFonts.poppins(
                color: AppConstants.mediumGrey.withOpacity(0.5),
              ),
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
              leftChevronIcon: Icon(
                Icons.chevron_left,
                color: AppConstants.primaryBlack,
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right,
                color: AppConstants.primaryBlack,
              ),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppConstants.mediumGrey,
              ),
              weekendStyle: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppConstants.mediumGrey,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSlotsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Available Time Slots',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: AppConstants.primaryBlack,
              ),
            ),
            const Spacer(),
            Obx(
              () => controller.isLoadingSlots.value
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppConstants.primaryBlack,
                      ),
                    )
                  : IconButton(
                      onPressed: controller.fetchAvailableSlots,
                      icon: Icon(
                        Icons.refresh,
                        color: AppConstants.primaryBlack,
                      ),
                      tooltip: 'Refresh slots',
                    ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Obx(() {
          if (controller.selectedServiceIds.isEmpty) {
            return Text(
              'Please select services first to see available time slots',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
            );
          }

          final selectedDate = controller.selectedDate.value;
          return Text(
            'Select your preferred time slot for ${selectedDate != null ? _formatDate(selectedDate) : 'selected date'}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
          );
        }),
        const SizedBox(height: AppConstants.paddingMedium),

        Obx(() {
          if (controller.isLoadingSlots.value) {
            return _buildLoadingSlotsState();
          }

          if (controller.availableTimeSlots.isEmpty) {
            return _buildEmptySlotsState();
          }

          return _buildTimeSlotsGrid();
        }),
      ],
    );
  }

  Widget _buildLoadingSlotsState() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withOpacity(0.5),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          CircularProgressIndicator(color: AppConstants.primaryBlack),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Loading available time slots...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.mediumGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySlotsState() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withOpacity(0.5),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          Icon(Icons.schedule, size: 48, color: AppConstants.mediumGrey),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No available time slots',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.mediumGrey,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please try selecting a different date or check back later',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotsGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2.5,
        crossAxisSpacing: AppConstants.paddingSmall,
        mainAxisSpacing: AppConstants.paddingSmall,
      ),
      itemCount: controller.availableTimeSlots.length,
      itemBuilder: (context, index) {
        final timeSlot = controller.availableTimeSlots[index];
        return _buildTimeSlotCard(timeSlot);
      },
    );
  }

  Widget _buildTimeSlotCard(TimeSlot timeSlot) {
    return Obx(() {
      final isSelected = controller.isTimeSlotSelected(timeSlot);

      return AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected
              ? AppConstants.primaryBlack
              : AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(
            color: isSelected
                ? AppConstants.primaryBlack
                : AppConstants.lightGrey,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            onTap: () => controller.selectTimeSlot(timeSlot),
            child: Center(
              child: Text(
                timeSlot.displayTime12Hour,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: isSelected
                      ? AppConstants.primaryWhite
                      : AppConstants.primaryBlack,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      );
    });
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
