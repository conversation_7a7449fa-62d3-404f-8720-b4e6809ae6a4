import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../constants/app_constants.dart';
import '../../controllers/booking_controller.dart';
import '../../models/appointment_booking_models.dart';

class BookingPaymentScreen extends StatelessWidget {
  const BookingPaymentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final BookingController controller = Get.find<BookingController>();

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      appBar: _buildAppBar(),
      body: _buildBody(controller),
    );
  }

  // App Bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppConstants.primaryBlack,
          size: 20,
        ),
      ),
      title: Text(
        'Payment Summary',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  // Body
  Widget _buildBody(BookingController controller) {
    return Obx(() {
      final bookingData = controller.bookingData.value;

      if (bookingData == null) {
        return _buildErrorState();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking Summary Card
            _buildBookingSummaryCard(controller, bookingData),

            const SizedBox(height: AppConstants.paddingLarge),

            // Payment Details Card
            _buildPaymentDetailsCard(bookingData),

            const SizedBox(height: AppConstants.paddingLarge),

            // Payment Instructions
            _buildPaymentInstructions(),

            const SizedBox(height: AppConstants.paddingLarge),

            // Continue to Payment Button
            _buildContinueToPaymentButton(controller, bookingData),
          ],
        ),
      );
    });
  }

  // Error State
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppConstants.mediumGrey,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Booking data not found',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppConstants.mediumGrey,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryBlack,
              foregroundColor: AppConstants.primaryWhite,
            ),
            child: Text(
              'Go Back',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Booking Summary Card
  Widget _buildBookingSummaryCard(
    BookingController controller,
    AppointmentBookingData bookingData,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Booking Summary',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Appointment ID
          _buildSummaryRow(
            'Appointment ID',
            bookingData.appointmentId.substring(0, 8).toUpperCase(),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Barber Name
          Obx(
            () =>
                _buildSummaryRow('Barber', controller.selectedBarberName.value),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Date & Time
          Obx(() {
            final date = controller.selectedDate.value;
            final timeSlot = controller.selectedTimeSlot.value;

            if (date != null && timeSlot != null) {
              return _buildSummaryRow(
                'Date & Time',
                '${controller.formatDate(date)} at ${timeSlot.displayTime12Hour}',
              );
            }
            return const SizedBox.shrink();
          }),

          const SizedBox(height: AppConstants.paddingSmall),

          // Services
          Obx(() {
            final services = controller.selectedServices;
            return _buildSummaryRow(
              'Services',
              '${services.length} service${services.length > 1 ? 's' : ''} selected',
            );
          }),
        ],
      ),
    );
  }

  // Payment Details Card
  Widget _buildPaymentDetailsCard(AppointmentBookingData bookingData) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryBlack,
            AppConstants.primaryBlack.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Details',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppConstants.primaryWhite,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          _buildPaymentRow('Total Amount', bookingData.formattedTotalAmount),

          if (bookingData.discountAmount > 0) ...[
            const SizedBox(height: AppConstants.paddingSmall),
            _buildPaymentRow(
              'Discount',
              '- ${bookingData.formattedDiscountAmount}',
            ),
          ],

          const SizedBox(height: AppConstants.paddingSmall),
          const Divider(color: AppConstants.primaryWhite),
          const SizedBox(height: AppConstants.paddingSmall),

          _buildPaymentRow(
            'Amount to Pay',
            bookingData.formattedAmount,
            isTotal: true,
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          Text(
            'Order ID: ${bookingData.razorpayOrderId}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppConstants.primaryWhite.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Payment Instructions
  Widget _buildPaymentInstructions() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppConstants.primaryBlack,
                size: 20,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                'Payment Instructions',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '• You will be redirected to Razorpay payment gateway\n'
            '• Choose your preferred payment method\n'
            '• Complete the payment securely\n'
            '• You will receive a confirmation once payment is successful\n'
            '• Keep your appointment OTP safe for salon visit',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppConstants.darkGrey,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  // Continue to Payment Button
  Widget _buildContinueToPaymentButton(
    BookingController controller,
    AppointmentBookingData bookingData,
  ) {
    return Obx(
      () => SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton(
          onPressed: controller.isProcessingPayment.value
              ? null
              : () => _proceedToPayment(controller, bookingData),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryBlack,
            foregroundColor: AppConstants.primaryWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            elevation: 2,
          ),
          child: controller.isProcessingPayment.value
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppConstants.primaryWhite,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Processing...',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.payment, size: 20),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Continue to Payment',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      bookingData.formattedAmount,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  // Proceed to Payment
  void _proceedToPayment(
    BookingController controller,
    AppointmentBookingData bookingData,
  ) {
    // Validate booking data before proceeding
    if (bookingData.razorpayOrderId.isEmpty) {
      Get.snackbar(
        'Error',
        'Invalid order ID. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (bookingData.amount <= 0) {
      Get.snackbar(
        'Error',
        'Invalid payment amount. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // Open Razorpay payment gateway directly from controller
    controller.openRazorpayPayment(bookingData);
  }

  // Helper method to build summary rows
  Widget _buildSummaryRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppConstants.mediumGrey,
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  // Helper method to build payment rows
  Widget _buildPaymentRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            color: AppConstants.primaryWhite,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: isTotal ? 18 : 14,
            fontWeight: FontWeight.w700,
            color: AppConstants.primaryWhite,
          ),
        ),
      ],
    );
  }
}
