import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../constants/app_constants.dart';
import '../../controllers/booking_controller.dart';
import '../../models/appointment_booking_models.dart';

class BookingSummaryStep extends StatelessWidget {
  final BookingController controller;

  const BookingSummaryStep({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    // Update booking summary when this step is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.selectedBarberName.value = _getBarberName();
      controller.changePaymentMode(controller.selectedPaymentMode.value);
    });

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Booking Summary',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Barber Information
          _buildBarberInfo(),

          const SizedBox(height: AppConstants.paddingLarge),

          // Selected Services
          _buildSelectedServices(),

          const SizedBox(height: AppConstants.paddingLarge),

          // Date and Time
          _buildDateTimeInfo(),

          const SizedBox(height: AppConstants.paddingLarge),

          // Payment Mode Selection
          _buildPaymentModeSelection(),

          const SizedBox(height: AppConstants.paddingLarge),

          // Total Summary
          _buildTotalSummary(),
        ],
      ),
    );
  }

  // Get barber name from arguments or controller
  String _getBarberName() {
    final arguments = Get.arguments as Map<String, dynamic>? ?? {};
    final barberName = arguments['barberName'] as String?;

    if (barberName != null && barberName.isNotEmpty) {
      return barberName;
    }

    // Try to get from barberId if barberName is not available
    final barberId = arguments['barberId'] as String?;
    if (barberId != null && barberId.isNotEmpty) {
      return 'Barber ID: ${barberId.length > 8 ? barberId.substring(0, 8) : barberId}';
    }

    return 'Selected Barber';
  }

  // Build barber information section
  Widget _buildBarberInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(25),
            ),
            child: const Icon(
              Icons.person,
              color: AppConstants.mediumGrey,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Barber',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppConstants.mediumGrey,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.selectedBarberName.value,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build payment mode selection
  Widget _buildPaymentModeSelection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Mode',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          Obx(
            () => Column(
              children: PaymentMode.values.map((mode) {
                final isSelected = controller.selectedPaymentMode.value == mode;
                return GestureDetector(
                  onTap: () => controller.changePaymentMode(mode),
                  child: Container(
                    margin: const EdgeInsets.only(
                      bottom: AppConstants.paddingSmall,
                    ),
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppConstants.primaryBlack.withValues(alpha: 0.05)
                          : AppConstants.lightGrey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusSmall,
                      ),
                      border: Border.all(
                        color: isSelected
                            ? AppConstants.primaryBlack
                            : AppConstants.lightGrey,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppConstants.primaryBlack
                                  : AppConstants.mediumGrey,
                              width: 2,
                            ),
                            color: isSelected
                                ? AppConstants.primaryBlack
                                : AppConstants.primaryWhite,
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  size: 12,
                                  color: AppConstants.primaryWhite,
                                )
                              : null,
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                mode.displayName,
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.primaryBlack,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                mode.description,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: AppConstants.mediumGrey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (mode != PaymentMode.payAtSalon)
                          Obx(() {
                            final amount =
                                controller
                                    .bookingSummary
                                    .value
                                    ?.payableAmount ??
                                0.0;
                            return Text(
                              '₹${amount.toStringAsFixed(2)}',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppConstants.primaryBlack,
                              ),
                            );
                          }),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedServices() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Services',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          Obx(
            () => ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.selectedServices.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final service = controller.selectedServices[index];
                return Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            service.displayName,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppConstants.primaryBlack,
                            ),
                          ),
                          Text(
                            service.displayDuration,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: AppConstants.mediumGrey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      service.displayPrice,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Appointment Details',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          Obx(
            () => Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 20,
                      color: AppConstants.mediumGrey,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Date:',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      () {
                        final selectedDate = controller.selectedDate.value;
                        return selectedDate != null
                            ? _formatDate(selectedDate)
                            : 'Not selected';
                      }(),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 20,
                      color: AppConstants.mediumGrey,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Time:',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      controller.selectedTimeSlot.value?.displayTime12Hour ??
                          'Not selected',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalSummary() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryBlack,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total Summary',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: AppConstants.primaryWhite,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          Obx(
            () => Column(
              children: [
                Row(
                  children: [
                    Text(
                      'Total Duration:',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      controller.formattedTotalDuration,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Row(
                  children: [
                    Text(
                      'Total Price:',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      controller.formattedTotalPrice,
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
