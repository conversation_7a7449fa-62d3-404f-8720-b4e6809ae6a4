import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../controllers/salon_detail_controller.dart';
import '../utils/salon_status_utils.dart';
import '../widgets/custom_back_button.dart';

class SalonDetailPage extends StatelessWidget {
  const SalonDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SalonDetailController());

    return Scaffold(
      backgroundColor: AppConstants.primaryWhite,
      body: Obx(() {
        if (controller.isLoading.value && !controller.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.hasError.value && !controller.hasData) {
          return _buildErrorWidget(controller);
        }

        return _buildContent(context, controller);
      }),
      floatingActionButton: Obx(() {
        if (controller.showReviewFAB) {
          return FloatingActionButton(
            onPressed: controller.openReviewModal,
            backgroundColor: AppConstants.primaryBlack,
            foregroundColor: AppConstants.primaryWhite,
            child: const Icon(Icons.rate_review),
          );
        }
        return const SizedBox.shrink();
      }),
      bottomNavigationBar: Obx(() {
        if (controller.hasData) {
          return _buildBottomActionBar(context, controller);
        }
        return const SizedBox.shrink();
      }),
    );
  }

  Widget _buildErrorWidget(SalonDetailController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppConstants.mediumGrey),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              'Failed to load salon details',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              controller.errorMessage.value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppConstants.mediumGrey,
              ),
              textAlign: TextAlign.center,
            ),
            if (controller.canRetry) ...[
              const SizedBox(height: AppConstants.paddingLarge),
              ElevatedButton(
                onPressed: controller.retryLoading,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryBlack,
                  foregroundColor: AppConstants.primaryWhite,
                ),
                child: const Text('Retry'),
              ),
            ],
            const SizedBox(height: AppConstants.paddingMedium),
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, SalonDetailController controller) {
    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [_buildSliverAppBar(context, controller)];
      },
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: NeverScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Salon Information Section
                  _buildSalonInfoSection(context, controller),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Tabbed Content Section
                  _buildTabbedSection(context, controller),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return SliverAppBar(
      expandedHeight: 300,
      floating: false,
      pinned: true,
      automaticallyImplyLeading: false,
      backgroundColor: AppConstants.primaryWhite,
      flexibleSpace: FlexibleSpaceBar(
        background: Obx(() {
          if (controller.hasImages) {
            return _buildImageCarousel(controller);
          } else {
            return _buildPlaceholderImage();
          }
        }),
      ),
      leading: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        child: CustomBackButtonTransparent(),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.all(AppConstants.paddingSmall),
          child: Obx(
            () => CustomIconButtonTransparent(
              icon: controller.isHomeSalon.value
                  ? Icons.favorite
                  : Icons.favorite_border,
              onPressed: controller.toggleHomeSalon,
              tooltip: controller.isHomeSalon.value
                  ? 'Remove from home salon'
                  : 'Add to home salon',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageCarousel(SalonDetailController controller) {
    final images = controller.salonData.value!.images;

    // Initialize page controller when building carousel
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.pageController == null) {
        controller.initializePageController();
      }
    });

    return Stack(
      children: [
        PageView.builder(
          controller: controller.pageController,
          itemCount: images.length,
          onPageChanged: (index) {
            controller.setImageIndex(index);
            // Restart auto-scroll when user manually changes page
            controller.stopAutoScroll();
            controller.startAutoScroll();
          },
          itemBuilder: (context, index) {
            return CachedNetworkImage(
              imageUrl: images[index].imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppConstants.lightGrey,
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => _buildPlaceholderImage(),
            );
          },
        ),

        // Image count indicator (top-right)
        Positioned(
          top: 15,
          right: 15,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppConstants.primaryBlack.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Obx(
              () => Text(
                '${controller.currentImageIndex.value + 1}/${images.length}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryWhite,
                ),
              ),
            ),
          ),
        ),

        // Image indicators (bottom)
        if (images.length > 1)
          Positioned(
            bottom: 15,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: images.asMap().entries.map((entry) {
                return Obx(
                  () => Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: controller.currentImageIndex.value == entry.key
                          ? AppConstants.primaryWhite
                          : AppConstants.primaryWhite.withValues(alpha: 0.5),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: AppConstants.lightGrey,
      child: Center(
        child: Icon(Icons.image, size: 64, color: AppConstants.mediumGrey),
      ),
    );
  }

  Widget _buildSalonInfoSection(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Obx(() {
      final salonData = controller.salonData.value;
      if (salonData == null) return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Salon Name with professional typography
            Text(
              salonData.saloonName,
              style: GoogleFonts.poppins(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
                height: 1.2,
              ),
            ),

            const SizedBox(height: 12),

            // Address with improved presentation
            Container(
              padding: const EdgeInsets.all(10.0),
              decoration: BoxDecoration(
                color: AppConstants.lightGrey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 14,
                    color: AppConstants.primaryBlack,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      salonData.fullAddress,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.primaryBlack,
                        height: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Rating and Operating Hours Row
            Row(
              children: [
                // Star Rating with background
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.star, size: 14, color: Colors.amber),
                      const SizedBox(width: 4),
                      Text(
                        salonData.displayStars,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // Operating Hours with background
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.lightGrey.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 14,
                        color: AppConstants.primaryBlack,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        salonData.operatingHours,
                        style: GoogleFonts.poppins(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Dynamic salon status
            const SizedBox(height: 12),
            _buildSalonStatus(salonData),

            // Show More Content Section
            const SizedBox(height: 15),
            Obx(
              () => Column(
                children: [
                  if (controller.showMoreContent.value) ...[
                    // Additional salon information
                    Container(
                      padding: const EdgeInsets.all(10.0),
                      decoration: BoxDecoration(
                        color: AppConstants.lightGrey.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'About This Salon',
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: AppConstants.primaryBlack,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Professional salon offering premium services with experienced staff. We provide a comfortable and hygienic environment for all our customers.',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              fontWeight: FontWeight.w400,
                              color: AppConstants.mediumGrey,
                              height: 1.4,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Icon(
                                Icons.verified,
                                size: 14,
                                color: Colors.green,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Verified Business',
                                style: GoogleFonts.poppins(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Show More/Less Button
                  const SizedBox(height: 10),
                  GestureDetector(
                    onTap: controller.toggleShowMore,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppConstants.mediumGrey),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            controller.showMoreContent.value
                                ? 'Show Less'
                                : 'Show More',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: AppConstants.primaryBlack,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            controller.showMoreContent.value
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            size: 16,
                            color: AppConstants.primaryBlack,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// Build dynamic salon status indicator
  Widget _buildSalonStatus(salonData) {
    final status = SalonStatusUtils.getSalonStatus(
      startTime: salonData.saloonStart,
      endTime: salonData.saloonEnd,
      offDays: salonData.offDays,
    );

    final statusText = SalonStatusUtils.getStatusText(status);
    final statusColor = SalonStatusUtils.getStatusColor(status);

    Color displayColor;
    IconData displayIcon;

    switch (statusColor) {
      case SalonStatusColor.green:
        displayColor = Colors.green;
        displayIcon = Icons.check_circle;
        break;
      case SalonStatusColor.red:
        displayColor = Colors.red;
        displayIcon = Icons.cancel;
        break;
      case SalonStatusColor.grey:
        displayColor = Colors.grey;
        displayIcon = Icons.help_outline;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: displayColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(displayIcon, size: 14, color: displayColor),
          const SizedBox(width: 4),
          Text(
            statusText,
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: displayColor,
            ),
          ),
          // Show off days info if closed today
          if (status == SalonStatus.closedToday &&
              salonData.offDays.isNotEmpty) ...[
            const SizedBox(width: 4),
            Text(
              '(${salonData.offDays})',
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w400,
                color: displayColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTabbedSection(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Column(
      children: [
        // Professional Tab Bar
        Container(
          height: 50,
          margin: const EdgeInsets.symmetric(horizontal: 10.0),
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryBlack.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: controller.tabController != null
              ? TabBar(
                  controller: controller.tabController!,
                  indicator: BoxDecoration(
                    color: AppConstants.primaryBlack,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: AppConstants.primaryBlack.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorPadding: const EdgeInsets.all(4),
                  labelPadding: EdgeInsets.zero,
                  padding: EdgeInsets.zero,
                  dividerColor: Colors.transparent,
                  labelColor: AppConstants.primaryWhite,
                  unselectedLabelColor: AppConstants.mediumGrey,
                  labelStyle: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                  tabs: const [
                    Tab(text: 'Services'),
                    Tab(text: 'Barbers'),
                    Tab(text: 'Reviews'),
                  ],
                )
              : const SizedBox.shrink(),
        ),
        SizedBox(height: AppConstants.paddingMedium),

        // Tab Content
        SizedBox(
          height: 400, // Fixed height for tab content
          child: controller.tabController != null
              ? TabBarView(
                  controller: controller.tabController!,
                  children: [
                    _buildServicesTab(context, controller),
                    _buildBarbersTab(context, controller),
                    _buildReviewsTab(context, controller),
                  ],
                )
              : const Center(child: CircularProgressIndicator()),
        ),
      ],
    );
  }

  Widget _buildBottomActionBar(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 15.0),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 56, // Increased height to prevent text cutoff
          child: ElevatedButton(
            onPressed: controller.bookAppointment,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryBlack,
              foregroundColor: AppConstants.primaryWhite,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
              padding: const EdgeInsets.symmetric(
                vertical: 16,
              ), // Proper vertical padding
            ),
            child: Text(
              'Book an Appointment',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                height: 1.2, // Proper line height
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServicesTab(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Obx(() {
      if (controller.services.isEmpty) {
        return _buildEmptyState('No services available', Icons.design_services);
      }

      final displayServices = controller.services.take(3).toList();

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Column(
          children: [
            // Compact Services List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.only(top: 10),
                itemCount: displayServices.length,
                itemBuilder: (context, index) {
                  final service = displayServices[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    padding: const EdgeInsets.all(10.0),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryWhite,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryBlack.withValues(
                            alpha: 0.05,
                          ),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Service Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Service Name
                              Text(
                                service.displayName,
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.primaryBlack,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              // Category and Duration Row
                              Row(
                                children: [
                                  Text(
                                    service.displayCategory,
                                    style: GoogleFonts.poppins(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400,
                                      color: AppConstants.mediumGrey,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppConstants.lightGrey,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      service.displayDuration,
                                      style: GoogleFonts.poppins(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                        color: AppConstants.primaryBlack,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Price
                        Text(
                          service.displayPrice,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Compact View All Button
            if (controller.services.length > 3)
              Container(
                width: double.infinity,
                height: 40,
                margin: const EdgeInsets.only(bottom: 10),
                child: OutlinedButton(
                  onPressed: () =>
                      _showServicesBottomSheet(context, controller),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppConstants.mediumGrey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  child: Text(
                    'View All Services (${controller.services.length})',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildBarbersTab(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Obx(() {
      if (controller.barbers.isEmpty) {
        return _buildEmptyState('No barbers available', Icons.person);
      }

      final displayBarbers = controller.barbers.take(3).toList();

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Column(
          children: [
            // Compact Barbers List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.only(top: 10),
                itemCount: displayBarbers.length,
                itemBuilder: (context, index) {
                  final barber = displayBarbers[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    padding: const EdgeInsets.all(10.0),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryWhite,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryBlack.withValues(
                            alpha: 0.05,
                          ),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Compact Avatar
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: AppConstants.lightGrey,
                          backgroundImage: barber.hasProfileImage
                              ? CachedNetworkImageProvider(barber.profileimage)
                              : null,
                          child: !barber.hasProfileImage
                              ? Icon(
                                  Icons.person,
                                  color: AppConstants.mediumGrey,
                                  size: 18,
                                )
                              : null,
                        ),
                        const SizedBox(width: 10),
                        // Barber Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Name
                              Text(
                                barber.displayName,
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.primaryBlack,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              // Bio
                              Text(
                                barber.displayBio,
                                style: GoogleFonts.poppins(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: AppConstants.mediumGrey,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              // Rating Row
                              Row(
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 12,
                                    color: Colors.amber,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    barber.displayRating,
                                    style: GoogleFonts.poppins(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                      color: AppConstants.primaryBlack,
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    barber.displayReviews,
                                    style: GoogleFonts.poppins(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400,
                                      color: AppConstants.mediumGrey,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Compact Book Button
                        ElevatedButton(
                          onPressed: () => controller.bookWithBarber(barber.id),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConstants.primaryBlack,
                            foregroundColor: AppConstants.primaryWhite,
                            minimumSize: const Size(60, 32),
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          child: Text(
                            'Book',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Compact View All Button
            if (controller.barbers.length > 3)
              Container(
                width: double.infinity,
                height: 40,
                margin: const EdgeInsets.only(bottom: 10),
                child: OutlinedButton(
                  onPressed: () => _showBarbersBottomSheet(context, controller),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppConstants.mediumGrey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  child: Text(
                    'View All Barbers (${controller.barbers.length})',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildReviewsTab(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Obx(() {
      if (controller.reviews.isEmpty) {
        return _buildEmptyState('No reviews yet', Icons.rate_review);
      }

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: ListView.builder(
          padding: const EdgeInsets.only(top: 10),
          itemCount: controller.reviews.length,
          itemBuilder: (context, index) {
            final review = controller.reviews[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 10),
              padding: const EdgeInsets.all(10.0),
              decoration: BoxDecoration(
                color: AppConstants.primaryWhite,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: AppConstants.primaryBlack.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Row
                  Row(
                    children: [
                      // User Name
                      Text(
                        review.displayUserName,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                      const Spacer(),
                      // Rating Badge
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.star, size: 10, color: Colors.amber),
                            const SizedBox(width: 2),
                            Text(
                              review.displayRating,
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: AppConstants.primaryBlack,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  // Review Text
                  Text(
                    review.reviewText,
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                      height: 1.3,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 48, color: AppConstants.mediumGrey),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            message,
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: AppConstants.mediumGrey,
            ),
          ),
        ],
      ),
    );
  }

  // Modal Bottom Sheets
  void _showServicesBottomSheet(
    BuildContext context,
    SalonDetailController controller,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildServicesBottomSheet(context, controller),
    );
  }

  void _showBarbersBottomSheet(
    BuildContext context,
    SalonDetailController controller,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildBarbersBottomSheet(context, controller),
    );
  }

  Widget _buildServicesBottomSheet(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppConstants.mediumGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                Text(
                  'All Services',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: AppConstants.primaryBlack),
                ),
              ],
            ),
          ),

          // Services List
          Expanded(
            child: Obx(
              () => ListView.builder(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                ),
                itemCount: controller.services.length,
                itemBuilder: (context, index) {
                  final service = controller.services[index];
                  return Container(
                    margin: const EdgeInsets.only(
                      bottom: AppConstants.paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryWhite,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusMedium,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryBlack.withValues(
                            alpha: 0.1,
                          ),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(
                        AppConstants.paddingMedium,
                      ),
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: AppConstants.lightGrey,
                          borderRadius: BorderRadius.circular(
                            AppConstants.radiusSmall,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                            AppConstants.radiusSmall,
                          ),
                          child: service.hasImage
                              ? CachedNetworkImage(
                                  imageUrl: service.serviceimage,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: AppConstants.primaryBlack,
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Icon(
                                    Icons.design_services,
                                    color: AppConstants.mediumGrey,
                                  ),
                                )
                              : Icon(
                                  Icons.design_services,
                                  color: AppConstants.mediumGrey,
                                ),
                        ),
                      ),
                      title: Text(
                        service.displayName,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                            service.displayCategory,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: AppConstants.mediumGrey,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            service.displayDuration,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: AppConstants.primaryBlack,
                            ),
                          ),
                        ],
                      ),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            service.displayPrice,
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: AppConstants.primaryBlack,
                            ),
                          ),
                          const SizedBox(height: 4),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              controller.bookAppointment();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppConstants.primaryBlack,
                              foregroundColor: AppConstants.primaryWhite,
                              minimumSize: const Size(60, 28),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  AppConstants.radiusSmall,
                                ),
                              ),
                            ),
                            child: Text(
                              'Book',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarbersBottomSheet(
    BuildContext context,
    SalonDetailController controller,
  ) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppConstants.mediumGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                Text(
                  'All Barbers',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: AppConstants.primaryBlack),
                ),
              ],
            ),
          ),

          // Barbers List
          Expanded(
            child: Obx(
              () => ListView.builder(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                ),
                itemCount: controller.barbers.length,
                itemBuilder: (context, index) {
                  final barber = controller.barbers[index];
                  return Container(
                    margin: const EdgeInsets.only(
                      bottom: AppConstants.paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryWhite,
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusMedium,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryBlack.withValues(
                            alpha: 0.1,
                          ),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(
                        AppConstants.paddingMedium,
                      ),
                      leading: CircleAvatar(
                        radius: 25,
                        backgroundColor: AppConstants.lightGrey,
                        backgroundImage: barber.hasProfileImage
                            ? CachedNetworkImageProvider(barber.profileimage)
                            : null,
                        child: !barber.hasProfileImage
                            ? Icon(Icons.person, color: AppConstants.mediumGrey)
                            : null,
                      ),
                      title: Text(
                        barber.displayName,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.primaryBlack,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                            barber.displayBio,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: AppConstants.mediumGrey,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.star, size: 14, color: Colors.amber),
                              const SizedBox(width: 4),
                              Text(
                                barber.displayRating,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.primaryBlack,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                barber.displayReviews,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: AppConstants.mediumGrey,
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: barber.available
                                      ? Colors.green.withValues(alpha: 0.1)
                                      : Colors.red.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  barber.availabilityStatus,
                                  style: GoogleFonts.poppins(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: barber.available
                                        ? Colors.green
                                        : Colors.red,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      trailing: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          controller.bookWithBarber(barber.id);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryBlack,
                          foregroundColor: AppConstants.primaryWhite,
                          minimumSize: const Size(80, 32),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppConstants.radiusSmall,
                            ),
                          ),
                        ),
                        child: Text(
                          'Book',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
