class ApiEndpoints {
  // Base URL
  // static const String baseUrl = 'http://localhost:4000';
  // static const String baseUrl = 'http://api.barbrandco.com:4000';
  static const String baseUrl = "http://************";

  // Auth Endpoints
  static const String emailRegister = '/auth/email/register';
  static const String phoneRegister = '/auth/phone/register';
  static const String login = '/auth/login';
  static const String resendOtp = '/auth/resend-otp';
  static const String verifyOtp = '/auth/verify-otp';

  // Profile Endpoints
  static const String ownerProfile = '/auth/profile/owner';
  static const String customerProfile = '/auth/profile/customer';

  // Dashboard Endpoints
  static const String userDashboard = '/user/dashboard';
  static const String ownerDashboard = '/owner/dashboard';
  static const String ownerWeeklyData = '/owner/data1';
  static const String ownerMonthlyData = '/owner/data2';

  // Salon Detail Endpoints
  static const String salonDetail = '/saloon';

  // Booking Endpoints
  static const String availableSlots = '/appointments/available-slots';

  // Complete URLs
  static String get emailRegisterUrl => baseUrl + emailRegister;
  static String get phoneRegisterUrl => baseUrl + phoneRegister;
  static String get loginUrl => baseUrl + login;
  static String get resendOtpUrl => baseUrl + resendOtp;
  static String get verifyOtpUrl => baseUrl + verifyOtp;
  static String get ownerProfileUrl => baseUrl + ownerProfile;
  static String get customerProfileUrl => baseUrl + customerProfile;
  static String get userDashboardUrl => baseUrl + userDashboard;
  static String get ownerDashboardUrl => baseUrl + ownerDashboard;
  static String get ownerWeeklyDataUrl => baseUrl + ownerWeeklyData;
  static String get ownerMonthlyDataUrl => baseUrl + ownerMonthlyData;
  static String get availableSlotsUrl => baseUrl + availableSlots;

  // Salon Detail URL
  static String salonDetailUrl(String salonId) =>
      '$baseUrl$salonDetail/$salonId';

  // Dynamic URLs
  static String ownerProfileByIdUrl(String saloonId) =>
      '$baseUrl$ownerProfile/$saloonId';
  static String customerProfileByIdUrl(String customerId) =>
      '$baseUrl$customerProfile/$customerId';

  static String get addBarberService => baseUrl + '/owner/service';
  static String get getBarberServices => baseUrl + '/owner/service';
  static String get deleteBarberService => baseUrl + '/owner/service/';

  // Barber Endpoints
  static String get addBarber => baseUrl + '/owner/barber';
  static String get getBarbers =>
      baseUrl + '/owner/barbers'; // Fixed: added 's'
  static String get updateBarber => baseUrl + '/owner/barber/';
  static String get deleteBarber => baseUrl + '/owner/barber/';

  // Owner Profile
  static String get ownerOrUserProfile => baseUrl + '/owner/profile';

  // User Appointments
  static String get userAppointments => baseUrl + '/user/appointments';

  // Appointment Pending, Approved, Ongoing Get API Endpoints
  static String get appointmentPending =>
      baseUrl + '/appointments/salon/others';

  // Decision On Appointment
  static String get appointmentDecision => baseUrl + '/appointments/decision';
  static String get startService => baseUrl + '/appointments/start-service';
  static String get completeService =>
      baseUrl + '/appointments/complete-service';

  // User appointment endpoints
  static String get getUserAppointments => baseUrl + '/user/appointments';
  static String get submitSalonReview => baseUrl + '/saloon/review';
  static String get cancelAppointment => baseUrl + '/user/appointment/cancel';
  static String get rescheduleAppointment =>
      baseUrl + '/appointments/reschedule';
  static String get getAppointmentDetails => baseUrl + '/user/appointment';

  // Search endpoints
  static String get searchSalons => baseUrl + '/saloon/search';

  // User profile endpoints
  static String get userProfile => baseUrl + '/user/profile';
  static String get userProfileImage => baseUrl + '/user/profile/image';

  // Owner appointment management endpoints
  static String get getSalonAppointments =>
      baseUrl + '/appointments/salon/others';
  static String get getCompletedAppointments =>
      baseUrl + '/appointments/salon/completed';

  // Home salon endpoints
  static String get addHomeSalon => baseUrl + '/saloon/home';

  // Notification endpoints
  static String get notifications => '$baseUrl/notifications';
  static String get notificationsUnreadCount =>
      '$baseUrl/notifications/unread-count';
  static String get notificationsReadAll => '$baseUrl/notifications/read-all';

  // Dynamic notification endpoints
  static String notificationMarkAsRead(String notificationId) =>
      '$baseUrl/notifications/$notificationId/read';
}
