import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'Saloon Booking';
  static const String appVersion = '1.0.0';

  // Colors
  static const Color primaryBlack = Color(0xFF000000);
  static const Color primaryWhite = Color(0xFFFFFFFF);
  static const Color primaryGrey = Color(0xFF9E9E9E);
  static const Color lightGrey = Color(0xFFF5F5F5);
  static const Color darkGrey = Color(0xFF424242);
  static const Color mediumGrey = Color(0xFF757575);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryBlack, darkGrey],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient lightGradient = LinearGradient(
    colors: [lightGrey, primaryWhite],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;

  // Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  // OTP Timer
  static const int otpResendTimer = 30;

  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email';
  static const String passwordRequired = 'Password is required';
  static const String passwordMinLength =
      'Password must be at least 6 characters';
  static const String firstNameRequired = 'First name is required';
  static const String lastNameRequired = 'Last name is required';
  static const String localityRequired = 'Locality is required';
  static const String cityRequired = 'City is required';
  static const String stateRequired = 'State is required';
  static const String pincodeRequired = 'Pincode is required';
  static const String pincodeInvalid = 'Please enter a valid 6-digit pincode';
  static const String phoneRequired = 'Phone number is required';
  static const String phoneInvalid =
      'Please enter a valid 10-digit phone number';
  static const String salonNameRequired = 'Salon name is required';
  static const String salonRegIdRequired = 'Salon registration ID is required';
  static const String salonDescRequired = 'Salon description is required';
  static const String businessEmailRequired = 'Business email is required';
  static const String otpRequired = 'OTP is required';
  static const String otpInvalid = 'Please enter a valid 6-digit OTP';
}
