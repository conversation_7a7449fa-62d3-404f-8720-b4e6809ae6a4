import 'dart:developer';

/// Utility class for determining salon operating status
class SalonStatusUtils {
  /// Determine if salon is currently open based on operating hours and off days
  static SalonStatus getSalonStatus({
    String? startTime,
    String? endTime,
    String? offDays,
  }) {
    try {
      final now = DateTime.now();
      final currentDay = _getCurrentDayName();

      // Check if salon is closed today
      if (offDays?.isNotEmpty == true && _isClosedToday(offDays!, currentDay)) {
        return SalonStatus.closedToday;
      }

      // Check operating hours if available
      if (startTime?.isNotEmpty == true && endTime?.isNotEmpty == true) {
        final isWithinHours = _isWithinOperatingHours(
          startTime!,
          endTime!,
          now,
        );
        return isWithinHours ? SalonStatus.open : SalonStatus.closedHours;
      }

      // Default to open if no operating hours specified
      return SalonStatus.open;
    } catch (e) {
      log('SalonStatusUtils: Error determining status: $e');
      return SalonStatus.unknown;
    }
  }

  /// Get display text for salon status
  static String getStatusText(SalonStatus status) {
    switch (status) {
      case SalonStatus.open:
        return 'Open';
      case SalonStatus.closedToday:
        return 'Closed Today';
      case SalonStatus.closedHours:
        return 'Closed';
      case SalonStatus.unknown:
        return 'Status Unknown';
    }
  }

  /// Get status color for UI display
  static SalonStatusColor getStatusColor(SalonStatus status) {
    switch (status) {
      case SalonStatus.open:
        return SalonStatusColor.green;
      case SalonStatus.closedToday:
      case SalonStatus.closedHours:
        return SalonStatusColor.red;
      case SalonStatus.unknown:
        return SalonStatusColor.grey;
    }
  }

  /// Check if salon is closed on current day
  static bool _isClosedToday(String offDays, String currentDay) {
    final offDaysList = offDays
        .toLowerCase()
        .split(',')
        .map((day) => day.trim())
        .toList();
    return offDaysList.contains(currentDay.toLowerCase());
  }

  /// Check if current time is within operating hours
  static bool _isWithinOperatingHours(
    String startTime,
    String endTime,
    DateTime now,
  ) {
    try {
      final start = _parseTime(startTime);
      final end = _parseTime(endTime);

      if (start == null || end == null) {
        return true; // Default to open if can't parse
      }

      final currentTime = TimeOfDay.fromDateTime(now);
      final currentMinutes = currentTime.hour * 60 + currentTime.minute;
      final startMinutes = start.hour * 60 + start.minute;
      final endMinutes = end.hour * 60 + end.minute;

      // Handle overnight hours (e.g., 10 PM - 2 AM)
      if (endMinutes < startMinutes) {
        return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
      }

      // Normal hours (e.g., 9 AM - 8 PM)
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } catch (e) {
      log('SalonStatusUtils: Error parsing operating hours: $e');
      return true; // Default to open if error
    }
  }

  /// Parse time string to TimeOfDay
  static TimeOfDay? _parseTime(String timeString) {
    try {
      // Handle different time formats
      timeString = timeString.trim();

      // Handle 12-hour format with AM/PM
      bool isPM = timeString.toLowerCase().contains('pm');
      bool isAM = timeString.toLowerCase().contains('am');

      // Remove AM/PM suffixes
      timeString = timeString.replaceAll(RegExp(r'\s*(AM|PM|am|pm)\s*'), '');

      // Split by colon
      final parts = timeString.split(':');
      if (parts.length >= 2) {
        int? hour = int.tryParse(parts[0]);
        final minute = int.tryParse(parts[1]);

        if (hour != null && minute != null) {
          // Convert 12-hour to 24-hour format
          if (isPM && hour != 12) {
            hour += 12;
          } else if (isAM && hour == 12) {
            hour = 0;
          }

          // Validate hour and minute ranges
          if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
            return TimeOfDay(hour: hour, minute: minute);
          }
        }
      }

      // Try parsing as hour only
      int? hour = int.tryParse(timeString);
      if (hour != null) {
        // Convert 12-hour to 24-hour format for hour-only
        if (isPM && hour != 12) {
          hour += 12;
        } else if (isAM && hour == 12) {
          hour = 0;
        }

        if (hour >= 0 && hour <= 23) {
          return TimeOfDay(hour: hour, minute: 0);
        }
      }

      return null;
    } catch (e) {
      log('SalonStatusUtils: Error parsing time "$timeString": $e');
      return null;
    }
  }

  /// Get current day name
  static String _getCurrentDayName() {
    final weekdays = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];

    final now = DateTime.now();
    return weekdays[now.weekday - 1];
  }
}

/// Salon operating status enum
enum SalonStatus { open, closedToday, closedHours, unknown }

/// Status color enum for UI
enum SalonStatusColor { green, red, grey }

/// TimeOfDay class for time handling (if not available from Flutter)
class TimeOfDay {
  final int hour;
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  factory TimeOfDay.fromDateTime(DateTime dateTime) {
    return TimeOfDay(hour: dateTime.hour, minute: dateTime.minute);
  }

  @override
  String toString() =>
      '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
}
