import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/owner_profile_model.dart';

class ProfileImageGallery extends StatelessWidget {
  final List<SaloonImage> networkImages;
  final List<File> localImages;
  final VoidCallback onAddImages;
  final Function(int) onRemoveImage;
  final Function(int)? onDeleteNetworkImage;
  final bool isUploading;

  const ProfileImageGallery({
    super.key,
    required this.networkImages,
    required this.localImages,
    required this.onAddImages,
    required this.onRemoveImage,
    this.onDeleteNetworkImage,
    this.isUploading = false,
  });

  @override
  Widget build(BuildContext context) {
    final totalImages = networkImages.length + localImages.length;

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Salon Gallery',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryBlack,
                ),
              ),
              Text(
                '$totalImages/5',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          if (totalImages == 0) _buildEmptyState() else _buildImageGrid(),

          if (totalImages < 5) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            _buildAddButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(
          color: AppConstants.primaryGrey.withValues(alpha: 0.3),
          style: BorderStyle.solid,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_library_outlined,
              size: 32,
              color: AppConstants.mediumGrey,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Add salon photos',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppConstants.mediumGrey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid() {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: networkImages.length + localImages.length,
        itemBuilder: (context, index) {
          if (index < networkImages.length) {
            return _buildNetworkImageItem(networkImages[index], index);
          } else {
            final localIndex = index - networkImages.length;
            return _buildLocalImageItem(localImages[localIndex], index);
          }
        },
      ),
    );
  }

  Widget _buildNetworkImageItem(SaloonImage salonImage, int index) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            child: CachedNetworkImage(
              imageUrl: salonImage.imageUrl,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 100,
                height: 100,
                color: AppConstants.lightGrey,
                child: const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppConstants.mediumGrey,
                    ),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                width: 100,
                height: 100,
                color: AppConstants.lightGrey,
                child: const Icon(
                  Icons.error_outline,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () {
                if (onDeleteNetworkImage != null) {
                  onDeleteNetworkImage!(index);
                } else {
                  onRemoveImage(index);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.red.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: AppConstants.primaryWhite,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocalImageItem(File imageFile, int index) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            child: Image.file(
              imageFile,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => onRemoveImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: AppConstants.primaryWhite,
                ),
              ),
            ),
          ),
          if (isUploading)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppConstants.primaryWhite,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAddButton() {
    return GestureDetector(
      onTap: isUploading ? null : onAddImages,
      child: AnimatedContainer(
        duration: AppConstants.animationMedium,
        padding: const EdgeInsets.symmetric(
          vertical: AppConstants.paddingMedium,
          horizontal: AppConstants.paddingLarge,
        ),
        decoration: BoxDecoration(
          gradient: isUploading ? null : AppConstants.primaryGradient,
          color: isUploading ? AppConstants.lightGrey : null,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isUploading)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppConstants.mediumGrey,
                  ),
                ),
              )
            else
              const Icon(
                Icons.add_photo_alternate_outlined,
                color: AppConstants.primaryWhite,
                size: 20,
              ),
            const SizedBox(width: AppConstants.paddingSmall),
            Text(
              isUploading ? 'Uploading...' : 'Add Photos',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isUploading
                    ? AppConstants.mediumGrey
                    : AppConstants.primaryWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
