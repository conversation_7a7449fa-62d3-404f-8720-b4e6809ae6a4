import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../models/barber_models.dart';

class EnhancedBarberListTile extends StatelessWidget {
  final BarberModel barber;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final Function(bool)? onAvailabilityToggle;
  final bool isLoading;

  const EnhancedBarberListTile({
    super.key,
    required this.barber,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onAvailabilityToggle,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(barber.id ?? ''),
      background: _buildSwipeBackground(
        color: Colors.blue,
        icon: Icons.edit,
        alignment: Alignment.centerLeft,
        text: 'Edit',
      ),
      secondaryBackground: _buildSwipeBackground(
        color: Colors.red,
        icon: Icons.delete,
        alignment: Alignment.centerRight,
        text: 'Delete',
      ),
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd) {
          onEdit?.call();
        } else if (direction == DismissDirection.endToStart) {
          onDelete?.call();
        }
      },
      confirmDismiss: (direction) async {
        // Let parent handle confirmation for delete
        return true; // Allow edit swipe
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isLoading ? null : onTap,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  // Profile Image
                  _buildProfileImage(),

                  const SizedBox(width: AppConstants.paddingMedium),

                  // Barber Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Name
                        Text(
                          barber.name,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppConstants.primaryBlack,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        // const SizedBox(height: 4),

                        // Bio/Specialization
                        Text(
                          barber.specialization,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: AppConstants.mediumGrey,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 8),

                        // Services Count
                        if (barber.services != null &&
                            barber.services!.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppConstants.lightGrey,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${barber.services!.length} services',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: AppConstants.primaryBlack,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(width: AppConstants.paddingMedium),

                  // Availability Toggle
                  Column(
                    children: [
                      Switch(
                        value: barber.available,
                        onChanged: isLoading
                            ? null
                            : (value) {
                                log(
                                  'Toggle changed for barber ${barber.name}: $value',
                                );
                                log(
                                  'Original barber available: ${barber.available}',
                                );
                                log('New availability: $value');
                                log('Barber ID: ${barber.id}');
                                if (onAvailabilityToggle != null) {
                                  onAvailabilityToggle!(value);
                                }
                              },
                        activeColor: Colors.green,
                        inactiveThumbColor: AppConstants.mediumGrey,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      Text(
                        barber.availableTime,
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color: barber.available
                              ? Colors.green
                              : AppConstants.mediumGrey,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: AppConstants.lightGrey, width: 2),
      ),
      child: ClipOval(
        child: barber.imageUrl.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: barber.imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppConstants.lightGrey,
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                errorWidget: (context, url, error) => _buildDefaultAvatar(),
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: AppConstants.lightGrey,
      child: Icon(Icons.person, size: 30, color: AppConstants.mediumGrey),
    );
  }

  Widget _buildSwipeBackground({
    required Color color,
    required IconData icon,
    required Alignment alignment,
    required String text,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Align(
        alignment: alignment,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: Colors.white, size: 24),
              const SizedBox(height: 4),
              Text(
                text,
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
