import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../models/appointment_management_models.dart';
import '../controllers/appointment_management_controller.dart';

/// Pending Appointment Card Widget
class PendingAppointmentCard extends StatelessWidget {
  final OwnerAppointment appointment;
  final AppointmentManagementController controller;

  const PendingAppointmentCard({
    super.key,
    required this.appointment,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info
          Row(
            children: [
              // User Avatar
              CircleAvatar(
                radius: 25,
                backgroundColor: AppConstants.lightGrey,
                child: Icon(
                  Icons.person,
                  color: AppConstants.mediumGrey,
                  size: 30,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              // User details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appointment.userName,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      appointment.barberName,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                  ],
                ),
              ),
              // Status badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Pending',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.orange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Appointment details
          AppointmentDetailsWidget(appointment: appointment),

          const SizedBox(height: AppConstants.paddingMedium),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: Obx(() {
                  final isLoading = controller.isAppointmentLoading(
                    appointment.id,
                  );
                  return ElevatedButton(
                    onPressed: isLoading
                        ? null
                        : () => controller.rejectAppointment(appointment.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade50,
                      foregroundColor: Colors.red,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.red.shade200),
                      ),
                    ),
                    child: isLoading
                        ? const SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            'Deny',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  );
                }),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: Obx(() {
                  final isLoading = controller.isAppointmentLoading(
                    appointment.id,
                  );
                  return ElevatedButton(
                    onPressed: isLoading
                        ? null
                        : () => controller.acceptAppointment(appointment.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: AppConstants.primaryWhite,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: isLoading
                        ? const SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            'Accept',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  );
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Approved Appointment Card Widget
class ApprovedAppointmentCard extends StatefulWidget {
  final OwnerAppointment appointment;
  final AppointmentManagementController controller;

  const ApprovedAppointmentCard({
    super.key,
    required this.appointment,
    required this.controller,
  });

  @override
  State<ApprovedAppointmentCard> createState() =>
      _ApprovedAppointmentCardState();
}

class _ApprovedAppointmentCardState extends State<ApprovedAppointmentCard> {
  final TextEditingController _otpController = TextEditingController();

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppConstants.lightGrey,
                child: Icon(
                  Icons.person,
                  color: AppConstants.mediumGrey,
                  size: 30,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.appointment.userName,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      widget.appointment.barberName,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Approved',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          AppointmentDetailsWidget(appointment: widget.appointment),

          const SizedBox(height: AppConstants.paddingMedium),

          // OTP Input Section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Enter Customer OTP to Start Service',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryBlack,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _otpController,
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                      decoration: InputDecoration(
                        hintText: 'Enter 4-digit OTP',
                        hintStyle: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppConstants.mediumGrey,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: AppConstants.mediumGrey,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        counterText: '',
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Obx(() {
                    final isLoading = widget.controller.isAppointmentLoading(
                      widget.appointment.id,
                    );
                    return ElevatedButton(
                      onPressed: isLoading
                          ? null
                          : () {
                              // Get OTP from text field and call start service
                              final otp = _otpController.text.trim();
                              if (otp.length != 4) {
                                Get.snackbar(
                                  'Invalid OTP',
                                  'Please enter a valid 4-digit OTP',
                                  backgroundColor: Colors.red,
                                  colorText: Colors.white,
                                );
                                return;
                              }
                              widget.controller.startService(
                                widget.appointment.id,
                                otp,
                              );
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.primaryBlack,
                        foregroundColor: AppConstants.primaryWhite,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: isLoading
                          ? const SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Text(
                              'Start',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    );
                  }),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Ongoing Appointment Card Widget
class OngoingAppointmentCard extends StatelessWidget {
  final OwnerAppointment appointment;
  final AppointmentManagementController controller;

  const OngoingAppointmentCard({
    super.key,
    required this.appointment,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppConstants.lightGrey,
                child: Icon(
                  Icons.person,
                  color: AppConstants.mediumGrey,
                  size: 30,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appointment.userName,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      appointment.barberName,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Ongoing',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          AppointmentDetailsWidget(appointment: appointment),

          const SizedBox(height: AppConstants.paddingMedium),

          // Complete Service button
          SizedBox(
            width: double.infinity,
            child: Obx(() {
              final isLoading = controller.isAppointmentLoading(appointment.id);
              return ElevatedButton(
                onPressed: isLoading
                    ? null
                    : () => controller.completeService(appointment.id),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryBlack,
                  foregroundColor: AppConstants.primaryWhite,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: isLoading
                    ? const SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        'Complete Service',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

/// Completed Appointment Card Widget
class CompletedAppointmentCard extends StatelessWidget {
  final OwnerAppointment appointment;

  const CompletedAppointmentCard({super.key, required this.appointment});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppConstants.lightGrey,
                child: Icon(
                  Icons.person,
                  color: AppConstants.mediumGrey,
                  size: 30,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appointment.userName,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      appointment.barberName,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.mediumGrey,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Completed',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          AppointmentDetailsWidget(appointment: appointment),
        ],
      ),
    );
  }
}

/// Appointment Details Widget
class AppointmentDetailsWidget extends StatelessWidget {
  final OwnerAppointment appointment;

  const AppointmentDetailsWidget({super.key, required this.appointment});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Service and amount row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Service',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    appointment.serviceNames,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryBlack,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'Amount',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.mediumGrey,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '₹${appointment.amount.toStringAsFixed(0)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),

        // Date, time, and duration row with equal spacing
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    appointment.formattedDate,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Time',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    appointment.formattedTimeRange,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Duration',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    appointment.formattedDuration,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),

        // Payment type
        Row(
          children: [
            Text(
              'Payment: ',
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
              ),
            ),
            Text(
              _getPaymentTypeText(appointment.bookingType),
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: _getPaymentTypeColor(appointment.bookingType),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Get payment type display text based on booking type
  String _getPaymentTypeText(String bookingType) {
    switch (bookingType.toLowerCase()) {
      case 'full_payment':
        return 'Paid Online';
      case 'cash_after_service':
        return 'Pay at Salon';
      case 'partial_payment':
        return 'Slot Booking Amount';
      default:
        return bookingType;
    }
  }

  /// Get payment type color based on booking type
  Color _getPaymentTypeColor(String bookingType) {
    switch (bookingType.toLowerCase()) {
      case 'full_payment':
        return Colors.green;
      case 'cash_after_service':
        return Colors.orange;
      case 'partial_payment':
        return AppConstants.primaryBlack;
      default:
        return AppConstants.primaryBlack;
    }
  }
}
