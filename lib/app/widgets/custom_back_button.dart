import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_constants.dart';

/// Custom back button widget with consistent styling
class CustomBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final bool showShadow;

  const CustomBackButton({
    super.key,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size,
    this.padding,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          onTap: onPressed ?? () => Get.back(),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppConstants.paddingSmall),
            child: Icon(
              Icons.arrow_back_ios_new,
              color: iconColor ?? AppConstants.primaryBlack,
              size: size ?? 20,
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom back button for transparent backgrounds (like over images)
class CustomBackButtonTransparent extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  final EdgeInsetsGeometry? padding;

  const CustomBackButtonTransparent({
    super.key,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppConstants.primaryBlack.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          onTap: onPressed ?? () => Get.back(),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppConstants.paddingSmall),
            child: Icon(
              Icons.arrow_back_ios_new,
              color: iconColor ?? AppConstants.primaryWhite,
              size: size ?? 20,
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom icon button for app bars
class CustomIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final bool showShadow;
  final String? tooltip;

  const CustomIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size,
    this.padding,
    this.showShadow = true,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    final button = Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          onTap: onPressed,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppConstants.paddingSmall),
            child: Icon(
              icon,
              color: iconColor ?? AppConstants.primaryBlack,
              size: size ?? 20,
            ),
          ),
        ),
      ),
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }
}

/// Custom icon button for transparent backgrounds
class CustomIconButtonTransparent extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final String? tooltip;

  const CustomIconButtonTransparent({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size,
    this.padding,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    final button = Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppConstants.primaryBlack.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          onTap: onPressed,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppConstants.paddingSmall),
            child: Icon(
              icon,
              color: iconColor ?? AppConstants.primaryWhite,
              size: size ?? 20,
            ),
          ),
        ),
      ),
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }
}
