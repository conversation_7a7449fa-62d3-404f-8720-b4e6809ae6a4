import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_constants.dart';
import '../controllers/user/home_controller.dart';
import '../controllers/notification_controller.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final bool showNotification;
  final VoidCallback? onNotificationTap;
  final List<Widget>? actions;

  const CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = false,
    this.showNotification = true,
    this.onNotificationTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton
          ? IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppConstants.primaryBlack,
              ),
              onPressed: () => Get.back(),
            )
          : null,
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      actions:
          actions ??
          (showNotification
              ? [
                  IconButton(
                    onPressed:
                        onNotificationTap ??
                        () {
                          Get.toNamed('/notifications');
                        },
                    icon: Stack(
                      children: [
                        Icon(
                          Icons.notifications_outlined,
                          size: 28,
                          color: AppConstants.primaryBlack,
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                ]
              : null),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback? onNotificationTap;

  const HomeAppBar({super.key, this.onNotificationTap});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();
    final NotificationController notificationController = Get.put(
      NotificationController(),
    );

    return Container(
      color: AppConstants.primaryWhite,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
        left: AppConstants.paddingSmall,
        right: AppConstants.paddingSmall,
        bottom: AppConstants.paddingMedium,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Obx(
                  () => Padding(
                    padding: const EdgeInsets.only(left: 5),
                    child: Text(
                      'Hello, ${controller.userName.value}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryBlack,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.paddingSmall),

                Obx(
                  () => GestureDetector(
                    onTap: () async {
                      final result = await Get.toNamed('/location-selection');
                      if (result != null && result is Map<String, dynamic>) {
                        final latitude = result['latitude'] as double?;
                        final longitude = result['longitude'] as double?;
                        final address = result['address'] as String?;

                        if (latitude != null &&
                            longitude != null &&
                            address != null) {
                          controller.setSelectedLocation(
                            latitude,
                            longitude,
                            address,
                          );
                        }
                      }
                    },
                    child: Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: AppConstants.mediumGrey,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            controller.userAddress.value.isEmpty
                                ? 'Select Location'
                                : controller.userAddress.value,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: AppConstants.mediumGrey),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Notification Icon
          Obx(
            () => IconButton(
              padding: EdgeInsets.only(right: 0),
              onPressed:
                  onNotificationTap ??
                  () {
                    Get.toNamed('/notifications');
                  },
              icon: Stack(
                children: [
                  Icon(
                    Icons.notifications_outlined,
                    size: 28,
                    color: AppConstants.primaryBlack,
                  ),
                  if (notificationController.unreadCount.value > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(80);
}
