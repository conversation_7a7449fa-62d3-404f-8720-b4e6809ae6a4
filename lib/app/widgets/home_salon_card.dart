import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';

class HomeSalonCard extends StatelessWidget {
  final String salonName;
  final String salonAddress;
  final String salonImage;
  final String salonProfileImage;
  final VoidCallback? onRebook;
  final VoidCallback? onTap;

  const HomeSalonCard({
    super.key,
    required this.salonName,
    required this.salonAddress,
    required this.salonImage,
    required this.salonProfileImage,
    this.onRebook,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Salon Image
            Container(
              height: 180,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(AppConstants.radiusMedium),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(AppConstants.radiusMedium),
                ),
                child: salonImage.isNotEmpty
                    ? Image.network(
                        salonImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage();
                        },
                      )
                    : _buildPlaceholderImage(),
              ),
            ),

            // Salon Details
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                children: [
                  // Top Row: Profile Image and Salon Info
                  Row(
                    children: [
                      // Salon Profile Image
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: AppConstants.lightGrey,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: salonProfileImage.isNotEmpty
                              ? Image.network(
                                  salonProfileImage,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return _buildProfilePlaceholder();
                                  },
                                )
                              : _buildProfilePlaceholder(),
                        ),
                      ),

                      const SizedBox(width: 12.0),

                      // Salon Info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              salonName,
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppConstants.primaryBlack,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            const SizedBox(height: 4),

                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  size: 12,
                                  color: AppConstants.mediumGrey,
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    salonAddress,
                                    style: GoogleFonts.poppins(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w400,
                                      color: AppConstants.mediumGrey,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12.0),

                  // Bottom Row: Rebook Button
                  SizedBox(
                    width: double.infinity,
                    height: 44,
                    child: ElevatedButton(
                      onPressed: onRebook ?? () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.primaryBlack,
                        foregroundColor: AppConstants.primaryWhite,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        elevation: 2,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 12.0,
                        ),
                      ),
                      child: Text(
                        'Rebook',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.primaryWhite,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: AppConstants.lightGrey,
      child: Center(
        child: Icon(Icons.store, size: 60, color: AppConstants.mediumGrey),
      ),
    );
  }

  Widget _buildProfilePlaceholder() {
    return Container(
      color: AppConstants.lightGrey,
      child: Icon(Icons.person, size: 24, color: AppConstants.mediumGrey),
    );
  }
}
