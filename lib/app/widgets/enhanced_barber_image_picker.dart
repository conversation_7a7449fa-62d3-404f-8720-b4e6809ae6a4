import 'dart:io';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';

class EnhancedBarberImagePicker extends StatelessWidget {
  final Function(File?) onImagePicked;
  final File? currentImage;
  final String? imageUrl;
  final double size;
  final bool isUploading;
  final double uploadProgress;

  const EnhancedBarberImagePicker({
    super.key,
    required this.onImagePicked,
    this.currentImage,
    this.imageUrl,
    this.size = 120,
    this.isUploading = false,
    this.uploadProgress = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isUploading ? null : _showImagePickerOptions,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size / 2),
          border: Border.all(
            color: isUploading
                ? AppConstants.primaryBlack.withValues(alpha: 0.3)
                : AppConstants.primaryBlack,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size / 2),
          child: Stack(
            children: [
              // Background/Image Layer
              _buildImageLayer(),

              // Upload Progress Overlay
              if (isUploading) _buildUploadOverlay(),

              // Add/Edit Icon
              if (!isUploading) _buildActionIcon(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageLayer() {
    // Priority: currentImage > imageUrl > placeholder
    if (currentImage != null) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: FileImage(currentImage!),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else if (imageUrl != null &&
        imageUrl!.isNotEmpty &&
        _isValidImageUrl(imageUrl!)) {
      return CachedNetworkImage(
        imageUrl: imageUrl!,
        width: size,
        height: size,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildLoadingPlaceholder(),
        errorWidget: (context, url, error) => _buildPlaceholder(),
      );
    } else {
      return _buildPlaceholder();
    }
  }

  Widget _buildUploadOverlay() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppConstants.primaryBlack.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Circular Progress Indicator
          SizedBox(
            width: size * 0.4,
            height: size * 0.4,
            child: CircularProgressIndicator(
              value: uploadProgress,
              strokeWidth: 3,
              backgroundColor: AppConstants.primaryWhite.withValues(alpha: 0.3),
              valueColor: const AlwaysStoppedAnimation<Color>(
                AppConstants.primaryWhite,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Upload Text
          Text(
            'Uploading...',
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: AppConstants.primaryWhite,
            ),
          ),

          // Progress Percentage
          Text(
            '${(uploadProgress * 100).toInt()}%',
            style: GoogleFonts.poppins(
              fontSize: 8,
              fontWeight: FontWeight.w400,
              color: AppConstants.primaryWhite.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionIcon() {
    final hasImage =
        currentImage != null ||
        (imageUrl != null &&
            imageUrl!.isNotEmpty &&
            _isValidImageUrl(imageUrl!));

    return Positioned(
      bottom: 0,
      right: 0,
      child: Container(
        width: size * 0.25,
        height: size * 0.25,
        decoration: BoxDecoration(
          color: AppConstants.primaryBlack,
          borderRadius: BorderRadius.circular(size * 0.125),
          border: Border.all(color: AppConstants.primaryWhite, width: 2),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          hasImage ? Icons.edit : Icons.add_a_photo,
          color: AppConstants.primaryWhite,
          size: size * 0.12,
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person, size: size * 0.4, color: AppConstants.mediumGrey),
          const SizedBox(height: 4),
          Text(
            'Add Photo',
            style: GoogleFonts.poppins(
              fontSize: size * 0.08,
              fontWeight: FontWeight.w500,
              color: AppConstants.mediumGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppConstants.mediumGrey),
          ),
        ),
      ),
    );
  }

  bool _isValidImageUrl(String url) {
    if (url.isEmpty) return false;
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  void _showImagePickerOptions() {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.radiusLarge),
            topRight: Radius.circular(AppConstants.radiusLarge),
          ),
        ),
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            Text(
              'Select Barber Photo',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Camera Option
            _buildOption(
              icon: Icons.camera_alt,
              title: 'Take Photo',
              subtitle: 'Use camera to take a new photo',
              onTap: () {
                Get.back();
                _pickImageDirect(ImageSource.camera);
              },
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Gallery Option
            _buildOption(
              icon: Icons.photo_library,
              title: 'Choose from Gallery',
              subtitle: 'Select from your photo library',
              onTap: () {
                Get.back();
                _pickImageDirect(ImageSource.gallery);
              },
            ),

            if (currentImage != null ||
                (imageUrl != null && imageUrl!.isNotEmpty)) ...[
              const SizedBox(height: AppConstants.paddingMedium),

              // Remove Option
              _buildOption(
                icon: Icons.delete,
                title: 'Remove Photo',
                subtitle: 'Remove current barber photo',
                iconColor: Colors.red,
                onTap: () {
                  Get.back();
                  onImagePicked(null);
                },
              ),
            ],

            const SizedBox(height: AppConstants.paddingLarge),

            // Cancel Button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Get.back(),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.mediumGrey,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          border: Border.all(color: AppConstants.lightGrey),
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (iconColor ?? AppConstants.primaryBlack).withValues(
                  alpha: 0.1,
                ),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                icon,
                color: iconColor ?? AppConstants.primaryBlack,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppConstants.mediumGrey,
            ),
          ],
        ),
      ),
    );
  }

  /// Direct image picker without complex permission handling (like AddServiceController)
  Future<void> _pickImageDirect(ImageSource source) async {
    try {
      log(
        'EnhancedBarberImagePicker: Starting direct image pick from ${source.name}',
      );

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 80,
      );

      if (image != null) {
        final imageFile = File(image.path);
        log(
          'EnhancedBarberImagePicker: Image picked successfully: ${imageFile.path}',
        );
        onImagePicked(imageFile);
      } else {
        log('EnhancedBarberImagePicker: No image was selected');
      }
    } catch (e) {
      log('EnhancedBarberImagePicker: Error picking image: $e');
      Get.snackbar(
        'Error',
        'Failed to pick image. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    }
  }
}
