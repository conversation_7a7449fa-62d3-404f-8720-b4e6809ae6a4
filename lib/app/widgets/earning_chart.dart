import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';

class EarningChartWidget extends StatelessWidget {
  final List<double> monthlyEarnings;
  final double totalEarnings;
  final double growthPercentage;
  final String lastUpdated;
  final String timeFilter;
  final List<String> labels;
  final Function(String?)? onTimeFilterChanged;
  final bool isLoading;

  const EarningChartWidget({
    super.key,
    required this.monthlyEarnings,
    required this.totalEarnings,
    required this.growthPercentage,
    required this.lastUpdated,
    this.timeFilter = 'Weekly',
    this.labels = const [],
    this.onTimeFilterChanged,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final displayLabels = labels.isNotEmpty
        ? labels
        : [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
          ];

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryBlack.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and filter
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Earnings Overview',
                style: GoogleFonts.poppins(
                  color: AppConstants.primaryBlack,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
                decoration: BoxDecoration(
                  color: AppConstants.lightGrey,
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                  border: Border.all(color: AppConstants.mediumGrey.withValues(alpha: 0.3)),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: timeFilter,
                    dropdownColor: AppConstants.primaryWhite,
                    icon: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppConstants.primaryBlack,
                      size: 16,
                    ),
                    items: ['Monthly', 'Weekly'].map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(
                          value,
                          style: GoogleFonts.poppins(
                            color: AppConstants.primaryBlack,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: onTimeFilterChanged,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Earnings summary
          Row(
            children: [
              Text(
                '₹${totalEarnings.toStringAsFixed(0)}',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: AppConstants.primaryBlack,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: growthPercentage >= 0 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      growthPercentage >= 0 ? Icons.trending_up : Icons.trending_down,
                      color: growthPercentage >= 0 ? Colors.green : Colors.red,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${growthPercentage >= 0 ? '+' : ''}${growthPercentage.toStringAsFixed(1)}%',
                      style: GoogleFonts.poppins(
                        color: growthPercentage >= 0 ? Colors.green : Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingSmall),
          
          Text(
            'Last updated: $lastUpdated',
            style: GoogleFonts.poppins(
              color: AppConstants.mediumGrey,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Chart
          SizedBox(
            height: 180,
            child: isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: AppConstants.primaryBlack,
                    ),
                  )
                : monthlyEarnings.isEmpty
                    ? Center(
                        child: Text(
                          'No data available',
                          style: GoogleFonts.poppins(
                            color: AppConstants.mediumGrey,
                            fontSize: 14,
                          ),
                        ),
                      )
                    : BarChart(
                        BarChartData(
                          maxY: monthlyEarnings.reduce((a, b) => a > b ? a : b) + 1000,
                          minY: 0,
                          backgroundColor: Colors.transparent,
                          barTouchData: BarTouchData(enabled: false),
                          titlesData: FlTitlesData(
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  final index = value.toInt();
                                  if (index >= 0 && index < displayLabels.length) {
                                    return Padding(
                                      padding: const EdgeInsets.only(top: 8),
                                      child: Text(
                                        displayLabels[index],
                                        style: GoogleFonts.poppins(
                                          color: AppConstants.mediumGrey,
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                                reservedSize: 28,
                                interval: 1,
                              ),
                            ),
                            leftTitles: AxisTitles(
                              sideTitles: SideTitles(
                                reservedSize: 40,
                                showTitles: true,
                                interval: 2000,
                                getTitlesWidget: (value, meta) {
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: Text(
                                      '${(value / 1000).toStringAsFixed(0)}k',
                                      style: GoogleFonts.poppins(
                                        color: AppConstants.mediumGrey,
                                        fontSize: 10,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            topTitles: AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                            rightTitles: AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                          ),
                          gridData: FlGridData(
                            show: true,
                            drawVerticalLine: false,
                            horizontalInterval: 2000,
                            getDrawingHorizontalLine: (value) {
                              return FlLine(
                                color: AppConstants.lightGrey,
                                strokeWidth: 1,
                              );
                            },
                          ),
                          borderData: FlBorderData(show: false),
                          barGroups: monthlyEarnings.asMap().entries.map((entry) {
                            int index = entry.key;
                            double value = entry.value;
                            return BarChartGroupData(
                              x: index,
                              barRods: [
                                BarChartRodData(
                                  toY: value,
                                  width: 16,
                                  borderRadius: BorderRadius.circular(4),
                                  gradient: LinearGradient(
                                    colors: [
                                      AppConstants.primaryBlack,
                                      AppConstants.mediumGrey,
                                    ],
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.topCenter,
                                  ),
                                ),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
          ),
        ],
      ),
    );
  }
}
