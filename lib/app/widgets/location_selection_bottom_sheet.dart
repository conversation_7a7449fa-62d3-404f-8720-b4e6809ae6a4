import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';

class LocationSelectionBottomSheet extends StatelessWidget {
  final VoidCallback? onLocationSelected;

  const LocationSelectionBottomSheet({
    super.key,
    this.onLocationSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 24),

            // Location icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.location_on,
                size: 40,
                color: AppConstants.primaryBlack,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              'Select Your Location',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Description
            Text(
              'To find the nearest salons around you, please select your location first.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppConstants.mediumGrey,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Select Location Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () async {
                  // Navigate to location selection screen
                  final result = await Get.toNamed('/location-selection');
                  
                  if (result != null && result is Map<String, dynamic>) {
                    final latitude = result['latitude'] as double?;
                    final longitude = result['longitude'] as double?;
                    final address = result['address'] as String?;
                    
                    if (latitude != null && longitude != null && address != null) {
                      // Close bottom sheet and notify parent
                      Get.back(result: {
                        'latitude': latitude,
                        'longitude': longitude,
                        'address': address,
                      });
                      
                      // Call callback if provided
                      onLocationSelected?.call();
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryBlack,
                  foregroundColor: AppConstants.primaryWhite,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 20,
                      color: AppConstants.primaryWhite,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Select Location',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryWhite,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Skip for now button (optional)
            TextButton(
              onPressed: () {
                Get.back(result: {'skip': true});
              },
              child: Text(
                'Skip for now',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ),

            // Bottom padding for safe area
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  /// Show the location selection bottom sheet
  static Future<Map<String, dynamic>?> show({
    VoidCallback? onLocationSelected,
  }) async {
    return await Get.bottomSheet<Map<String, dynamic>>(
      LocationSelectionBottomSheet(
        onLocationSelected: onLocationSelected,
      ),
      isScrollControlled: true,
      isDismissible: false, // Don't allow dismissing without selection
      enableDrag: false, // Don't allow dragging to dismiss
      backgroundColor: Colors.transparent,
    );
  }
}
