import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:saloon_app/app/constants/app_constants.dart';

class StackHomePageBanner extends StatelessWidget {
  const StackHomePageBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        Positioned(
          bottom: -30,
          child: Container(
            height: 50,
            width: Get.width / 1.3,
            decoration: BoxDecoration(
              color: AppConstants.mediumGrey,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(100),
                bottomRight: Radius.circular(100),
              ),
            ),
          ),
        ),
        Positioned(
          bottom: -15,
          child: Container(
            height: 35,
            width: Get.width / 1.2,
            decoration: BoxDecoration(
              color: AppConstants.primaryGrey,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(100),
                bottomRight: Radius.circular(100),
              ),
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: AppConstants.primaryWhite,
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: AppConstants.lightGrey.withValues(alpha: 0.5),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 15,
                      bottom: 15,
                      left: 10,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 10, top: 10),
                          child: Text(
                            "Haircut",
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: AppConstants.primaryBlack,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10, top: 0),
                          child: Text(
                            "upto",
                            style: GoogleFonts.poppins(
                              fontSize: 10,
                              color: AppConstants.mediumGrey,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10, top: 0),
                          child: Text(
                            "50% off",
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: AppConstants.primaryBlack,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10, top: 0),
                          child: Text(
                            "01 May 2025 - 15 May 2025",
                            style: GoogleFonts.poppins(
                              fontSize: 10,
                              color: AppConstants.darkGrey,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        const SizedBox(height: 10.0),
                        Container(
                          padding: const EdgeInsets.only(
                            top: 8,
                            bottom: 8,
                            left: 10,
                            right: 10,
                          ),
                          decoration: BoxDecoration(
                            color: AppConstants.primaryBlack,
                            borderRadius: BorderRadius.circular(100),
                            boxShadow: [
                              BoxShadow(
                                color: AppConstants.primaryGrey.withValues(
                                  alpha: 0.3,
                                ),
                                spreadRadius: 1,
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Text(
                                "Get Offer Now",
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: AppConstants.primaryWhite,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 12),
                              CircleAvatar(
                                backgroundColor: AppConstants.primaryWhite,
                                radius: 12,
                                child: const Icon(
                                  Icons.arrow_forward_ios,
                                  color: AppConstants.primaryBlack,
                                  size: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Positioned(
                bottom: 2,
                right: 5,
                child: Image.asset(
                  "assets/homepage/banner.png",
                  height: Get.height / 5.9,
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
