import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../models/barber_models.dart';

class BarberListTile extends StatelessWidget {
  final BarberData barber;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final Function(bool)? onAvailabilityToggle;
  final bool isLoading;

  const BarberListTile({
    super.key,
    required this.barber,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onAvailabilityToggle,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(barber.id),
      background: _buildSwipeBackground(
        color: Colors.blue,
        icon: Icons.edit,
        alignment: Alignment.centerLeft,
        text: 'Edit',
      ),
      secondaryBackground: _buildSwipeBackground(
        color: Colors.red,
        icon: Icons.delete,
        alignment: Alignment.centerRight,
        text: 'Delete',
      ),
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd) {
          onEdit?.call();
        } else if (direction == DismissDirection.endToStart) {
          onDelete?.call();
        }
      },
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.endToStart) {
          return await _showDeleteConfirmation(context);
        }
        return true; // Allow edit swipe
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryBlack.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isLoading ? null : onTap,
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  // Profile Image
                  _buildProfileImage(),
                  
                  const SizedBox(width: AppConstants.paddingMedium),
                  
                  // Barber Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Name
                        Text(
                          barber.displayName,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppConstants.primaryBlack,
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        // Specialties/Bio
                        Text(
                          barber.displayBio,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: AppConstants.mediumGrey,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Star Rating (placeholder for now)
                        Row(
                          children: [
                            ...List.generate(5, (index) {
                              return Icon(
                                Icons.star,
                                size: 16,
                                color: index < 4 
                                    ? Colors.amber 
                                    : AppConstants.lightGrey,
                              );
                            }),
                            const SizedBox(width: 8),
                            Text(
                              '4.0',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: AppConstants.mediumGrey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Availability Toggle
                  Column(
                    children: [
                      Switch(
                        value: barber.available,
                        onChanged: isLoading ? null : onAvailabilityToggle,
                        activeColor: Colors.green,
                        inactiveThumbColor: AppConstants.mediumGrey,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      Text(
                        barber.availabilityStatus,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: barber.available ? Colors.green : AppConstants.mediumGrey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppConstants.lightGrey,
          width: 2,
        ),
      ),
      child: ClipOval(
        child: barber.hasImage
            ? CachedNetworkImage(
                imageUrl: barber.profileImage!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppConstants.lightGrey,
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => _buildDefaultAvatar(),
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: AppConstants.lightGrey,
      child: Icon(
        Icons.person,
        size: 30,
        color: AppConstants.mediumGrey,
      ),
    );
  }

  Widget _buildSwipeBackground({
    required Color color,
    required IconData icon,
    required Alignment alignment,
    required String text,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Align(
        alignment: alignment,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: AppConstants.primaryWhite,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                text,
                style: GoogleFonts.poppins(
                  color: AppConstants.primaryWhite,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool?> _showDeleteConfirmation(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          ),
          title: Text(
            'Delete Barber',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          content: Text(
            'Are you sure you want to delete ${barber.displayName}? This action cannot be undone.',
            style: GoogleFonts.poppins(
              color: AppConstants.mediumGrey,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: AppConstants.mediumGrey,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Delete',
                style: GoogleFonts.poppins(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
