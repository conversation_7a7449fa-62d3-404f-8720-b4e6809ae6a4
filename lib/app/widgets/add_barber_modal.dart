import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../controllers/owner/add_barber_controller.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/enhanced_barber_image_picker.dart';
import '../models/owner_service_models.dart';

class AddBarberModal extends StatelessWidget {
  final bool isEditing;

  const AddBarberModal({super.key, this.isEditing = false});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AddBarberController>();

    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    isEditing ? 'Edit Barber' : 'Add New Barber',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(
                    Icons.close,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ],
            ),
          ),

          // Form content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Image Section
                  Center(
                    child: Obx(
                      () => EnhancedBarberImagePicker(
                        onImagePicked: controller.setProfileImage,
                        currentImage: controller.selectedImage.value,
                        imageUrl: controller.selectedImageUrl.value,
                        isUploading: controller.isUploadingImage.value,
                        uploadProgress: controller.uploadProgress.value,
                        size: 120,
                      ),
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Name Fields
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: controller.firstNameController,
                          labelText: 'First Name *',
                          hintText: 'Enter first name',
                          focusNode: controller.firstNameFocus,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'First name is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: CustomTextField(
                          controller: controller.lastNameController,
                          labelText: 'Last Name *',
                          hintText: 'Enter last name',
                          focusNode: controller.lastNameFocus,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Last name is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Phone
                  CustomTextField(
                    controller: controller.phoneController,
                    labelText: 'Phone Number *',
                    hintText: 'Enter phone number',
                    keyboardType: TextInputType.phone,
                    focusNode: controller.phoneFocus,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Phone number is required';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Email
                  CustomTextField(
                    controller: controller.emailController,
                    labelText: 'Email *',
                    hintText: 'Enter email address',
                    keyboardType: TextInputType.emailAddress,
                    focusNode: controller.emailFocus,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Email is required';
                      }
                      if (!GetUtils.isEmail(value.trim())) {
                        return 'Enter valid email';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Bio
                  CustomTextField(
                    controller: controller.bioController,
                    labelText: 'Bio (Optional)',
                    hintText: 'Enter barber bio or specialties',
                    maxLines: 3,
                    focusNode: controller.bioFocus,
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Specialties Section
                  _buildSpecialtiesSection(controller),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Services Selection
                  _buildServicesSection(controller),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Availability Toggle
                  _buildAvailabilitySection(controller),

                  const SizedBox(height: AppConstants.paddingXLarge),
                ],
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            decoration: BoxDecoration(
              color: AppConstants.primaryWhite,
              border: Border(
                top: BorderSide(color: AppConstants.lightGrey, width: 1),
              ),
            ),
            child: Column(
              children: [
                Obx(
                  () => CustomButton(
                    text: isEditing ? 'Update Barber' : 'Add Barber',
                    onPressed: isEditing
                        ? controller.updateBarber
                        : controller.addBarber,
                    isLoading:
                        controller.isAddingBarber.value ||
                        controller.isUpdatingBarber.value,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                CustomButton(
                  text: 'Cancel',
                  onPressed: () => Get.back(),
                  isOutlined: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialtiesSection(AddBarberController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Specialties',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.lightGrey,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
          child: Obx(
            () => Wrap(
              spacing: AppConstants.paddingSmall,
              runSpacing: AppConstants.paddingSmall,
              children: controller.predefinedSpecialties.map((specialty) {
                final isSelected = controller.selectedSpecialties.contains(
                  specialty,
                );
                return FilterChip(
                  label: Text(specialty),
                  selected: isSelected,
                  onSelected: (selected) {
                    controller.toggleSpecialtySelection(specialty);
                  },
                  selectedColor: AppConstants.primaryBlack.withValues(
                    alpha: 0.1,
                  ),
                  checkmarkColor: AppConstants.primaryBlack,
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildServicesSection(AddBarberController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Services *',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.lightGrey,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
          child: Obx(
            () => controller.isLoadingServices.value
                ? const Center(child: CircularProgressIndicator())
                : controller.availableServices.isEmpty
                ? Text(
                    'No services available. Please add services first.',
                    style: GoogleFonts.poppins(color: AppConstants.mediumGrey),
                  )
                : Column(
                    children: controller.availableServices.map((service) {
                      final isSelected = controller.selectedServiceIds.contains(
                        service.id,
                      );
                      return CheckboxListTile(
                        title: Text(
                          service.name,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          '${service.categoryName} • ₹${service.price} • ${service.length} min',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: AppConstants.mediumGrey,
                          ),
                        ),
                        value: isSelected,
                        onChanged: (value) {
                          controller.toggleServiceSelection(service.id);
                        },
                        activeColor: AppConstants.primaryBlack,
                        contentPadding: EdgeInsets.zero,
                      );
                    }).toList(),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildAvailabilitySection(AddBarberController controller) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Availability Status',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlack,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.isAvailable.value ? 'Online' : 'Offline',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: controller.isAvailable.value
                          ? Colors.green
                          : AppConstants.mediumGrey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Obx(
            () => Switch(
              value: controller.isAvailable.value,
              onChanged: (value) {
                controller.isAvailable.value = value;
              },
              activeColor: Colors.green,
            ),
          ),
        ],
      ),
    );
  }
}
