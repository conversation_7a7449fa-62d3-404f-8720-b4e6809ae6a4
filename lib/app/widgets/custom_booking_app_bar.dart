import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../controllers/appointment_management_controller.dart';

class CustomBookingAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final AppointmentManagementController? controller;

  const CustomBookingAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.primaryWhite,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: showBackButton
          ? IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppConstants.primaryBlack,
                size: 20,
              ),
              onPressed: () => Get.back(),
            )
          : null,
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryBlack,
        ),
      ),
      centerTitle: true,
      actions: [
        // Refresh Button
        Obx(() {
          final isRefreshing = controller?.isRefreshing.value ?? false;
          return Container(
            margin: const EdgeInsets.only(right: 16),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: isRefreshing ? null : () => _handleRefresh(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppConstants.lightGrey,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: isRefreshing
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppConstants.primaryBlack,
                            ),
                          ),
                        )
                      : Icon(
                          Icons.refresh,
                          color: AppConstants.primaryBlack,
                          size: 20,
                        ),
                ),
              ),
            ),
          );
        }),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Container(height: 1, color: AppConstants.lightGrey),
      ),
    );
  }

  void _handleRefresh() async {
    if (controller != null) {
      try {
        // Show refresh feedback
        _showRefreshFeedback();

        // Trigger refresh
        await controller!.refreshAllData();

        // Show success feedback
        _showSuccessFeedback();
      } catch (e) {
        // Show error feedback
        _showErrorFeedback();
      }
    }
  }

  void _showRefreshFeedback() {
    // Optional: Add haptic feedback
    // HapticFeedback.lightImpact();
  }

  void _showSuccessFeedback() {
    Get.snackbar(
      'Refreshed',
      'Appointments updated successfully',
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppConstants.primaryBlack.withValues(alpha: 0.9),
      colorText: AppConstants.primaryWhite,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: Icon(
        Icons.check_circle,
        color: AppConstants.primaryWhite,
        size: 20,
      ),
    );
  }

  void _showErrorFeedback() {
    Get.snackbar(
      'Error',
      'Failed to refresh appointments. Please try again.',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red.withValues(alpha: 0.9),
      colorText: AppConstants.primaryWhite,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: Icon(
        Icons.error_outline,
        color: AppConstants.primaryWhite,
        size: 20,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 1);
}
