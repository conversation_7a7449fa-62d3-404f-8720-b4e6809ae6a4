import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';

/// Step indicator widget for multi-step processes
class StepIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepTitles;
  final Color activeColor;
  final Color inactiveColor;
  final Color completedColor;
  final double lineHeight;
  final double circleRadius;
  final bool showTitles;
  final VoidCallback? onStepTap;

  const StepIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitles,
    this.activeColor = Colors.blue,
    this.inactiveColor = Colors.grey,
    this.completedColor = Colors.green,
    this.lineHeight = 2.0,
    this.circleRadius = 12.0,
    this.showTitles = true,
    this.onStepTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingLarge,
        vertical: AppConstants.paddingMedium,
      ),
      child: Column(
        children: [
          // Step circles and lines
          SizedBox(
            height: circleRadius * 2,
            child: Row(children: _buildStepIndicators()),
          ),

          if (showTitles) ...[
            const SizedBox(height: AppConstants.paddingSmall),
            // Step titles
            Row(children: _buildStepTitles()),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildStepIndicators() {
    List<Widget> indicators = [];

    for (int i = 0; i < totalSteps; i++) {
      // Add step circle
      indicators.add(_buildStepCircle(i));

      // Add connecting line (except for last step)
      if (i < totalSteps - 1) {
        indicators.add(_buildConnectingLine(i));
      }
    }

    return indicators;
  }

  Widget _buildStepCircle(int stepIndex) {
    final isCompleted = stepIndex < currentStep;
    final isActive = stepIndex == currentStep;
    final isInactive = stepIndex > currentStep;

    Color circleColor;
    Color textColor;
    Widget child;

    if (isCompleted) {
      circleColor = completedColor;
      textColor = AppConstants.primaryWhite;
      child = Icon(Icons.check, size: circleRadius, color: textColor);
    } else if (isActive) {
      circleColor = activeColor;
      textColor = AppConstants.primaryWhite;
      child = Text(
        '${stepIndex + 1}',
        style: GoogleFonts.poppins(
          fontSize: circleRadius * 0.7,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      );
    } else {
      circleColor = inactiveColor.withOpacity(0.3);
      textColor = inactiveColor;
      child = Text(
        '${stepIndex + 1}',
        style: GoogleFonts.poppins(
          fontSize: circleRadius * 0.7,
          fontWeight: FontWeight.w500,
          color: textColor,
        ),
      );
    }

    return GestureDetector(
      onTap: onStepTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: circleRadius * 2,
        height: circleRadius * 2,
        decoration: BoxDecoration(
          color: circleColor,
          shape: BoxShape.circle,
          border: isActive
              ? Border.all(color: activeColor.withOpacity(0.3), width: 3)
              : null,
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: activeColor.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ]
              : null,
        ),
        child: Center(child: child),
      ),
    );
  }

  Widget _buildConnectingLine(int stepIndex) {
    final isCompleted = stepIndex < currentStep;
    final isActive = stepIndex == currentStep - 1;

    Color lineColor;
    if (isCompleted || isActive) {
      lineColor = completedColor;
    } else {
      lineColor = inactiveColor.withOpacity(0.3);
    }

    return Expanded(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: lineHeight,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: lineColor,
          borderRadius: BorderRadius.circular(lineHeight / 2),
        ),
      ),
    );
  }

  List<Widget> _buildStepTitles() {
    return stepTitles.asMap().entries.map((entry) {
      final index = entry.key;
      final title = entry.value;

      final isCompleted = index < currentStep;
      final isActive = index == currentStep;

      Color textColor;
      FontWeight fontWeight;

      if (isCompleted) {
        textColor = completedColor;
        fontWeight = FontWeight.w600;
      } else if (isActive) {
        textColor = activeColor;
        fontWeight = FontWeight.w700;
      } else {
        textColor = inactiveColor;
        fontWeight = FontWeight.w500;
      }

      return Expanded(
        child: AnimatedDefaultTextStyle(
          duration: const Duration(milliseconds: 300),
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: fontWeight,
            color: textColor,
          ),
          textAlign: TextAlign.center,
          child: Text(title, maxLines: 2, overflow: TextOverflow.ellipsis),
        ),
      );
    }).toList();
  }
}

/// Booking-specific step indicator
class BookingStepIndicator extends StatelessWidget {
  final int currentStep;
  final VoidCallback? onStepTap;

  const BookingStepIndicator({
    super.key,
    required this.currentStep,
    this.onStepTap,
  });

  @override
  Widget build(BuildContext context) {
    return StepIndicator(
      currentStep: currentStep,
      totalSteps: 3,
      stepTitles: const ['Services & Time', 'Summary', 'Confirmation'],
      activeColor: AppConstants.primaryBlack,
      inactiveColor: AppConstants.mediumGrey,
      completedColor: Colors.green,
      onStepTap: onStepTap,
    );
  }
}

/// Progress bar style step indicator
class ProgressStepIndicator extends StatelessWidget {
  final double progress; // 0.0 to 1.0
  final Color activeColor;
  final Color backgroundColor;
  final double height;
  final String? label;

  const ProgressStepIndicator({
    super.key,
    required this.progress,
    this.activeColor = Colors.blue,
    this.backgroundColor = Colors.grey,
    this.height = 4.0,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingLarge,
        vertical: AppConstants.paddingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null) ...[
            Text(
              label!,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
          ],

          ClipRRect(
            borderRadius: BorderRadius.circular(height / 2),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: backgroundColor.withOpacity(0.3),
              valueColor: AlwaysStoppedAnimation<Color>(activeColor),
              minHeight: height,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${((progress * 3).ceil()).clamp(1, 3)} of 3',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: AppConstants.mediumGrey,
                ),
              ),
              Text(
                '${(progress * 100).round()}% Complete',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: activeColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
