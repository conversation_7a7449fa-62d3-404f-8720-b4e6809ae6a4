import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';
import '../constants/app_constants.dart';

class EnhancedProfileImagePicker extends StatelessWidget {
  final Function(File?) onImagePicked;
  final File? currentImage;
  final String? imageUrl;
  final double size;

  const EnhancedProfileImagePicker({
    super.key,
    required this.onImagePicked,
    this.currentImage,
    this.imageUrl,
    this.size = 120,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: AppConstants.lightGrey,
          borderRadius: BorderRadius.circular(size / 2),
          border: Border.all(
            color: AppConstants.primaryGrey,
            width: 2,
          ),
        ),
        child: _buildImageContent(),
      ),
    );
  }

  Widget _buildImageContent() {
    // Priority: Local file > Network URL > Placeholder
    if (currentImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: Image.file(
          currentImage!,
          fit: BoxFit.cover,
        ),
      );
    } else if (imageUrl != null && imageUrl!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: CachedNetworkImage(
          imageUrl: imageUrl!,
          fit: BoxFit.cover,
          placeholder: (context, url) => _buildLoadingPlaceholder(),
          errorWidget: (context, url, error) => _buildErrorPlaceholder(),
        ),
      );
    } else {
      return _buildDefaultPlaceholder();
    }
  }

  Widget _buildLoadingPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppConstants.mediumGrey),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: size * 0.3,
            color: Colors.red.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 4),
          Text(
            'Failed to load',
            style: TextStyle(
              fontSize: size * 0.08,
              color: Colors.red.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.person,
          size: size * 0.4,
          color: AppConstants.mediumGrey,
        ),
        const SizedBox(height: 8),
        Text(
          'Add Photo',
          style: TextStyle(
            fontSize: size * 0.1,
            color: AppConstants.mediumGrey,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _showImagePickerOptions() {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: AppConstants.primaryWhite,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppConstants.radiusLarge),
          ),
        ),
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppConstants.lightGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            Text(
              'Select Profile Photo',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Camera Option
            _buildOption(
              icon: Icons.camera_alt,
              title: 'Take Photo',
              subtitle: 'Use camera to take a new photo',
              onTap: () {
                Get.back();
                _pickImage(ImageSource.camera);
              },
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Gallery Option
            _buildOption(
              icon: Icons.photo_library,
              title: 'Choose from Gallery',
              subtitle: 'Select from your photo library',
              onTap: () {
                Get.back();
                _pickImage(ImageSource.gallery);
              },
            ),
            
            if (currentImage != null || (imageUrl != null && imageUrl!.isNotEmpty)) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Remove Option
              _buildOption(
                icon: Icons.delete,
                title: 'Remove Photo',
                subtitle: 'Remove current profile photo',
                iconColor: Colors.red,
                onTap: () {
                  Get.back();
                  onImagePicked(null);
                },
              ),
            ],
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Cancel Button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: (iconColor ?? AppConstants.primaryBlack).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: Icon(
                icon,
                color: iconColor ?? AppConstants.primaryBlack,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Get.textTheme.bodyMedium?.copyWith(
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 80,
      );
      
      if (image != null) {
        onImagePicked(File(image.path));
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to pick image: $e',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
      );
    }
  }
}
