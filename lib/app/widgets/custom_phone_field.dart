import 'package:flutter/material.dart';
import 'package:country_picker/country_picker.dart';
import '../constants/app_constants.dart';

class CustomPhoneField extends StatefulWidget {
  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final String? Function(String?)? validator;
  final Function(Country)? onCountryChanged;
  final Country? initialCountry;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final Function(String)? onFieldSubmitted;

  const CustomPhoneField({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.validator,
    this.onCountryChanged,
    this.initialCountry,
    this.focusNode,
    this.textInputAction,
    this.onFieldSubmitted,
  });

  @override
  State<CustomPhoneField> createState() => _CustomPhoneFieldState();
}

class _CustomPhoneFieldState extends State<CustomPhoneField> {
  late Country _selectedCountry;

  @override
  void initState() {
    super.initState();
    // Default to India if no initial country is provided
    _selectedCountry =
        widget.initialCountry ??
        Country(
          phoneCode: '91',
          countryCode: 'IN',
          e164Sc: 0,
          geographic: true,
          level: 1,
          name: 'India',
          example: '9123456789',
          displayName: 'India (IN) [+91]',
          displayNameNoCountryCode: 'India (IN)',
          e164Key: '',
        );
  }

  void _showCountryPicker() {
    showCountryPicker(
      context: context,
      showPhoneCode: true,
      onSelect: (Country country) {
        setState(() {
          _selectedCountry = country;
        });
        widget.onCountryChanged?.call(country);
      },
      countryListTheme: CountryListThemeData(
        flagSize: 25,
        backgroundColor: AppConstants.primaryWhite,
        textStyle: TextStyle(
          fontSize: 16,
          color: AppConstants.primaryBlack,
          fontFamily: 'Poppins',
        ),
        bottomSheetHeight: MediaQuery.of(context).size.height * 0.7,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppConstants.radiusLarge),
        ),
        inputDecoration: InputDecoration(
          labelText: 'Search',
          hintText: 'Start typing to search',
          prefixIcon: const Icon(Icons.search),
          filled: true,
          fillColor: AppConstants.lightGrey,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            borderSide: const BorderSide(
              color: AppConstants.primaryBlack,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null) ...[
          Text(
            widget.labelText!,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
        Row(
          children: [
            // Country Selector
            GestureDetector(
              onTap: _showCountryPicker,
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                ),
                decoration: BoxDecoration(
                  color: AppConstants.lightGrey,
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                  border: Border.all(color: Colors.transparent, width: 2),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Flag
                    Text(
                      _selectedCountry.flagEmoji,
                      style: const TextStyle(fontSize: 24),
                    ),

                    const SizedBox(width: AppConstants.paddingSmall),

                    // Country Code
                    Text(
                      '+${_selectedCountry.phoneCode}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(width: AppConstants.paddingSmall),

                    // Dropdown Icon
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: AppConstants.mediumGrey,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(width: AppConstants.paddingMedium),

            // Phone Number Input
            Expanded(
              child: TextFormField(
                controller: widget.controller,
                validator: widget.validator,
                keyboardType: TextInputType.phone,
                focusNode: widget.focusNode,
                textInputAction: widget.textInputAction,
                onFieldSubmitted: widget.onFieldSubmitted,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  filled: true,
                  fillColor: AppConstants.lightGrey,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusMedium,
                    ),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusMedium,
                    ),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusMedium,
                    ),
                    borderSide: const BorderSide(
                      color: AppConstants.primaryBlack,
                      width: 2,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusMedium,
                    ),
                    borderSide: const BorderSide(color: Colors.red, width: 1),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusMedium,
                    ),
                    borderSide: const BorderSide(color: Colors.red, width: 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingMedium,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
