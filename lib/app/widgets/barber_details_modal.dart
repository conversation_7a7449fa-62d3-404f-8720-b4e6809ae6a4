import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/barber_models.dart';
import '../controllers/owner/add_barber_controller.dart';

class BarberDetailsModal extends StatelessWidget {
  final BarberData barber;

  const BarberDetailsModal({
    super.key,
    required this.barber,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AddBarberController>();

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.lightGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Barber Details',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(
                    Icons.close,
                    color: AppConstants.primaryBlack,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Section
                  _buildProfileSection(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Contact Information
                  _buildContactSection(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Bio Section
                  _buildBioSection(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Services Section
                  _buildServicesSection(controller),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Statistics Section (placeholder)
                  _buildStatisticsSection(),

                  const SizedBox(height: AppConstants.paddingLarge),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return Center(
      child: Column(
        children: [
          // Profile Image
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppConstants.lightGrey,
                width: 3,
              ),
            ),
            child: ClipOval(
              child: barber.hasImage
                  ? CachedNetworkImage(
                      imageUrl: barber.profileImage!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppConstants.lightGrey,
                        child: const Center(
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                      errorWidget: (context, url, error) => _buildDefaultAvatar(),
                    )
                  : _buildDefaultAvatar(),
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Name
          Text(
            barber.displayName,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Availability Status
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingSmall,
            ),
            decoration: BoxDecoration(
              color: barber.available ? Colors.green : AppConstants.mediumGrey,
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            child: Text(
              barber.availabilityStatus,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppConstants.primaryWhite,
              ),
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Star Rating
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(5, (index) {
                return Icon(
                  Icons.star,
                  size: 20,
                  color: index < 4 ? Colors.amber : AppConstants.lightGrey,
                );
              }),
              const SizedBox(width: 8),
              Text(
                '4.0 (25 reviews)',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppConstants.mediumGrey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: AppConstants.lightGrey,
      child: Icon(
        Icons.person,
        size: 50,
        color: AppConstants.mediumGrey,
      ),
    );
  }

  Widget _buildContactSection() {
    return _buildSection(
      title: 'Contact Information',
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.phone,
            label: 'Phone',
            value: barber.displayPhone,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildInfoRow(
            icon: Icons.email,
            label: 'Email',
            value: barber.displayEmail,
          ),
        ],
      ),
    );
  }

  Widget _buildBioSection() {
    return _buildSection(
      title: 'About',
      child: Text(
        barber.displayBio,
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: AppConstants.mediumGrey,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildServicesSection(AddBarberController controller) {
    return Obx(
      () => _buildSection(
        title: 'Services (${barber.serviceIds.length})',
        child: controller.availableServices.isEmpty
            ? Text(
                'Loading services...',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppConstants.mediumGrey,
                ),
              )
            : Column(
                children: controller.availableServices
                    .where((service) => barber.serviceIds.contains(service.id))
                    .map((service) => Container(
                          margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                          padding: const EdgeInsets.all(AppConstants.paddingMedium),
                          decoration: BoxDecoration(
                            color: AppConstants.lightGrey,
                            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      service.name,
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppConstants.primaryBlack,
                                      ),
                                    ),
                                    Text(
                                      service.categoryName,
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        color: AppConstants.mediumGrey,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    '₹${service.price}',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: AppConstants.primaryBlack,
                                    ),
                                  ),
                                  Text(
                                    '${service.length} min',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: AppConstants.mediumGrey,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ))
                    .toList(),
              ),
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return _buildSection(
      title: 'Statistics',
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'Total Bookings',
              value: '156',
              icon: Icons.calendar_today,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              title: 'This Month',
              value: '23',
              icon: Icons.trending_up,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryBlack,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        child,
      ],
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppConstants.mediumGrey,
        ),
        const SizedBox(width: AppConstants.paddingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: AppConstants.mediumGrey,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.lightGrey,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: AppConstants.primaryBlack,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryBlack,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppConstants.mediumGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
