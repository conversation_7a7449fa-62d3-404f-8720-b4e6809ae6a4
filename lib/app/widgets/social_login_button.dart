import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class SocialLoginButton extends StatelessWidget {
  final IconData icon;
  final String text;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.text,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppConstants.primaryWhite,
          foregroundColor: textColor ?? AppConstants.primaryBlack,
          elevation: 0,
          side: const BorderSide(
            color: AppConstants.lightGrey,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 24,
              color: iconColor ?? (backgroundColor != null ? AppConstants.primaryWhite : AppConstants.primaryBlack),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Text(
              text,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: textColor ?? (backgroundColor != null ? AppConstants.primaryWhite : AppConstants.primaryBlack),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
