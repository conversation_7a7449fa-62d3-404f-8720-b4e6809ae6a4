/// Razorpay Configuration for Salon Booking App
class RazorpayConfig {
  // Razorpay Test Credentials
  static const String keyId = 'rzp_test_cW5wDOSELiIvAr';
  static const String keySecret = 'JpTP0E884120j7MJZHO77nEY';

  // Company/App Details
  static const String companyName = 'Salon Booking';
  static const String companyDescription = 'Appointment booking payment';
  static const String companyLogo = ''; // Add your logo URL here

  // Theme Configuration
  static const String themeColor = '#000000'; // App primary black color

  // Currency
  static const String currency = 'INR';

  // Payment Methods (optional - Razorpay will show all by default)
  static const List<String> paymentMethods = [
    'card',
    'netbanking',
    'wallet',
    'upi',
  ];

  // Timeout settings
  static const int timeoutInSeconds = 300; // 5 minutes

  // Validation
  static bool get isConfigValid {
    return keyId.isNotEmpty && keySecret.isNotEmpty;
  }

  /// Get Razorpay options for payment
  static Map<String, dynamic> getPaymentOptions({
    required String orderId,
    required double amount,
    required String appointmentId,
    String? userEmail,
    String? userPhone,
    String? userName,
  }) {
    // Ensure all values are properly formatted and not null
    final Map<String, String> prefillData = {};

    if (userEmail != null && userEmail.trim().isNotEmpty) {
      prefillData['email'] = userEmail.trim();
    }

    if (userPhone != null && userPhone.trim().isNotEmpty) {
      prefillData['contact'] = userPhone.trim();
    }

    if (userName != null && userName.trim().isNotEmpty) {
      prefillData['name'] = userName.trim();
    }

    // Build the options map with safe values
    final Map<String, dynamic> options = {
      'key': keyId,
      'amount': (amount * 100).toInt(), // Amount in paise (multiply by 100)
      'name': companyName,
      'description':
          '$companyDescription - Appointment #${appointmentId.length > 8 ? appointmentId.substring(0, 8) : appointmentId}',
      'order_id': orderId,
      'currency': currency,
    };

    // Only add prefill if we have data
    if (prefillData.isNotEmpty) {
      options['prefill'] = prefillData;
    }

    // Add theme
    options['theme'] = {'color': themeColor};

    // Add notes
    options['notes'] = {
      'appointment_id': appointmentId,
      'payment_for': 'salon_appointment',
    };

    return options;
  }

  /// Validate Razorpay response
  static bool validatePaymentResponse(Map<String, dynamic> response) {
    final requiredFields = [
      'razorpay_payment_id',
      'razorpay_order_id',
      'razorpay_signature',
    ];

    for (final field in requiredFields) {
      if (!response.containsKey(field) ||
          response[field] == null ||
          response[field].toString().isEmpty) {
        return false;
      }
    }

    return true;
  }

  /// Extract payment details from Razorpay response
  static Map<String, String> extractPaymentDetails(
    Map<String, dynamic> response,
  ) {
    return {
      'paymentId': response['razorpay_payment_id']?.toString() ?? '',
      'orderId': response['razorpay_order_id']?.toString() ?? '',
      'signature': response['razorpay_signature']?.toString() ?? '',
    };
  }

  /// Format amount for display
  static String formatAmount(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Convert paise to rupees
  static double paiseToRupees(int paise) {
    return paise / 100.0;
  }

  /// Convert rupees to paise
  static int rupeesToPaise(double rupees) {
    return (rupees * 100).toInt();
  }
}
