import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/bank_account_models.dart';
import '../services/shared_preferences_service.dart';

class BankAccountService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Add bank account details
  /// Endpoint: POST /wallet/bankDetails
  static Future<BankAccountResponse> addBankAccount(
    BankAccountRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('BankAccountService: No auth token found');
        return BankAccountResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/bankDetails');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());
      
      log('BankAccountService: Adding bank account');
      log('BankAccountService: Request URL: ${url.toString()}');
      log('BankAccountService: Request body: $body');
      
      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('BankAccountService: Response status: ${response.statusCode}');
      log('BankAccountService: Response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return BankAccountResponse.fromJson(jsonData);
      } else if (response.statusCode == 400) {
        // Handle UPI verification failed or other validation errors
        return BankAccountResponse(
          code: jsonData['code'] ?? 400,
          success: false,
          message: jsonData['message'] ?? 'Validation failed',
        );
      } else if (response.statusCode == 401) {
        log('BankAccountService: Unauthorized - token may be expired');
        return BankAccountResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else {
        log('BankAccountService: Error ${response.statusCode}: ${response.body}');
        return BankAccountResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to add bank account',
        );
      }
    } on SocketException {
      log('BankAccountService: No internet connection');
      return BankAccountResponse(
        code: 0,
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('BankAccountService: Client exception: $e');
      return BankAccountResponse(
        code: 0,
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('BankAccountService: Exception occurred: $e');
      return BankAccountResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get bank account details
  /// Endpoint: GET /wallet/bankDetails
  static Future<BankAccountResponse> getBankAccountDetails() async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('BankAccountService: No auth token found for get request');
        return BankAccountResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/bankDetails');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      log('BankAccountService: Fetching bank account details');
      log('BankAccountService: Request URL: ${url.toString()}');
      
      final response = await http
          .get(url, headers: headers)
          .timeout(_timeoutDuration);

      log('BankAccountService: Get response status: ${response.statusCode}');
      log('BankAccountService: Get response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return BankAccountResponse.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        // No bank account found
        return BankAccountResponse(
          code: 404,
          success: false,
          message: 'No bank account details found',
        );
      } else if (response.statusCode == 401) {
        log('BankAccountService: Unauthorized during get');
        return BankAccountResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else {
        log('BankAccountService: Get error ${response.statusCode}: ${response.body}');
        return BankAccountResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to fetch bank account details',
        );
      }
    } on SocketException {
      log('BankAccountService: No internet connection during get');
      return BankAccountResponse(
        code: 0,
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } catch (e) {
      log('BankAccountService: Get exception occurred: $e');
      return BankAccountResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Update bank account details
  /// Endpoint: PUT /wallet/bankDetails
  static Future<BankAccountResponse> updateBankAccount(
    BankAccountRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('BankAccountService: No auth token found for update');
        return BankAccountResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/bankDetails');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());
      
      log('BankAccountService: Updating bank account');
      log('BankAccountService: Update request body: $body');
      
      final response = await http
          .put(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('BankAccountService: Update response status: ${response.statusCode}');
      log('BankAccountService: Update response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return BankAccountResponse.fromJson(jsonData);
      } else if (response.statusCode == 400) {
        return BankAccountResponse(
          code: jsonData['code'] ?? 400,
          success: false,
          message: jsonData['message'] ?? 'Validation failed',
        );
      } else if (response.statusCode == 401) {
        log('BankAccountService: Unauthorized during update');
        return BankAccountResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else {
        log('BankAccountService: Update error ${response.statusCode}: ${response.body}');
        return BankAccountResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to update bank account',
        );
      }
    } catch (e) {
      log('BankAccountService: Update exception occurred: $e');
      return BankAccountResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Helper method to validate bank account request
  static bool validateBankAccountRequest(BankAccountRequest request) {
    // Validate all required fields
    if (request.accountHolderName.trim().isEmpty ||
        request.bankName.trim().isEmpty ||
        request.bankAccountNumber.trim().isEmpty ||
        request.bankIfscCode.trim().isEmpty ||
        request.linkedMobileNumber.trim().isEmpty ||
        request.upiId.trim().isEmpty) {
      log('BankAccountService: Missing required fields');
      return false;
    }

    // Validate formats
    if (!BankAccountValidator.isValidAccountNumber(request.bankAccountNumber)) {
      log('BankAccountService: Invalid account number format');
      return false;
    }

    if (!BankAccountValidator.isValidIFSC(request.bankIfscCode)) {
      log('BankAccountService: Invalid IFSC code format');
      return false;
    }

    if (!BankAccountValidator.isValidUPI(request.upiId)) {
      log('BankAccountService: Invalid UPI ID format');
      return false;
    }

    if (!BankAccountValidator.isValidMobileNumber(request.linkedMobileNumber)) {
      log('BankAccountService: Invalid mobile number format');
      return false;
    }

    return true;
  }
}
