import 'dart:developer';
import 'dart:math' hide log;
import 'shared_preferences_service.dart';

/// Fallback device info service for testing without native plugins
class DeviceInfoServiceFallback {
  static final DeviceInfoServiceFallback _instance =
      DeviceInfoServiceFallback._internal();
  factory DeviceInfoServiceFallback() => _instance;
  DeviceInfoServiceFallback._internal();

  /// Generate a mock device ID for testing
  String _generateMockDeviceId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSuffix = random.nextInt(9999).toString().padLeft(4, '0');
    return 'mock_device_${timestamp}_$randomSuffix';
  }

  /// Generate a mock FCM token for testing
  String _generateMockFcmToken() {
    final random = Random();
    final chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final length = 152; // Typical FCM token length

    return List.generate(
      length,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// Get device ID (mock version)
  Future<String> getDeviceId() async {
    try {
      // Try to get cached device ID first
      final cachedId = await SharedPreferencesService.getDeviceId();
      if (cachedId != null &&
          cachedId.isNotEmpty &&
          !cachedId.startsWith('error_')) {
        return cachedId;
      }

      // Generate new mock device ID
      final deviceId = _generateMockDeviceId();
      await SharedPreferencesService.saveDeviceId(deviceId);

      log('DeviceInfoServiceFallback: Generated mock device ID: $deviceId');
      return deviceId;
    } catch (e) {
      log('DeviceInfoServiceFallback: Error generating device ID: $e');
      return 'fallback_device_id_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Get FCM token (mock version)
  Future<String?> getFcmToken() async {
    try {
      // Try to get cached FCM token first
      final cachedToken = await SharedPreferencesService.getFcmToken();
      if (cachedToken != null &&
          cachedToken.isNotEmpty &&
          !cachedToken.startsWith('error_')) {
        return cachedToken;
      }

      // Generate new mock FCM token
      final fcmToken = _generateMockFcmToken();
      await SharedPreferencesService.saveFcmToken(fcmToken);

      log(
        'DeviceInfoServiceFallback: Generated mock FCM token: ${fcmToken.substring(0, 20)}...',
      );
      return fcmToken;
    } catch (e) {
      log('DeviceInfoServiceFallback: Error generating FCM token: $e');
      return 'fallback_fcm_token_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Get device token (same as FCM token for this app)
  Future<String?> getDeviceToken() async {
    return await getFcmToken();
  }

  /// Initialize and get all device information
  Future<Map<String, String?>> initializeDeviceInfo() async {
    try {
      log('DeviceInfoServiceFallback: Initializing mock device info...');

      // Get device ID
      final deviceId = await getDeviceId();

      // Get FCM token
      final fcmToken = await getFcmToken();

      // Device token is the same as FCM token for this app
      final deviceToken = fcmToken;

      // Save to SharedPreferences
      await SharedPreferencesService.saveDeviceId(deviceId);
      if (fcmToken != null) {
        await SharedPreferencesService.saveFcmToken(fcmToken);
        await SharedPreferencesService.saveDeviceToken(fcmToken);
      }

      final deviceInfo = {
        'deviceId': deviceId,
        'fcmToken': fcmToken,
        'deviceToken': deviceToken,
      };

      log(
        'DeviceInfoServiceFallback: Mock device info initialized successfully',
      );
      log('DeviceInfoServiceFallback: Device ID: $deviceId');
      log(
        'DeviceInfoServiceFallback: FCM Token: ${fcmToken?.substring(0, 20)}...',
      );

      return deviceInfo;
    } catch (e) {
      log('DeviceInfoServiceFallback: Error initializing mock device info: $e');
      return {
        'deviceId': 'error_device_id',
        'fcmToken': null,
        'deviceToken': null,
      };
    }
  }

  /// Get cached device info from SharedPreferences
  Future<Map<String, String?>> getCachedDeviceInfo() async {
    try {
      return await SharedPreferencesService.getDeviceInfo();
    } catch (e) {
      log('DeviceInfoServiceFallback: Error getting cached device info: $e');
      return {'deviceId': null, 'fcmToken': null, 'deviceToken': null};
    }
  }

  /// Get device information for API requests
  Future<Map<String, String>> getDeviceInfoForApi() async {
    try {
      final cachedInfo = await getCachedDeviceInfo();

      // If cached info is incomplete, reinitialize
      if (cachedInfo['deviceId'] == null || cachedInfo['fcmToken'] == null) {
        log(
          'DeviceInfoServiceFallback: Cached info incomplete, reinitializing...',
        );
        final freshInfo = await initializeDeviceInfo();
        return {
          'deviceId': freshInfo['deviceId'] ?? 'unknown_device',
          'fcmToken': freshInfo['fcmToken'] ?? 'no_fcm_token',
          'deviceToken': freshInfo['deviceToken'] ?? 'no_device_token',
        };
      }

      return {
        'deviceId': cachedInfo['deviceId'] ?? 'unknown_device',
        'fcmToken': cachedInfo['fcmToken'] ?? 'no_fcm_token',
        'deviceToken': cachedInfo['deviceToken'] ?? 'no_device_token',
      };
    } catch (e) {
      log('DeviceInfoServiceFallback: Error getting device info for API: $e');
      return {
        'deviceId': 'error_device',
        'fcmToken': 'error_fcm_token',
        'deviceToken': 'error_device_token',
      };
    }
  }

  /// Debug method to print all device information
  Future<void> debugPrintDeviceInfo() async {
    try {
      log('=== DeviceInfoServiceFallback Debug Info ===');

      final deviceInfo = await getDeviceInfoForApi();

      log('Device ID: ${deviceInfo['deviceId']}');
      log('FCM Token: ${deviceInfo['fcmToken']?.substring(0, 20)}...');
      log('Device Token: ${deviceInfo['deviceToken']?.substring(0, 20)}...');
      log('Platform: Mock/Fallback');
      log('Note: This is using fallback mock data for testing');

      log('==========================================');
    } catch (e) {
      log('DeviceInfoServiceFallback: Error in debug print: $e');
    }
  }
}
