import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';
import '../services/salon_image_service.dart';
import '../services/permission_service.dart';

class ImagePickerService {
  static final ImagePicker _picker = ImagePicker();

  /// Show image picker bottom sheet
  static Future<File?> showImagePicker({
    required BuildContext context,
    bool allowCropping = true,
    CropAspectRatio? aspectRatio,
    String title = 'Select Image',
  }) async {
    return await showModalBottomSheet<File?>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _ImagePickerBottomSheet(
        title: title,
        allowCropping: allowCropping,
        aspectRatio: aspectRatio,
      ),
    );
  }

  /// Pick image from camera
  static Future<File?> pickFromCamera({
    bool allowCropping = true,
    CropAspectRatio? aspectRatio,
  }) async {
    try {
      log('ImagePickerService: Starting camera image pick');

      // Check camera permission using PermissionService
      final hasPermission = await PermissionService.requestCameraPermission();
      if (!hasPermission) {
        log('ImagePickerService: Camera permission not granted');
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) {
        log('ImagePickerService: No image selected from camera');
        return null;
      }

      File imageFile = File(image.path);
      log('ImagePickerService: Image picked from camera: ${imageFile.path}');

      // Validate image with better error handling
      try {
        final validation = SalonImageService.validateImage(imageFile);
        if (!validation.isValid) {
          Get.snackbar(
            'Invalid Image',
            validation.errorMessage ?? 'Please select a valid image',
            backgroundColor: Colors.red,
            colorText: AppConstants.primaryWhite,
            snackPosition: SnackPosition.TOP,
          );
          return null;
        }
      } catch (e) {
        log('ImagePickerService: Image validation error: $e');
        // Continue with the image if validation fails
      }

      // Crop image if requested with better error handling
      if (allowCropping) {
        try {
          final croppedFile = await _cropImage(imageFile, aspectRatio);
          return croppedFile ?? imageFile;
        } catch (e) {
          log('ImagePickerService: Cropping error: $e');
          // Return original image if cropping fails
          return imageFile;
        }
      }

      return imageFile;
    } catch (e) {
      log('ImagePickerService: Error picking from camera: $e');
      Get.snackbar(
        'Error',
        'Failed to capture image. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.TOP,
      );
      return null;
    }
  }

  /// Pick image from gallery
  static Future<File?> pickFromGallery({
    bool allowCropping = true,
    CropAspectRatio? aspectRatio,
  }) async {
    try {
      log('ImagePickerService: Starting gallery image pick');

      // Check photos permission using PermissionService
      final hasPermission = await PermissionService.requestPhotosPermission();
      if (!hasPermission) {
        log('ImagePickerService: Photos permission not granted');
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
      );

      if (image == null) {
        log('ImagePickerService: No image selected from gallery');
        return null;
      }

      File imageFile = File(image.path);
      log('ImagePickerService: Image picked from gallery: ${imageFile.path}');

      // Validate image with better error handling
      try {
        final validation = SalonImageService.validateImage(imageFile);
        if (!validation.isValid) {
          Get.snackbar(
            'Invalid Image',
            validation.errorMessage ?? 'Please select a valid image',
            backgroundColor: Colors.red,
            colorText: AppConstants.primaryWhite,
            snackPosition: SnackPosition.TOP,
          );
          return null;
        }
      } catch (e) {
        log('ImagePickerService: Image validation error: $e');
        // Continue with the image if validation fails
      }

      // Crop image if requested with better error handling
      if (allowCropping) {
        try {
          final croppedFile = await _cropImage(imageFile, aspectRatio);
          return croppedFile ?? imageFile;
        } catch (e) {
          log('ImagePickerService: Cropping error: $e');
          // Return original image if cropping fails
          return imageFile;
        }
      }

      return imageFile;
    } catch (e) {
      log('ImagePickerService: Error picking from gallery: $e');
      Get.snackbar(
        'Error',
        'Failed to select image. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.TOP,
      );
      return null;
    }
  }

  /// Pick multiple images from gallery
  static Future<List<File>> pickMultipleFromGallery({
    int maxImages = 10,
  }) async {
    try {
      // Check storage permission - let system handle it
      try {
        await Permission.photos.request();
      } catch (e) {
        // Continue anyway, let image picker handle permissions
      }

      final List<XFile> images = await _picker.pickMultiImage(
        imageQuality: 85,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (images.isEmpty) return [];

      // Limit number of images
      final limitedImages = images.take(maxImages).toList();

      List<File> validImages = [];
      for (final image in limitedImages) {
        final imageFile = File(image.path);

        // Validate each image
        final validation = SalonImageService.validateImage(imageFile);
        if (validation.isValid) {
          validImages.add(imageFile);
        } else {
          log(
            'ImagePickerService: Invalid image skipped: ${validation.errorMessage}',
          );
        }
      }

      if (validImages.length != limitedImages.length) {
        Get.snackbar(
          'Some Images Skipped',
          'Some images were skipped due to size or format restrictions',
          backgroundColor: Colors.orange,
          colorText: AppConstants.primaryWhite,
          snackPosition: SnackPosition.TOP,
        );
      }

      return validImages;
    } catch (e) {
      log('ImagePickerService: Error picking multiple images: $e');
      Get.snackbar(
        'Error',
        'Failed to select images. Please try again.',
        backgroundColor: Colors.red,
        colorText: AppConstants.primaryWhite,
        snackPosition: SnackPosition.TOP,
      );
      return [];
    }
  }

  /// Crop image
  static Future<File?> _cropImage(
    File imageFile,
    CropAspectRatio? aspectRatio,
  ) async {
    try {
      log('ImagePickerService: Starting image crop');

      // Check if file exists before cropping
      if (!await imageFile.exists()) {
        log('ImagePickerService: Image file does not exist for cropping');
        return null;
      }

      final croppedFile = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        aspectRatio: aspectRatio,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Crop Image',
            toolbarColor: AppConstants.primaryBlack,
            toolbarWidgetColor: AppConstants.primaryWhite,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: false,
            hideBottomControls: false,
            aspectRatioPresets: [
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9,
            ],
          ),
          IOSUiSettings(
            title: 'Crop Image',
            aspectRatioLockEnabled: false,
            minimumAspectRatio: 0.5,
            aspectRatioPresets: [
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9,
            ],
          ),
        ],
      );

      if (croppedFile != null) {
        log('ImagePickerService: Image cropped successfully');
        return File(croppedFile.path);
      } else {
        log('ImagePickerService: Image cropping cancelled by user');
        return null;
      }
    } catch (e) {
      log('ImagePickerService: Error cropping image: $e');
      // Return null instead of throwing to prevent crashes
      return null;
    }
  }
}

/// Image Picker Bottom Sheet Widget
class _ImagePickerBottomSheet extends StatelessWidget {
  final String title;
  final bool allowCropping;
  final CropAspectRatio? aspectRatio;

  const _ImagePickerBottomSheet({
    required this.title,
    required this.allowCropping,
    this.aspectRatio,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppConstants.primaryWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusMedium),
          topRight: Radius.circular(AppConstants.radiusMedium),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: AppConstants.paddingSmall),
              decoration: BoxDecoration(
                color: AppConstants.mediumGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryBlack,
                ),
              ),
            ),

            // Options
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
              ),
              child: Column(
                children: [
                  // Camera option
                  _buildOption(
                    icon: Icons.camera_alt,
                    title: 'Camera',
                    subtitle: 'Take a new photo',
                    onTap: () async {
                      if (context.mounted) Navigator.pop(context);
                      final file = await ImagePickerService.pickFromCamera(
                        allowCropping: allowCropping,
                        aspectRatio: aspectRatio,
                      );
                      if (context.mounted) Navigator.pop(context, file);
                    },
                  ),

                  const SizedBox(height: AppConstants.paddingSmall),

                  // Gallery option
                  _buildOption(
                    icon: Icons.photo_library,
                    title: 'Gallery',
                    subtitle: 'Choose from gallery',
                    onTap: () async {
                      if (context.mounted) Navigator.pop(context);
                      final file = await ImagePickerService.pickFromGallery(
                        allowCropping: allowCropping,
                        aspectRatio: aspectRatio,
                      );
                      if (context.mounted) Navigator.pop(context, file);
                    },
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          border: Border.all(color: AppConstants.lightGrey),
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppConstants.primaryBlack.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(icon, color: AppConstants.primaryBlack, size: 24),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.primaryBlack,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppConstants.mediumGrey,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppConstants.mediumGrey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
