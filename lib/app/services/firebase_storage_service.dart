import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';
import 'package:image/image.dart' as img;

class FirebaseStorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Helper method to compress and validate image
  Future<Uint8List?> _processImage(File file) async {
    try {
      // Check if file exists
      if (!await file.exists()) {
        log('File does not exist: ${file.path}');
        return null;
      }

      // Check file size (limit to 10MB)
      final fileSize = await file.length();
      log('Original file size: ${fileSize / (1024 * 1024)} MB');

      if (fileSize > 10 * 1024 * 1024) {
        // 10MB limit
        log('File too large: ${fileSize / (1024 * 1024)} MB');
        return null;
      }

      // Read and decode image
      final bytes = await file.readAsBytes();

      final image = img.decodeImage(bytes);
      if (image == null) {
        log('Failed to decode image');
        return null;
      }

      // Resize image if needed (max 1024x1024)
      img.Image resizedImage = image;
      if (image.width > 1024 || image.height > 1024) {
        resizedImage = img.copyResize(
          image,
          width: image.width > image.height ? 1024 : null,
          height: image.height > image.width ? 1024 : null,
        );
      }

      // Compress to JPEG with 85% quality
      final compressedBytes = img.encodeJpg(resizedImage, quality: 85);
      log('Compressed file size: ${compressedBytes.length / (1024 * 1024)} MB');
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      log('Error processing image: $e');
      return null;
    }
  }

  Future<String?> uploadOwnerProfileFile(File file) async {
    try {
      final processedBytes = await _processImage(file);
      if (processedBytes == null) {
        log('Failed to process image for owner profile');
        return null;
      }

      // TODO: Uncomment when Firebase is initialized
      // String fileName = '${const Uuid().v4()}.jpg';
      // final ref = _storage.ref().child('OwnerProfile/$fileName');

      // // Upload processed bytes instead of file
      // UploadTask uploadTask = ref.putData(
      //   processedBytes,
      //   SettableMetadata(
      //     contentType: 'image/jpeg',
      //     customMetadata: {'uploaded': DateTime.now().toIso8601String()},
      //   ),
      // );

      // TaskSnapshot snapshot = await uploadTask;
      // String downloadUrl = await snapshot.ref.getDownloadURL();

      // log('Owner profile uploaded successfully: $downloadUrl');
      // return downloadUrl;

      // For now, return a dummy URL (remove when Firebase is initialized)
      log('Owner profile upload simulated successfully');
      return 'https://example.com/owner-profile-${DateTime.now().millisecondsSinceEpoch}.jpg';
    } catch (e) {
      log('Error uploading owner profile file to Firebase: $e');
      return null;
    }
  }

  Future<String?> uploadUserProfileFile(File file) async {
    try {
      final processedBytes = await _processImage(file);
      if (processedBytes == null) {
        log('Failed to process image for user profile');
        return null;
      }

      // TODO: Uncomment when Firebase is initialized
      // String fileName = '${const Uuid().v4()}.jpg';
      // final ref = _storage.ref().child('UserProfile/$fileName');

      // UploadTask uploadTask = ref.putData(
      //   processedBytes,
      //   SettableMetadata(
      //     contentType: 'image/jpeg',
      //     customMetadata: {'uploaded': DateTime.now().toIso8601String()},
      //   ),
      // );

      // TaskSnapshot snapshot = await uploadTask;
      // String downloadUrl = await snapshot.ref.getDownloadURL();

      // log('User profile uploaded successfully: $downloadUrl');
      // return downloadUrl;

      // For now, return a dummy URL (remove when Firebase is initialized)
      log('User profile upload simulated successfully');
      return 'https://example.com/user-profile-${DateTime.now().millisecondsSinceEpoch}.jpg';
    } catch (e) {
      log('Error uploading user profile file to Firebase: $e');
      return null;
    }
  }

  Future<String?> uploadServiceFile(File file) async {
    try {
      final processedBytes = await _processImage(file);
      if (processedBytes == null) {
        log('Failed to process image for service');
        return null;
      }

      String fileName = '${const Uuid().v4()}.jpg';
      final ref = _storage.ref().child('Service/$fileName');

      UploadTask uploadTask = ref.putData(
        processedBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {'uploaded': DateTime.now().toIso8601String()},
        ),
      );

      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      log('Service image uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      log('Error uploading service file to Firebase: $e');
      return null;
    }
  }

  Future<List<String>> uploadSaloonImages({
    required String userId,
    required List<File> imageFiles,
  }) async {
    List<String> downloadUrls = [];

    try {
      for (int i = 0; i < imageFiles.length; i++) {
        log('Processing saloon image ${i + 1}/${imageFiles.length}');

        final processedBytes = await _processImage(imageFiles[i]);
        if (processedBytes == null) {
          log('Failed to process saloon image ${i + 1}');
          continue; // Skip this image and continue with others
        }

        // TODO: Uncomment when Firebase is initialized
        // String fileName = '${const Uuid().v4()}.jpg';
        // final ref = _storage.ref().child('saloon_photos/$userId/$fileName');

        // UploadTask uploadTask = ref.putData(
        //   processedBytes,
        //   SettableMetadata(
        //     contentType: 'image/jpeg',
        //     customMetadata: {'uploaded': DateTime.now().toIso8601String()},
        //   ),
        // );

        // TaskSnapshot snapshot = await uploadTask;
        // String downloadUrl = await snapshot.ref.getDownloadURL();
        // downloadUrls.add(downloadUrl);

        // For now, add dummy URLs (remove when Firebase is initialized)
        downloadUrls.add(
          'https://example.com/saloon-$userId-${i + 1}-${DateTime.now().millisecondsSinceEpoch}.jpg',
        );
        log('Saloon image ${i + 1} upload simulated successfully');
      }

      return downloadUrls;
    } catch (e) {
      log('Error uploading saloon images: $e');
      return downloadUrls; // Return whatever was uploaded successfully
    }
  }

  // Helper method to check Firebase Storage rules and connection
  Future<bool> testConnection() async {
    try {
      // TODO: Uncomment when Firebase is initialized
      // final ref = _storage.ref().child('test/connection_test.txt');
      // await ref.putString('test', format: PutStringFormat.raw);
      // await ref.delete(); // Clean up test file
      // log('Firebase Storage connection test successful');
      // return true;

      // For now, return true (remove when Firebase is initialized)
      log('Firebase Storage connection test simulated successfully');
      return true;
    } catch (e) {
      log('Firebase Storage connection test failed: $e');
      return false;
    }
  }

  /// Upload service image with progress callback
  Future<String?> uploadServiceImage(
    File file, {
    Function(double)? onProgress,
  }) async {
    try {
      final processedBytes = await _processImage(file);
      if (processedBytes == null) {
        log('Failed to process service image');
        return null;
      }

      String fileName = '${const Uuid().v4()}.jpg';
      final ref = _storage.ref().child('services/$fileName');

      UploadTask uploadTask = ref.putData(
        processedBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {'uploaded': DateTime.now().toIso8601String()},
        ),
      );

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      log('Service image uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      log('Error uploading service image: $e');
      return null;
    }
  }

  /// Upload barber profile image with progress callback
  Future<String?> uploadBarberImage(
    File file, {
    Function(double)? onProgress,
  }) async {
    try {
      final processedBytes = await _processImage(file);
      if (processedBytes == null) {
        log('Failed to process barber image');
        return null;
      }

      String fileName = '${const Uuid().v4()}.jpg';
      final ref = _storage.ref().child('barbers/$fileName');

      UploadTask uploadTask = ref.putData(
        processedBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {'uploaded': DateTime.now().toIso8601String()},
        ),
      );

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      log('Barber image uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      log('Error uploading barber image: $e');
      return null;
    }
  }

  /// Upload multiple salon images with progress callback
  Future<List<String>> uploadSalonImages(
    List<File> files, {
    Function(int, int, double)? onProgress,
  }) async {
    List<String> downloadUrls = [];

    try {
      for (int i = 0; i < files.length; i++) {
        log('Processing salon image ${i + 1}/${files.length}');

        final processedBytes = await _processImage(files[i]);
        if (processedBytes == null) {
          log('Failed to process salon image ${i + 1}');
          continue;
        }

        String fileName = '${const Uuid().v4()}.jpg';
        final ref = _storage.ref().child('salon_gallery/$fileName');

        UploadTask uploadTask = ref.putData(
          processedBytes,
          SettableMetadata(
            contentType: 'image/jpeg',
            customMetadata: {'uploaded': DateTime.now().toIso8601String()},
          ),
        );

        // Listen to upload progress for this image
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          double progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress?.call(i + 1, files.length, progress);
        });

        TaskSnapshot snapshot = await uploadTask;
        String downloadUrl = await snapshot.ref.getDownloadURL();
        downloadUrls.add(downloadUrl);

        log('Salon image ${i + 1} uploaded successfully: $downloadUrl');
      }

      return downloadUrls;
    } catch (e) {
      log('Error uploading salon images: $e');
      return downloadUrls;
    }
  }

  /// Upload salon logo with progress callback
  Future<String?> uploadSalonLogo(
    File file, {
    Function(double)? onProgress,
  }) async {
    try {
      final processedBytes = await _processImage(file);
      if (processedBytes == null) {
        log('Failed to process salon logo');
        return null;
      }

      String fileName = '${const Uuid().v4()}.jpg';
      final ref = _storage.ref().child('salon_logos/$fileName');

      UploadTask uploadTask = ref.putData(
        processedBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {'uploaded': DateTime.now().toIso8601String()},
        ),
      );

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      log('Salon logo uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      log('Error uploading salon logo: $e');
      return null;
    }
  }

  /// Delete image from Firebase Storage
  Future<bool> deleteImage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
      log('Image deleted successfully: $imageUrl');
      return true;
    } catch (e) {
      log('Error deleting image: $e');
      return false;
    }
  }
}
