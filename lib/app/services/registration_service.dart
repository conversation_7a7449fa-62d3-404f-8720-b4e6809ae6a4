import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/user_registration_model.dart';
import '../services/shared_preferences_service.dart';

class RegistrationService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Register user profile
  /// Endpoint: POST /auth/profile/user
  static Future<RegistrationResponse> registerUser(
    UserRegistrationRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        log('RegistrationService: No auth token found for user registration');
        return RegistrationResponse(
          success: false,
          message: 'Authentication token not found. Please login again.',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/auth/profile/user');

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());

      log('RegistrationService: User registration request');
      log('RegistrationService: URL: ${url.toString()}');
      log('RegistrationService: Request body: $body');

      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('RegistrationService: User registration response status: ${response.statusCode}');
      log('RegistrationService: User registration response body: ${response.body}');

      return _handleResponse(response);
    } on SocketException {
      log('RegistrationService: No internet connection during user registration');
      return RegistrationResponse(
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('RegistrationService: Client exception during user registration: $e');
      return RegistrationResponse(
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('RegistrationService: Exception during user registration: $e');
      return RegistrationResponse(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Register owner profile
  /// Endpoint: POST /auth/profile/owner
  static Future<RegistrationResponse> registerOwner(
    OwnerRegistrationRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        log('RegistrationService: No auth token found for owner registration');
        return RegistrationResponse(
          success: false,
          message: 'Authentication token not found. Please login again.',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/auth/profile/owner');

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());

      log('RegistrationService: Owner registration request');
      log('RegistrationService: URL: ${url.toString()}');
      log('RegistrationService: Request body: $body');

      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('RegistrationService: Owner registration response status: ${response.statusCode}');
      log('RegistrationService: Owner registration response body: ${response.body}');

      return _handleResponse(response);
    } on SocketException {
      log('RegistrationService: No internet connection during owner registration');
      return RegistrationResponse(
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('RegistrationService: Client exception during owner registration: $e');
      return RegistrationResponse(
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('RegistrationService: Exception during owner registration: $e');
      return RegistrationResponse(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Handle HTTP response and return RegistrationResponse
  static RegistrationResponse _handleResponse(http.Response response) {
    try {
      final responseData = jsonDecode(response.body);

      switch (response.statusCode) {
        case 200:
        case 201:
          return RegistrationResponse.fromJson(responseData);
        case 400:
          return RegistrationResponse(
            success: false,
            message: responseData['message'] ?? 'Bad request. Please check your input.',
          );
        case 401:
          return RegistrationResponse(
            success: false,
            message: 'Unauthorized. Please login again.',
          );
        case 403:
          return RegistrationResponse(
            success: false,
            message: 'Access forbidden. You don\'t have permission.',
          );
        case 404:
          return RegistrationResponse(
            success: false,
            message: 'Service not found. Please try again later.',
          );
        case 422:
          return RegistrationResponse(
            success: false,
            message: responseData['message'] ?? 'Validation failed. Please check your input.',
          );
        case 500:
          return RegistrationResponse(
            success: false,
            message: 'Server error. Please try again later.',
          );
        default:
          return RegistrationResponse(
            success: false,
            message: 'Something went wrong. Please try again.',
          );
      }
    } catch (e) {
      log('RegistrationService: Response handling error: $e');
      return RegistrationResponse(
        success: false,
        message: 'Failed to process server response.',
      );
    }
  }

  /// Validate registration data before sending to API
  static String? validateUserData(UserRegistrationRequest request) {
    return RegistrationValidator.validateUserRegistration(request);
  }

  /// Validate owner registration data before sending to API
  static String? validateOwnerData(OwnerRegistrationRequest request) {
    return RegistrationValidator.validateOwnerRegistration(request);
  }

  /// Helper method to convert pincode string to int
  static int? parsePincode(String pincodeStr) {
    try {
      final pincode = int.parse(pincodeStr.trim());
      if (pincode >= 100000 && pincode <= 999999) {
        return pincode;
      }
      return null;
    } catch (e) {
      log('RegistrationService: Error parsing pincode: $pincodeStr');
      return null;
    }
  }

  /// Helper method to format phone number
  static String formatPhoneNumber(String phone) {
    // Remove any non-digit characters
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Return the digits only (API expects clean phone number)
    return digitsOnly;
  }

  /// Helper method to validate and format email
  static String formatEmail(String email) {
    return email.trim().toLowerCase();
  }

  /// Helper method to format name fields
  static String formatName(String name) {
    return name.trim();
  }

  /// Helper method to format address fields
  static String formatAddress(String address) {
    return address.trim();
  }
}
