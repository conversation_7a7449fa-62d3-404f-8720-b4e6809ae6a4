import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/barber_models.dart';
import '../services/shared_preferences_service.dart';

class BarberService {
  /// Add a new barber
  static Future<BarberResponse> addBarber(
    BarberRequest request, [
    File? imageFile,
  ]) async {
    try {
      log(
        'BarberService: Adding barber: ${request.firstname} ${request.lastname}',
      );

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      // Try JSON request first (like other working APIs)
      final uri = Uri.parse(ApiEndpoints.addBarber);

      // Use the request's toJson method which includes profileimage
      final requestBody = request.toJson();

      log('BarberService: Request body: ${jsonEncode(requestBody)}');
      log('BarberService: Token: ${token.substring(0, 20)}...');

      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      log('BarberService: Add barber response status: ${response.statusCode}');
      log('BarberService: Add barber response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return BarberResponse.fromString(response.body);
      } else {
        final errorData = jsonDecode(response.body);

        // Extract detailed validation errors
        String detailedMessage = errorData['message'] ?? 'Failed to add barber';
        if (errorData['errors'] != null) {
          final errors = errorData['errors'] as Map<String, dynamic>;
          List<String> errorMessages = [];

          errors.forEach((field, fieldErrors) {
            if (field != '_errors' &&
                fieldErrors is Map &&
                fieldErrors['_errors'] is List) {
              final fieldErrorList = fieldErrors['_errors'] as List;
              for (String error in fieldErrorList) {
                errorMessages.add('$field: $error');
              }
            }
          });

          if (errorMessages.isNotEmpty) {
            detailedMessage = '$detailedMessage\n${errorMessages.join('\n')}';
          }
        }

        throw BarberServiceException(
          detailedMessage,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('BarberService: Add barber error: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Get all barbers for the owner (using new API structure)
  static Future<List<BarberModel>> getAllBarbers() async {
    try {
      log('BarberService: Fetching all barbers');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}/owner/barbers'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log(
        'BarberService: Get all barbers response status: ${response.statusCode}',
      );
      log('BarberService: Get all barbers response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Handle the new response structure with proper null checks
        if (data['success'] == true) {
          // Check if data exists and is not null
          if (data['data'] != null && data['data'] is List) {
            final List<dynamic> barbersData = data['data'] as List<dynamic>;

            // Handle empty list case
            if (barbersData.isEmpty) {
              log('BarberService: No barbers found - empty data array');
              return [];
            }

            // Parse barbers from the data array
            return barbersData
                .map((json) => BarberModel.fromJson(json))
                .toList();
          } else {
            log('BarberService: Data field is null or not a list');
            return [];
          }
        } else {
          log(
            'BarberService: API response indicates failure: ${data['message'] ?? 'Unknown error'}',
          );
          return [];
        }
      } else {
        log('BarberService: HTTP error: ${response.statusCode}');
        log('BarberService: Error response: ${response.body}');
        return [];
      }
    } catch (e) {
      log('BarberService: Error fetching all barbers: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Get single barber by ID
  static Future<BarberModel?> getBarberById(String id) async {
    try {
      log('BarberService: Fetching barber by ID: $id');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}/owner/barber/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log(
        'BarberService: Get barber by ID response status: ${response.statusCode}',
      );
      log('BarberService: Get barber by ID response body: ${response.body}');

      if (response.statusCode == 200) {
        final singleBarberResponse = SingleBarberResponse.fromString(
          response.body,
        );
        log('BarberService: Parsed response: $singleBarberResponse');

        if (singleBarberResponse.services.barber != null) {
          log(
            'BarberService: Successfully found barber: ${singleBarberResponse.services.barber!.name}',
          );
          return singleBarberResponse.services.barber;
        } else {
          log('BarberService: No barber found in response');
        }
      } else {
        log(
          'BarberService: HTTP error ${response.statusCode}: ${response.body}',
        );
      }
      return null;
    } catch (e) {
      log('BarberService: Error fetching barber by ID: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Update barber information (enhanced method)
  static Future<bool> updateBarberEnhanced(
    String id,
    BarberModel barber,
  ) async {
    try {
      log('BarberService: Updating barber with ID: $id');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      log('BarberService: Token: ${token.substring(0, 20)}...');
      log('BarberService: Available status to send: ${barber.available}');
      log('BarberService: First name: ${barber.firstname}');
      log('BarberService: Bio: ${barber.bio}');

      // Create a comprehensive request body with all updatable fields
      final requestBody = {
        'available': barber.available,
        'firstname': barber.firstname,
        'lastname': barber.lastname,
        'bio': barber.bio ?? '',
        'profileimage': barber.profileimage,
        'assignedServices': barber.assignedServices ?? [],
      };

      log('BarberService: Request body: ${jsonEncode(requestBody)}');
      log(
        'BarberService: Request URL: ${ApiEndpoints.baseUrl}/owner/barber/$id',
      );

      final response = await http.put(
        Uri.parse('${ApiEndpoints.baseUrl}/owner/barber/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      log(
        'BarberService: Update barber response status: ${response.statusCode}',
      );
      log('BarberService: Update barber response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return true;
        } else {
          log('BarberService: API returned success: false');
          log(
            'BarberService: Error message: ${responseData['message'] ?? 'No message provided'}',
          );
          return false;
        }
      } else {
        log('BarberService: HTTP error: ${response.statusCode}');
        log('BarberService: Error response: ${response.body}');
        return false;
      }
    } catch (e) {
      log('BarberService: Error updating barber: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Update complete barber details (comprehensive method)
  static Future<bool> updateBarber(
    String id,
    BarberModel barber, {
    String? mobileNo,
  }) async {
    try {
      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      log('BarberService: Updating barber with ID: $id');
      log('BarberService: Token: ${token.substring(0, 20)}...');
      log('BarberService: Available status to send: ${barber.available}');
      log('BarberService: First name: ${barber.firstname}');
      log('BarberService: Last name: ${barber.lastname}');
      log('BarberService: Bio: ${barber.bio}');
      log('BarberService: Mobile: ${mobileNo ?? 'Not provided'}');
      log(
        'BarberService: Assigned services: ${barber.assignedServices ?? 'No services'}',
      );

      // Create request body with new API format
      final requestBody = <String, dynamic>{};

      // Add fields only if they have values (partial updates supported)
      requestBody['available'] = barber.available.toString();
      requestBody['firstname'] = barber.firstname;

      if (barber.lastname.isNotEmpty) {
        requestBody['lastname'] = barber.lastname;
      }

      if (barber.bio != null && barber.bio!.isNotEmpty) {
        requestBody['bio'] = barber.bio;
      }

      if (barber.profileimage != null && barber.profileimage!.isNotEmpty) {
        requestBody['profilephoto'] = barber.profileimage;
      }

      if (mobileNo != null && mobileNo.isNotEmpty) {
        requestBody['mobileNo'] = mobileNo;
      }

      // Send services as array of service IDs with correct key
      if (barber.assignedServices != null &&
          barber.assignedServices!.isNotEmpty) {
        requestBody['services'] = barber.assignedServices;
      } else {
        requestBody['services'] = [];
      }

      log('BarberService: Request body: ${jsonEncode(requestBody)}');
      log(
        'BarberService: Request URL: ${ApiEndpoints.baseUrl}/owner/barber/$id',
      );

      final response = await http.put(
        Uri.parse('${ApiEndpoints.baseUrl}/owner/barber/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      log(
        'BarberService: Update barber response status: ${response.statusCode}',
      );
      log('BarberService: Update barber response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          log('BarberService: Barber updated successfully');
          return true;
        } else {
          log('BarberService: API returned success: false');
          log(
            'BarberService: Error message: ${responseData['message'] ?? 'No message provided'}',
          );
          return false;
        }
      } else {
        log('BarberService: HTTP error: ${response.statusCode}');
        log('BarberService: Error response: ${response.body}');
        return false;
      }
    } catch (e) {
      log('BarberService: Error updating barber: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Update barber availability status only
  static Future<bool> updateBarberAvailability(
    String id,
    bool available,
  ) async {
    try {
      log(
        'BarberService: Updating barber availability - ID: $id, Available: $available',
      );

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      // Convert boolean to string as expected by API
      final requestBody = {'available': available.toString()};

      log(
        'BarberService: Availability update request body: ${jsonEncode(requestBody)}',
      );

      final response = await http.put(
        Uri.parse('${ApiEndpoints.baseUrl}/owner/barber/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      log(
        'BarberService: Update availability response status: ${response.statusCode}',
      );
      log('BarberService: Update availability response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          log('BarberService: Availability updated successfully');
          return true;
        } else {
          log(
            'BarberService: API returned success: false for availability update',
          );
          return false;
        }
      } else {
        log(
          'BarberService: HTTP error for availability update: ${response.statusCode}',
        );
        return false;
      }
    } catch (e) {
      log('BarberService: Error updating barber availability: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Delete barber by ID (enhanced method)
  static Future<bool> deleteBarberEnhanced(String id) async {
    try {
      log('BarberService: Deleting barber with ID: $id');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      log('BarberService: Token: ${token.substring(0, 20)}...');

      final requestBody = {
        'id': id, // Send ID in body
      };

      log('BarberService: Request body: ${jsonEncode(requestBody)}');
      log('BarberService: Request URL: ${ApiEndpoints.baseUrl}/owner/barber');

      final response = await http.delete(
        Uri.parse('${ApiEndpoints.baseUrl}/owner/barber'), // No ID in URL
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody), // Send ID in body
      );

      log(
        'BarberService: Delete barber response status: ${response.statusCode}',
      );
      log('BarberService: Delete barber response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return true;
        } else {
          log('BarberService: API returned success: false');
          return false;
        }
      } else {
        log('BarberService: HTTP error: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      log('BarberService: Error deleting barber: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Get all barbers for the owner (legacy method for backward compatibility)
  static Future<List<BarberData>> getBarbers() async {
    try {
      log('BarberService: Fetching barbers (legacy method)');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.get(
        Uri.parse(ApiEndpoints.getBarbers),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log('BarberService: Get barbers response status: ${response.statusCode}');
      log('BarberService: Get barbers response body: ${response.body}');

      if (response.statusCode == 200) {
        final barbersResponse = BarbersListResponse.fromString(response.body);
        if (barbersResponse.success) {
          return barbersResponse.data;
        } else {
          throw BarberServiceException(barbersResponse.message);
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw BarberServiceException(
          errorData['message'] ?? 'Failed to fetch barbers',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('BarberService: Get barbers error: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Update an existing barber (legacy multipart method)
  static Future<BarberResponse> updateBarberLegacy(
    String barberId,
    BarberRequest request, [
    File? imageFile,
  ]) async {
    try {
      log('BarberService: Updating barber: $barberId');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final uri = Uri.parse('${ApiEndpoints.updateBarber}$barberId');
      final multipartRequest = http.MultipartRequest('PUT', uri);

      // Add headers (Note: Content-Type is automatically set for multipart requests)
      multipartRequest.headers.addAll({'Authorization': 'Bearer $token'});

      // Add form fields
      multipartRequest.fields.addAll({
        'firstname': request.firstname,
        'lastname': request.lastname,
        'phone': request.phone,
        'email': request.email,
        'bio': request.bio,
        'available': request.available.toString(),
        'services': jsonEncode(request.services),
      });

      // Add image file if provided
      if (imageFile != null) {
        final imageStream = http.ByteStream(imageFile.openRead());
        final imageLength = await imageFile.length();
        final multipartFile = http.MultipartFile(
          'profileImage',
          imageStream,
          imageLength,
          filename: 'barber_${DateTime.now().millisecondsSinceEpoch}.jpg',
        );
        multipartRequest.files.add(multipartFile);
      }

      log('BarberService: Sending update request to ${uri.toString()}');
      final streamedResponse = await multipartRequest.send();
      final response = await http.Response.fromStream(streamedResponse);

      log(
        'BarberService: Update barber response status: ${response.statusCode}',
      );
      log('BarberService: Update barber response body: ${response.body}');

      if (response.statusCode == 200) {
        return BarberResponse.fromString(response.body);
      } else {
        final errorData = jsonDecode(response.body);
        throw BarberServiceException(
          errorData['message'] ?? 'Failed to update barber',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('BarberService: Update barber error: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Delete a barber
  static Future<DeleteBarberResponse> deleteBarber(String barberId) async {
    try {
      log('BarberService: Deleting barber: $barberId');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.delete(
        Uri.parse('${ApiEndpoints.deleteBarber}$barberId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log(
        'BarberService: Delete barber response status: ${response.statusCode}',
      );
      log('BarberService: Delete barber response body: ${response.body}');

      if (response.statusCode == 200) {
        return DeleteBarberResponse.fromString(response.body);
      } else {
        final errorData = jsonDecode(response.body);
        throw BarberServiceException(
          errorData['message'] ?? 'Failed to delete barber',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('BarberService: Delete barber error: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }

  /// Toggle barber availability
  static Future<BarberResponse> toggleBarberAvailability(
    String barberId,
    bool available,
  ) async {
    try {
      log(
        'BarberService: Toggling barber availability: $barberId to $available',
      );

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw BarberServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.patch(
        Uri.parse('${ApiEndpoints.updateBarber}$barberId/availability'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'available': available}),
      );

      log(
        'BarberService: Toggle availability response status: ${response.statusCode}',
      );
      log('BarberService: Toggle availability response body: ${response.body}');

      if (response.statusCode == 200) {
        return BarberResponse.fromString(response.body);
      } else {
        final errorData = jsonDecode(response.body);
        throw BarberServiceException(
          errorData['message'] ?? 'Failed to update barber availability',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('BarberService: Toggle availability error: $e');
      if (e is BarberServiceException) {
        rethrow;
      }
      throw BarberServiceException('Network error occurred. Please try again.');
    }
  }
}
