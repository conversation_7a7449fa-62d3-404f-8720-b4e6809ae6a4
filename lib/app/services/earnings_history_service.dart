import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/earnings_history_models.dart';
import '../services/shared_preferences_service.dart';

class EarningsHistoryService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Fetch all withdrawal details
  /// Endpoint: GET /wallet/withdrawal
  static Future<EarningsHistoryResponse> getWithdrawalHistory() async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('EarningsHistoryService: No auth token found');
        return EarningsHistoryResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/withdrawal');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      log('EarningsHistoryService: Fetching withdrawal history');
      log('EarningsHistoryService: Request URL: ${url.toString()}');
      
      final response = await http
          .get(url, headers: headers)
          .timeout(_timeoutDuration);

      log('EarningsHistoryService: Response status: ${response.statusCode}');
      log('EarningsHistoryService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return EarningsHistoryResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('EarningsHistoryService: Unauthorized - token may be expired');
        return EarningsHistoryResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else if (response.statusCode == 404) {
        log('EarningsHistoryService: No withdrawal history found');
        return EarningsHistoryResponse(
          code: 404,
          success: false,
          message: 'No withdrawal history found',
        );
      } else {
        log('EarningsHistoryService: Error ${response.statusCode}: ${response.body}');
        
        try {
          final errorData = jsonDecode(response.body);
          return EarningsHistoryResponse(
            code: response.statusCode,
            success: false,
            message: errorData['message'] ?? 'Failed to fetch withdrawal history',
          );
        } catch (parseError) {
          return EarningsHistoryResponse(
            code: response.statusCode,
            success: false,
            message: 'Failed to fetch withdrawal history. Status: ${response.statusCode}',
          );
        }
      }
    } on SocketException {
      log('EarningsHistoryService: No internet connection');
      return EarningsHistoryResponse(
        code: 0,
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('EarningsHistoryService: Client exception: $e');
      return EarningsHistoryResponse(
        code: 0,
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('EarningsHistoryService: Exception occurred: $e');
      return EarningsHistoryResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Download transaction slip
  /// This would typically be a separate endpoint for downloading PDFs
  static Future<bool> downloadTransactionSlip(String transactionId) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('EarningsHistoryService: No auth token found for download');
        return false;
      }

      // This would be the actual download endpoint
      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/download/slip/$transactionId');
      
      final headers = {
        'Authorization': 'Bearer $authToken',
      };

      log('EarningsHistoryService: Downloading transaction slip for $transactionId');
      
      final response = await http
          .get(url, headers: headers)
          .timeout(_timeoutDuration);

      if (response.statusCode == 200) {
        // Here you would typically save the PDF file to device storage
        // For now, we'll just simulate a successful download
        log('EarningsHistoryService: Transaction slip downloaded successfully');
        return true;
      } else {
        log('EarningsHistoryService: Download failed with status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      log('EarningsHistoryService: Download exception: $e');
      return false;
    }
  }

  /// Download monthly report
  /// This would typically be a separate endpoint for downloading monthly reports
  static Future<bool> downloadMonthlyReport(int year, int month) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('EarningsHistoryService: No auth token found for monthly report');
        return false;
      }

      // This would be the actual download endpoint
      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/download/monthly/$year/$month');
      
      final headers = {
        'Authorization': 'Bearer $authToken',
      };

      log('EarningsHistoryService: Downloading monthly report for $month/$year');
      
      final response = await http
          .get(url, headers: headers)
          .timeout(_timeoutDuration);

      if (response.statusCode == 200) {
        // Here you would typically save the PDF file to device storage
        // For now, we'll just simulate a successful download
        log('EarningsHistoryService: Monthly report downloaded successfully');
        return true;
      } else {
        log('EarningsHistoryService: Monthly report download failed with status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      log('EarningsHistoryService: Monthly report download exception: $e');
      return false;
    }
  }

  /// Helper method to format currency
  static String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(0)}';
  }

  /// Helper method to format percentage
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Helper method to get status color
  static String getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return 'green';
      case TransactionStatus.pending:
        return 'orange';
      case TransactionStatus.rejected:
        return 'red';
      default:
        return 'grey';
    }
  }

  /// Helper method to validate transaction data
  static bool validateTransactionData(WithdrawalTransaction transaction) {
    if (transaction.id.isEmpty) {
      log('EarningsHistoryService: Invalid transaction - missing ID');
      return false;
    }

    if (transaction.withDrawalAmount <= 0) {
      log('EarningsHistoryService: Invalid transaction - invalid amount');
      return false;
    }

    if (transaction.status.isEmpty) {
      log('EarningsHistoryService: Invalid transaction - missing status');
      return false;
    }

    return true;
  }

  /// Helper method to generate mock data for testing
  static EarningsHistoryResponse generateMockData() {
    final now = DateTime.now();
    
    final mockData = EarningsHistoryData(
      total: 9,
      completed: 3,
      pending: 3,
      rejected: 3,
      totalEarning: 9500.0,
      withdrawalData: WithdrawalData(
        pending: [
          WithdrawalTransaction(
            id: 'wd_001',
            withDrawalAmount: 1000.0,
            withdrawCharges: 50.0,
            status: 'pending',
            withDrawalAt: now.subtract(const Duration(days: 1)),
          ),
          WithdrawalTransaction(
            id: 'wd_002',
            withDrawalAmount: 2000.0,
            withdrawCharges: 100.0,
            status: 'pending',
            withDrawalAt: now.subtract(const Duration(days: 2)),
          ),
          WithdrawalTransaction(
            id: 'wd_003',
            withDrawalAmount: 1500.0,
            withdrawCharges: 75.0,
            status: 'pending',
            withDrawalAt: now.subtract(const Duration(days: 3)),
          ),
        ],
        completed: [
          WithdrawalTransaction(
            id: 'wd_004',
            withDrawalAmount: 3000.0,
            withdrawCharges: 150.0,
            status: 'completed',
            withDrawalAt: now.subtract(const Duration(days: 4)),
          ),
          WithdrawalTransaction(
            id: 'wd_005',
            withDrawalAmount: 2500.0,
            withdrawCharges: 125.0,
            status: 'completed',
            withDrawalAt: now.subtract(const Duration(days: 5)),
          ),
          WithdrawalTransaction(
            id: 'wd_006',
            withDrawalAmount: 4000.0,
            withdrawCharges: 200.0,
            status: 'completed',
            withDrawalAt: now.subtract(const Duration(days: 6)),
          ),
        ],
        rejected: [
          WithdrawalTransaction(
            id: 'wd_007',
            withDrawalAmount: 1200.0,
            withdrawCharges: 60.0,
            status: 'rejected',
            withDrawalAt: now.subtract(const Duration(days: 7)),
          ),
          WithdrawalTransaction(
            id: 'wd_008',
            withDrawalAmount: 1800.0,
            withdrawCharges: 90.0,
            status: 'rejected',
            withDrawalAt: now.subtract(const Duration(days: 8)),
          ),
          WithdrawalTransaction(
            id: 'wd_009',
            withDrawalAmount: 1600.0,
            withdrawCharges: 80.0,
            status: 'rejected',
            withDrawalAt: now.subtract(const Duration(days: 9)),
          ),
        ],
      ),
    );

    return EarningsHistoryResponse(
      code: 200,
      success: true,
      message: 'Mock data retrieved successfully',
      data: mockData,
    );
  }
}
