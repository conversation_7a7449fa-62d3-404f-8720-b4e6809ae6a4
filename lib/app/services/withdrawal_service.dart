import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/withdrawal_models.dart';
import '../services/shared_preferences_service.dart';

class WithdrawalService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Submit withdrawal request
  /// Endpoint: POST /wallet/withdrawal
  static Future<WithdrawalResponse> submitWithdrawalRequest(
    WithdrawalRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('WithdrawalService: No auth token found');
        return WithdrawalResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/wallet/withdrawal');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());
      
      log('WithdrawalService: Submitting withdrawal request');
      log('WithdrawalService: Request URL: ${url.toString()}');
      log('WithdrawalService: Request body: $body');
      
      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('WithdrawalService: Response status: ${response.statusCode}');
      log('WithdrawalService: Response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return WithdrawalResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('WithdrawalService: Unauthorized - token may be expired');
        return WithdrawalResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else if (response.statusCode == 400) {
        log('WithdrawalService: Bad request - validation error');
        return WithdrawalResponse(
          code: 400,
          success: false,
          message: jsonData['message'] ?? 'Invalid withdrawal request',
        );
      } else {
        log('WithdrawalService: Error ${response.statusCode}: ${response.body}');
        return WithdrawalResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to submit withdrawal request',
        );
      }
    } on SocketException {
      log('WithdrawalService: No internet connection');
      return WithdrawalResponse(
        code: 0,
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('WithdrawalService: Client exception: $e');
      return WithdrawalResponse(
        code: 0,
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('WithdrawalService: Exception occurred: $e');
      return WithdrawalResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Validate withdrawal request data
  static bool validateWithdrawalRequest(WithdrawalRequest request) {
    // Amount validation
    if (request.amount <= 0) {
      log('WithdrawalService: Invalid amount: ${request.amount}');
      return false;
    }

    // Bank account validation
    if (request.bankAccountNumber.trim().isEmpty) {
      log('WithdrawalService: Empty bank account number');
      return false;
    }

    if (request.bankIfscCode.trim().isEmpty) {
      log('WithdrawalService: Empty IFSC code');
      return false;
    }

    if (request.bankAccountName.trim().isEmpty) {
      log('WithdrawalService: Empty account holder name');
      return false;
    }

    // IFSC code format validation
    if (!isValidIfscCode(request.bankIfscCode)) {
      log('WithdrawalService: Invalid IFSC code format: ${request.bankIfscCode}');
      return false;
    }

    // Bank account number validation
    if (!isValidBankAccountNumber(request.bankAccountNumber)) {
      log('WithdrawalService: Invalid bank account number: ${request.bankAccountNumber}');
      return false;
    }

    return true;
  }

  /// Validate IFSC code format
  static bool isValidIfscCode(String ifscCode) {
    final ifscRegex = RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$');
    return ifscRegex.hasMatch(ifscCode.trim().toUpperCase());
  }

  /// Validate bank account number
  static bool isValidBankAccountNumber(String accountNumber) {
    final accountRegex = RegExp(r'^\d{9,18}$');
    return accountRegex.hasMatch(accountNumber.trim());
  }

  /// Validate UPI ID format
  static bool isValidUpiId(String upiId) {
    if (upiId.trim().isEmpty) return true; // UPI is optional
    final upiRegex = RegExp(r'^[\w\.-]+@[\w\.-]+$');
    return upiRegex.hasMatch(upiId.trim());
  }

  /// Format currency amount
  static String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Get minimum withdrawal amount
  static double getMinimumWithdrawalAmount() {
    return 100.0; // Minimum ₹100
  }

  /// Get maximum withdrawal amount
  static double getMaximumWithdrawalAmount() {
    return 50000.0; // Maximum ₹50,000 per transaction
  }
}
