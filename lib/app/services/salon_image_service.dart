import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/salon_image_models.dart';
import '../services/shared_preferences_service.dart';

class SalonImageService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Upload salon images to backend
  /// Endpoint: POST /owner/image
  static Future<SalonImageUploadResponse> uploadSalonImages(
    List<String> firebaseImageUrls,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('SalonImageService: No auth token found');
        return SalonImageUploadResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/owner/image');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final request = SalonImageUploadRequest(imageIds: firebaseImageUrls);
      final body = jsonEncode(request.toJson());
      
      log('SalonImageService: Uploading salon images');
      log('SalonImageService: Request URL: ${url.toString()}');
      log('SalonImageService: Request body: $body');
      
      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('SalonImageService: Response status: ${response.statusCode}');
      log('SalonImageService: Response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return SalonImageUploadResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('SalonImageService: Unauthorized - token may be expired');
        return SalonImageUploadResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else {
        log('SalonImageService: Error ${response.statusCode}: ${response.body}');
        return SalonImageUploadResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to upload salon images',
        );
      }
    } on SocketException {
      log('SalonImageService: No internet connection');
      return SalonImageUploadResponse(
        code: 0,
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('SalonImageService: Client exception: $e');
      return SalonImageUploadResponse(
        code: 0,
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('SalonImageService: Exception occurred: $e');
      return SalonImageUploadResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Delete salon image from backend
  /// Endpoint: DELETE /owner/image
  static Future<SalonImageDeleteResponse> deleteSalonImage(String imageId) async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      
      if (authToken == null || authToken.isEmpty) {
        log('SalonImageService: No auth token found for delete');
        return SalonImageDeleteResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/owner/image');
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final request = SalonImageDeleteRequest(id: imageId);
      final body = jsonEncode(request.toJson());
      
      log('SalonImageService: Deleting salon image: $imageId');
      log('SalonImageService: Request URL: ${url.toString()}');
      log('SalonImageService: Request body: $body');
      
      final response = await http
          .delete(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('SalonImageService: Delete response status: ${response.statusCode}');
      log('SalonImageService: Delete response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return SalonImageDeleteResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('SalonImageService: Unauthorized during delete');
        return SalonImageDeleteResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else if (response.statusCode == 404) {
        log('SalonImageService: Image not found');
        return SalonImageDeleteResponse(
          code: 404,
          success: false,
          message: 'Image not found',
        );
      } else {
        log('SalonImageService: Delete error ${response.statusCode}: ${response.body}');
        return SalonImageDeleteResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to delete salon image',
        );
      }
    } on SocketException {
      log('SalonImageService: No internet connection during delete');
      return SalonImageDeleteResponse(
        code: 0,
        success: false,
        message: 'No internet connection. Please check your network and try again.',
      );
    } catch (e) {
      log('SalonImageService: Delete exception occurred: $e');
      return SalonImageDeleteResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Validate image file before upload
  static ImageValidationResult validateImage(File imageFile) {
    try {
      // Check if file exists
      if (!imageFile.existsSync()) {
        return ImageValidationResult.invalid('Image file does not exist');
      }

      // Check file size
      final fileSizeBytes = imageFile.lengthSync();
      const maxSizeBytes = 10 * 1024 * 1024; // 10MB
      
      if (fileSizeBytes > maxSizeBytes) {
        final sizeMB = fileSizeBytes / (1024 * 1024);
        return ImageValidationResult.invalid(
          'Image size (${sizeMB.toStringAsFixed(1)}MB) exceeds 10MB limit',
        );
      }

      // Check file extension
      final extension = imageFile.path.split('.').last.toLowerCase();
      const allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
      
      if (!allowedExtensions.contains(extension)) {
        return ImageValidationResult.invalid(
          'Unsupported image format. Please use JPG, PNG, or WebP',
        );
      }

      return ImageValidationResult.valid(
        fileSizeBytes: fileSizeBytes,
        mimeType: 'image/$extension',
      );
    } catch (e) {
      log('SalonImageService: Error validating image: $e');
      return ImageValidationResult.invalid('Error validating image file');
    }
  }

  /// Get formatted file size string
  static String getFormattedFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Check if image URL is valid Firebase Storage URL
  static bool isValidFirebaseUrl(String url) {
    return url.contains('firebasestorage.googleapis.com') ||
           url.contains('firebase') ||
           url.startsWith('https://');
  }

  /// Extract filename from Firebase URL
  static String getFilenameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        return pathSegments.last.split('?').first;
      }
      return 'image.jpg';
    } catch (e) {
      return 'image.jpg';
    }
  }

  /// Generate thumbnail URL from Firebase URL (if supported)
  static String getThumbnailUrl(String originalUrl, {int size = 200}) {
    // For now, return original URL
    // In production, you might want to generate thumbnails
    return originalUrl;
  }
}
