import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/search_salon_models.dart';
import 'shared_preferences_service.dart';

class SearchService {
  static const String searchEndpoint = '/saloon/search';
  static const bool _debugMode = true; // Set to false for production

  /// Search salons with specified query and type
  static Future<SearchSalonResponse> searchSalons({
    required String searchQuery,
    String type = 'saloon',
  }) async {
    try {
      // Get auth token
      final token = await SharedPreferencesService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Prepare request
      final url = Uri.parse('${ApiEndpoints.baseUrl}$searchEndpoint');
      final requestBody = SearchRequest(type: type, value: searchQuery);

      // Debug logging
      if (_debugMode) {
        log('🔍 Search API Debug:');
        log('URL: $url');
        log('Request Body: ${jsonEncode(requestBody.toJson())}');
        log('Token: ${token.substring(0, 20)}...');
      }

      // Make API call
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody.toJson()),
      );

      // Debug response
      if (_debugMode) {
        log('Response Status: ${response.statusCode}');
        log('Response Body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (_debugMode) {
          log('🔄 Parsing JSON response...');
          log('JSON Keys: ${jsonData.keys.toList()}');
          log('Data type: ${jsonData['data'].runtimeType}');
          log('Data length: ${jsonData['data']?.length ?? 0}');
        }

        final searchResponse = SearchSalonResponse.fromJson(jsonData);

        if (_debugMode) {
          log('✅ Parsed ${searchResponse.data.length} salons successfully');
        }

        return searchResponse;
      } else {
        // Handle error response
        if (_debugMode) {
          log('❌ API Error: ${response.statusCode}');
          log('Error Body: ${response.body}');
        }

        final Map<String, dynamic> errorData = jsonDecode(response.body);
        return SearchSalonResponse(
          code: response.statusCode,
          success: false,
          message: errorData['message'] ?? 'Search failed',
          data: [],
        );
      }
    } catch (e) {
      log('❌ Search Service Error: $e');
      return SearchSalonResponse(
        code: 500,
        success: false,
        message: 'Failed to search: ${e.toString()}',
        data: [],
      );
    }
  }

  /// Search with different types
  static Future<SearchSalonResponse> searchByType({
    required String searchQuery,
    required String searchType,
  }) async {
    return await searchSalons(searchQuery: searchQuery, type: searchType);
  }

  /// Search salons specifically
  static Future<SearchSalonResponse> searchSalonsOnly(
    String searchQuery,
  ) async {
    return await searchSalons(searchQuery: searchQuery, type: 'saloon');
  }

  /// Search services specifically
  static Future<SearchSalonResponse> searchServices(String searchQuery) async {
    return await searchSalons(searchQuery: searchQuery, type: 'service');
  }

  /// Search barbers specifically
  static Future<SearchSalonResponse> searchBarbers(String searchQuery) async {
    return await searchSalons(searchQuery: searchQuery, type: 'barber');
  }

  /// Get popular search suggestions
  static List<SearchSuggestion> getPopularSuggestions() {
    return [
      SearchSuggestion(
        text: 'Hair Cut',
        type: SearchFilterType.service,
        icon: Icons.content_cut,
      ),
      SearchSuggestion(
        text: 'Beard Trim',
        type: SearchFilterType.service,
        icon: Icons.face_retouching_natural,
      ),
      SearchSuggestion(
        text: 'Hair Wash',
        type: SearchFilterType.service,
        icon: Icons.local_car_wash,
      ),
      SearchSuggestion(
        text: 'Facial',
        type: SearchFilterType.service,
        icon: Icons.face,
      ),
      SearchSuggestion(
        text: 'Massage',
        type: SearchFilterType.service,
        icon: Icons.spa,
      ),
      SearchSuggestion(
        text: 'Hair Color',
        type: SearchFilterType.service,
        icon: Icons.palette,
      ),
    ];
  }

  /// Get trending searches
  static List<String> getTrendingSearches() {
    return [
      'Best salon near me',
      'Hair cut and styling',
      'Beard grooming',
      'Bridal makeup',
      'Hair spa treatment',
      'Professional barber',
    ];
  }
}
