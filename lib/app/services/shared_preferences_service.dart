import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  static SharedPreferences? _prefs;

  // Initialize SharedPreferences
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Get SharedPreferences instance
  static Future<SharedPreferences> get _instance async {
    if (_prefs != null) return _prefs!;
    _prefs = await SharedPreferences.getInstance();
    return _prefs!;
  }

  static const String _tokenKey = 'auth_token';
  static const String _roleKey = 'user_role';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userIdKey = 'user_id';
  static const String _userDataKey = 'user_data';
  static const String _emailKey = 'user_email';
  static const String _phoneKey = 'user_phone';
  static const String _userNameKey = 'user_name';
  static const String _userLocationKey = 'user_location';
  static const String _isVerifiedKey = 'is_verified';
  static const String _isProfileCompletedKey = 'is_profile_completed';

  // FCM and Device Info Keys
  static const String _fcmTokenKey = 'fcm_token';
  static const String _deviceTokenKey = 'device_token';
  static const String _deviceIdKey = 'device_id';

  // Save authentication token
  static Future<bool> saveToken(String token) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_tokenKey, token);
    } catch (e) {
      return false;
    }
  }

  // Get authentication token
  static Future<String?> getToken() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_tokenKey);
    } catch (e) {
      return null;
    }
  }

  // Save user role
  static Future<bool> saveRole(String role) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_roleKey, role);
    } catch (e) {
      return false;
    }
  }

  // Get user role
  static Future<String?> getRole() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_roleKey);
    } catch (e) {
      return null;
    }
  }

  // Save user ID
  static Future<bool> saveUserId(String userId) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_userIdKey, userId);
    } catch (e) {
      return false;
    }
  }

  // Get user ID
  static Future<String?> getUserId() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_userIdKey);
    } catch (e) {
      return null;
    }
  }

  // Save user email
  static Future<bool> saveEmail(String email) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_emailKey, email);
    } catch (e) {
      return false;
    }
  }

  // Get user email
  static Future<String?> getEmail() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_emailKey);
    } catch (e) {
      return null;
    }
  }

  // Save user phone
  static Future<bool> savePhone(String phone) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_phoneKey, phone);
    } catch (e) {
      return false;
    }
  }

  // Get user phone
  static Future<String?> getPhone() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_phoneKey);
    } catch (e) {
      return null;
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Save user name
  static Future<bool> saveUserName(String name) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_userNameKey, name);
    } catch (e) {
      return false;
    }
  }

  // Get user name
  static Future<String?> getUserName() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_userNameKey);
    } catch (e) {
      return null;
    }
  }

  // Save user location
  static Future<bool> saveUserLocation(String location) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_userLocationKey, location);
    } catch (e) {
      return false;
    }
  }

  // Get user location
  static Future<String?> getUserLocation() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_userLocationKey);
    } catch (e) {
      return null;
    }
  }

  // Save verification status
  static Future<bool> saveVerificationStatus(bool isVerified) async {
    try {
      final prefs = await _instance;
      return await prefs.setBool(_isVerifiedKey, isVerified);
    } catch (e) {
      return false;
    }
  }

  // Get verification status
  static Future<bool> getVerificationStatus() async {
    try {
      final prefs = await _instance;
      return prefs.getBool(_isVerifiedKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  // Save profile completion status
  static Future<bool> saveProfileCompletionStatus(bool isCompleted) async {
    try {
      final prefs = await _instance;
      return await prefs.setBool(_isProfileCompletedKey, isCompleted);
    } catch (e) {
      return false;
    }
  }

  // Get profile completion status
  static Future<bool> getProfileCompletionStatus() async {
    try {
      final prefs = await _instance;
      return prefs.getBool(_isProfileCompletedKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  // Clear all user data (logout)
  static Future<bool> clearUserData() async {
    try {
      final prefs = await _instance;
      await prefs.remove(_tokenKey);
      await prefs.remove(_roleKey);
      await prefs.remove(_userIdKey);
      await prefs.remove(_emailKey);
      await prefs.remove(_phoneKey);
      await prefs.remove(_userNameKey);
      await prefs.remove(_userLocationKey);
      await prefs.remove(_isVerifiedKey);
      await prefs.remove(_isProfileCompletedKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Generic string storage methods
  static Future<bool> setString(String key, String value) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(key, value);
    } catch (e) {
      return false;
    }
  }

  static Future<String?> getString(String key) async {
    try {
      final prefs = await _instance;
      return prefs.getString(key);
    } catch (e) {
      return null;
    }
  }

  // ============ FCM and Device Info Methods ============

  /// Save FCM token
  static Future<bool> saveFcmToken(String fcmToken) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_fcmTokenKey, fcmToken);
    } catch (e) {
      return false;
    }
  }

  /// Get FCM token
  static Future<String?> getFcmToken() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_fcmTokenKey);
    } catch (e) {
      return null;
    }
  }

  /// Save device token (for salon booking app, this is the same as FCM token)
  static Future<bool> saveDeviceToken(String deviceToken) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_deviceTokenKey, deviceToken);
    } catch (e) {
      return false;
    }
  }

  /// Get device token
  static Future<String?> getDeviceToken() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_deviceTokenKey);
    } catch (e) {
      return null;
    }
  }

  /// Save device ID
  static Future<bool> saveDeviceId(String deviceId) async {
    try {
      final prefs = await _instance;
      return await prefs.setString(_deviceIdKey, deviceId);
    } catch (e) {
      return false;
    }
  }

  /// Get device ID
  static Future<String?> getDeviceId() async {
    try {
      final prefs = await _instance;
      return prefs.getString(_deviceIdKey);
    } catch (e) {
      return null;
    }
  }

  /// Clear FCM and device info (useful for logout)
  static Future<bool> clearFcmAndDeviceInfo() async {
    try {
      final prefs = await _instance;
      await prefs.remove(_fcmTokenKey);
      await prefs.remove(_deviceTokenKey);
      await prefs.remove(_deviceIdKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get all device info as a map (useful for API calls)
  static Future<Map<String, String?>> getDeviceInfo() async {
    try {
      final fcmToken = await getFcmToken();
      final deviceToken = await getDeviceToken();
      final deviceId = await getDeviceId();

      return {
        'fcmToken': fcmToken,
        'deviceToken':
            deviceToken ?? fcmToken, // Use FCM token as device token if not set
        'deviceId': deviceId,
      };
    } catch (e) {
      return {'fcmToken': null, 'deviceToken': null, 'deviceId': null};
    }
  }
}
