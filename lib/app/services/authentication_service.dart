import 'dart:developer';
import 'package:get/get.dart';
import '../routes/app_routes.dart';
import 'shared_preferences_service.dart';

enum AuthenticationStatus { unknown, authenticated, unauthenticated, expired }

class AuthenticationService extends GetxService {
  static AuthenticationService get instance =>
      Get.find<AuthenticationService>();

  final Rx<AuthenticationStatus> _status = AuthenticationStatus.unknown.obs;
  final RxString _userRole = ''.obs;
  final RxString _userId = ''.obs;
  final RxString _userToken = ''.obs;
  final RxBool _isInitialized = false.obs;

  // Getters
  AuthenticationStatus get status => _status.value;
  String get userRole => _userRole.value;
  String get userId => _userId.value;
  String get userToken => _userToken.value;
  bool get isInitialized => _isInitialized.value;
  bool get isAuthenticated =>
      _status.value == AuthenticationStatus.authenticated;
  bool get isUser => _userRole.value == 'user';
  bool get isOwner => _userRole.value == 'owner';

  /// Initialize and check authentication status
  Future<void> initialize() async {
    await checkAuthenticationStatus();
  }

  /// Check authentication status on app startup
  Future<void> checkAuthenticationStatus() async {
    try {
      log('Checking authentication status...');

      // Get stored authentication data
      final token = await SharedPreferencesService.getToken();
      final role = await SharedPreferencesService.getRole();
      final userId = await SharedPreferencesService.getUserId();

      if (token != null &&
          token.isNotEmpty &&
          role != null &&
          role.isNotEmpty) {
        // Validate token (you can add API call here to verify token)
        final isValidToken = await _validateToken(token);

        if (isValidToken) {
          // Set authenticated state
          _userToken.value = token;
          _userRole.value = role;
          _userId.value = userId ?? '';
          _status.value = AuthenticationStatus.authenticated;

          log('User is authenticated with role: $role');
        } else {
          // Token is invalid or expired
          await _handleInvalidToken();
        }
      } else {
        // No authentication data found
        _status.value = AuthenticationStatus.unauthenticated;
        log('No authentication data found');
      }
    } catch (e) {
      log('Error checking authentication status: $e');
      _status.value = AuthenticationStatus.unauthenticated;
    } finally {
      _isInitialized.value = true;
    }
  }

  /// Validate token with backend (placeholder for now)
  Future<bool> _validateToken(String token) async {
    try {
      // TODO: Implement actual token validation with your API
      // For now, we'll assume the token is valid if it exists
      // In a real app, you would make an API call here

      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Basic validation - check if token is not empty and has reasonable length
      if (token.length < 10) {
        return false;
      }

      // TODO: Replace with actual API validation
      // Example:
      // final response = await http.get(
      //   Uri.parse('${ApiEndpoints.baseUrl}/auth/validate'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // return response.statusCode == 200;

      return true; // Placeholder - assume valid for now
    } catch (e) {
      log('Token validation error: $e');
      return false;
    }
  }

  /// Handle invalid or expired token
  Future<void> _handleInvalidToken() async {
    log('Token is invalid or expired, clearing authentication data');
    await logout();
    _status.value = AuthenticationStatus.expired;
  }

  /// Set user as authenticated
  Future<void> setAuthenticated({
    required String token,
    required String role,
    String? userId,
    Map<String, dynamic>? userData,
  }) async {
    try {
      // Save to SharedPreferences
      await SharedPreferencesService.saveToken(token);
      await SharedPreferencesService.saveRole(role);

      if (userId != null) {
        await SharedPreferencesService.saveUserId(userId);
      }

      // Update reactive variables
      _userToken.value = token;
      _userRole.value = role;
      _userId.value = userId ?? '';
      _status.value = AuthenticationStatus.authenticated;

      log('User authenticated successfully with role: $role');
    } catch (e) {
      log('Error setting authentication: $e');
      throw Exception('Failed to save authentication data');
    }
  }

  /// Logout user and clear all data
  Future<void> logout() async {
    try {
      log('Logging out user...');

      // Clear SharedPreferences
      await SharedPreferencesService.clearUserData();

      // Clear reactive variables
      _userToken.value = '';
      _userRole.value = '';
      _userId.value = '';
      _status.value = AuthenticationStatus.unauthenticated;

      log('User logged out successfully');
    } catch (e) {
      log('Error during logout: $e');
      // Even if there's an error, set status to unauthenticated
      _status.value = AuthenticationStatus.unauthenticated;
    }
  }

  /// Navigate to appropriate screen based on authentication status
  void navigateBasedOnAuthStatus() {
    if (!_isInitialized.value) {
      log('Authentication service not initialized yet');
      return;
    }

    switch (_status.value) {
      case AuthenticationStatus.authenticated:
        _navigateToMainApp();
        break;
      case AuthenticationStatus.unauthenticated:
      case AuthenticationStatus.expired:
        _navigateToLogin();
        break;
      case AuthenticationStatus.unknown:
        // Stay on splash screen
        break;
    }
  }

  /// Navigate to main app based on user role
  void _navigateToMainApp() {
    if (_userRole.value == 'owner') {
      // TODO: Navigate to owner bottom navigation when implemented
      Get.offAllNamed(AppRoutes.userBottomNavigation); // Placeholder for now
      log('Navigating to owner dashboard');
    } else {
      Get.offAllNamed(AppRoutes.userBottomNavigation);
      log('Navigating to user dashboard');
    }
  }

  /// Navigate to login screen
  void _navigateToLogin() {
    Get.offAllNamed(AppRoutes.socialLogin);
    log('Navigating to login screen');
  }

  /// Force refresh authentication status
  Future<void> refreshAuthStatus() async {
    _isInitialized.value = false;
    await checkAuthenticationStatus();
  }

  /// Check if user has specific role
  bool hasRole(String role) {
    return isAuthenticated && _userRole.value == role;
  }

  /// Get user display name (placeholder)
  String getUserDisplayName() {
    // TODO: Implement based on your user data structure
    return 'User'; // Placeholder
  }
}
