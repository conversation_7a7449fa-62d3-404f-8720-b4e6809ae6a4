import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/appoinment_model.dart';
import 'shared_preferences_service.dart';

class AppointmentService {
  // Get appointments by status
  Future<AppoinmentOwnerSideModel?> getAppointments() async {
    final token = await SharedPreferencesService.getToken();
    try {
      final response = await http.get(
        Uri.parse(ApiEndpoints.appointmentPending),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // Debug print to see the actual response structure
        print('API Response: $jsonData');

        return AppoinmentOwnerSideModel.fromJson(jsonData);
      } else {
        print('Failed to load appointments: ${response.statusCode}');
        print('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching appointments: $e');
      return null;
    }
  }

  // Accept or reject appointment
  Future<bool> makeAppointmentDecision({
    required String appointmentId,
    required String decision, // "Accepted" or "Rejected"
  }) async {
    final token = await SharedPreferencesService.getToken();
    try {
      final body = {"appointmentId": appointmentId, "decision": decision};

      final response = await http.post(
        Uri.parse(ApiEndpoints.appointmentDecision),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        print('Failed to make decision: ${response.statusCode}');
        print('Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error making appointment decision: $e');
      return false;
    }
  }

  // Start service with OTP
  Future<bool> startService({
    required String appointmentId,
    required String otp,
  }) async {
    final token = await SharedPreferencesService.getToken();
    try {
      final body = {"appointmentId": appointmentId, "otp": otp};

      final response = await http.post(
        Uri.parse(ApiEndpoints.startService),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        print('Failed to start service: ${response.statusCode}');
        print('Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error starting service: $e');
      return false;
    }
  }

  // Complete service
  Future<bool> completeService({required String appointmentId}) async {
    final token = await SharedPreferencesService.getToken();
    try {
      final body = {"appointmentId": appointmentId};

      final response = await http.post(
        Uri.parse(ApiEndpoints.completeService),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        print('Failed to complete service: ${response.statusCode}');
        print('Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error completing service: $e');
      return false;
    }
  }

  Future<AppoinmentOwnerSideModel?> getCompletedAppointments() async {
    final token = await SharedPreferencesService.getToken();
    try {
      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}/appointments/salon/completed'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // Debug print to see the actual response structure
        print('Completed Appointments API Response: $jsonData');

        return AppoinmentOwnerSideModel.fromJson(jsonData);
      } else {
        print('Failed to load completed appointments: ${response.statusCode}');
        print('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching completed appointments: $e');
      return null;
    }
  }
}
