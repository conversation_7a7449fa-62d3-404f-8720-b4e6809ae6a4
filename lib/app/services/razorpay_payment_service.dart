import 'dart:developer';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../config/razorpay_config.dart';
import '../models/appointment_booking_models.dart';

/// Service for handling Razorpay payment integration
/// Following official Razorpay Flutter documentation
class RazorpayPaymentService {
  static RazorpayPaymentService? _instance;
  Razorpay? _razorpay;
  bool _isInitialized = false;

  // Callback functions
  Function(RazorpayPaymentDetails)? onPaymentSuccess;
  Function(String)? onPaymentError;
  Function(String)? onExternalWallet;

  // Singleton pattern
  static RazorpayPaymentService get instance {
    _instance ??= RazorpayPaymentService._internal();
    return _instance!;
  }

  RazorpayPaymentService._internal();

  /// Initialize Razorpay instance (lazy initialization)
  Future<bool> initialize() async {
    if (_isInitialized && _razorpay != null) {
      return true;
    }

    try {
      log('RazorpayPaymentService: Initializing Razorpay...');

      _razorpay = Razorpay();

      // Set up event listeners
      _razorpay!.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
      _razorpay!.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
      _razorpay!.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);

      _isInitialized = true;
      log('RazorpayPaymentService: Successfully initialized');
      return true;
    } catch (e) {
      log('RazorpayPaymentService: Failed to initialize: $e');
      _isInitialized = false;
      _razorpay = null;
      return false;
    }
  }

  /// Open Razorpay payment gateway
  Future<void> openPaymentGateway({
    required AppointmentBookingData bookingData,
    String? userEmail,
    String? userPhone,
    String? userName,
  }) async {
    try {
      if (!RazorpayConfig.isConfigValid) {
        throw Exception('Razorpay configuration is invalid');
      }

      if (!bookingData.requiresPayment) {
        throw Exception('No payment required for this booking');
      }

      final options = RazorpayConfig.getPaymentOptions(
        orderId: bookingData.razorpayOrderId,
        amount: bookingData.amount,
        appointmentId: bookingData.appointmentId,
        userEmail: userEmail,
        userPhone: userPhone,
        userName: userName,
      );

      log('RazorpayPaymentService: Opening payment gateway');
      log('RazorpayPaymentService: Amount: ₹${bookingData.amount}');
      log('RazorpayPaymentService: Order ID: ${bookingData.razorpayOrderId}');
      log(
        'RazorpayPaymentService: Appointment ID: ${bookingData.appointmentId}',
      );

      _razorpay!.open(options);
    } catch (e) {
      log('RazorpayPaymentService: Error opening payment gateway: $e');
      onPaymentError?.call('Failed to open payment gateway: $e');
    }
  }

  /// Handle payment success
  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    log('RazorpayPaymentService: Payment successful');
    log('RazorpayPaymentService: Payment ID: ${response.paymentId}');
    log('RazorpayPaymentService: Order ID: ${response.orderId}');
    log('RazorpayPaymentService: Signature: ${response.signature}');

    // Validate payment response
    final responseMap = {
      'razorpay_payment_id': response.paymentId,
      'razorpay_order_id': response.orderId,
      'razorpay_signature': response.signature,
    };

    if (!RazorpayConfig.validatePaymentResponse(responseMap)) {
      log('RazorpayPaymentService: Invalid payment response');
      onPaymentError?.call('Invalid payment response received');
      return;
    }

    final paymentDetails = RazorpayPaymentDetails(
      paymentId: response.paymentId ?? '',
      orderId: response.orderId ?? '',
      signature: response.signature ?? '',
    );

    if (paymentDetails.isValid) {
      onPaymentSuccess?.call(paymentDetails);
    } else {
      log('RazorpayPaymentService: Invalid payment details');
      onPaymentError?.call('Invalid payment details received');
    }
  }

  /// Handle payment error
  void _handlePaymentError(PaymentFailureResponse response) {
    log('RazorpayPaymentService: Payment failed');
    log('RazorpayPaymentService: Error code: ${response.code}');
    log('RazorpayPaymentService: Error message: ${response.message}');

    String errorMessage = 'Payment failed';

    // Handle specific error codes
    switch (response.code) {
      case Razorpay.PAYMENT_CANCELLED:
        errorMessage = 'Payment was cancelled by user';
        break;
      case Razorpay.NETWORK_ERROR:
        errorMessage =
            'Network error occurred. Please check your internet connection';
        break;
      case Razorpay.INVALID_OPTIONS:
        errorMessage = 'Invalid payment options. Please try again';
        break;
      // Note: PAYMENT_EXTERNAL_WALLET_NOT_SUPPORTED might not be available in all versions
      // case Razorpay.PAYMENT_EXTERNAL_WALLET_NOT_SUPPORTED:
      //   errorMessage = 'External wallet not supported';
      //   break;
      case Razorpay.TLS_ERROR:
        errorMessage = 'TLS error occurred. Please update your app';
        break;
      case Razorpay.INCOMPATIBLE_PLUGIN:
        errorMessage = 'Incompatible plugin version';
        break;
      case Razorpay.UNKNOWN_ERROR:
      default:
        errorMessage = response.message ?? 'Unknown payment error occurred';
        break;
    }

    onPaymentError?.call(errorMessage);
  }

  /// Handle external wallet
  void _handleExternalWallet(ExternalWalletResponse response) {
    log(
      'RazorpayPaymentService: External wallet selected: ${response.walletName}',
    );
    onExternalWallet?.call(response.walletName ?? 'Unknown wallet');
  }

  /// Set payment success callback
  void setOnPaymentSuccess(Function(RazorpayPaymentDetails) callback) {
    onPaymentSuccess = callback;
  }

  /// Set payment error callback
  void setOnPaymentError(Function(String) callback) {
    onPaymentError = callback;
  }

  /// Set external wallet callback
  void setOnExternalWallet(Function(String) callback) {
    onExternalWallet = callback;
  }

  /// Validate payment amount
  static bool validatePaymentAmount(double amount) {
    // Minimum amount is ₹1 (100 paise)
    return amount >= 1.0;
  }

  /// Format amount for Razorpay (convert to paise)
  static int formatAmountForRazorpay(double amount) {
    return RazorpayConfig.rupeesToPaise(amount);
  }

  /// Format amount for display
  static String formatAmountForDisplay(double amount) {
    return RazorpayConfig.formatAmount(amount);
  }

  /// Check if Razorpay is properly configured
  static bool isConfigured() {
    return RazorpayConfig.isConfigValid;
  }

  /// Get Razorpay key ID
  static String getKeyId() {
    return RazorpayConfig.keyId;
  }

  /// Dispose Razorpay instance
  void dispose() {
    try {
      _razorpay?.clear();
      _razorpay = null;
      _isInitialized = false;
      log('RazorpayPaymentService: Disposed');
    } catch (e) {
      log('RazorpayPaymentService: Error disposing: $e');
    }
  }
}
