import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/dashboard_models.dart';
import '../models/owner_dashboard_models.dart';
import '../models/chart_data_models.dart';
import '../services/shared_preferences_service.dart';

/// Service class for handling dashboard-related API calls
class DashboardService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Fetches user dashboard data including nearest salons, popular salons, and home salon
  /// Returns [DashboardResponse] on success or throws [DashboardServiceException] on error
  /// [latitude] and [longitude] are optional parameters for location-based filtering
  static Future<DashboardResponse> getUserDashboard({
    double? latitude,
    double? longitude,
  }) async {
    try {
      final bool hasLocation = latitude != null && longitude != null;
      log(
        'DashboardService: Fetching user dashboard data${hasLocation ? ' with location (lat: $latitude, lng: $longitude)' : ' without location'}...',
      );

      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw DashboardServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      // Build URL with query parameters if location is provided
      String url = ApiEndpoints.userDashboardUrl;
      if (hasLocation) {
        url += '?latitude=$latitude&longitude=$longitude';
      }

      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log('DashboardService: Response status: ${response.statusCode}');
      log('DashboardService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final dashboardResponse = DashboardResponse.fromString(response.body);

          log(
            'DashboardService: Parsed response - success: ${dashboardResponse.success}',
          );
          log(
            'DashboardService: Parsed response - message: ${dashboardResponse.message}',
          );

          if (dashboardResponse.data != null) {
            log(
              'DashboardService: Nearest salons count: ${dashboardResponse.data!.nearestSalons.length}',
            );
            log(
              'DashboardService: Popular salons count: ${dashboardResponse.data!.popularSalons.length}',
            );
            log(
              'DashboardService: Home salon: ${dashboardResponse.data!.homeSaloon?.name ?? 'null'}',
            );
          }

          if (dashboardResponse.success) {
            log('DashboardService: Dashboard data fetched successfully');
            return dashboardResponse;
          } else {
            throw DashboardServiceException(
              dashboardResponse.message.isNotEmpty
                  ? dashboardResponse.message
                  : 'API returned success: false',
              statusCode: response.statusCode,
            );
          }
        } catch (e) {
          log('DashboardService: Error parsing response: $e');
          throw DashboardServiceException(
            'Failed to parse dashboard response: $e',
            statusCode: response.statusCode,
            error: e.toString(),
          );
        }
      } else {
        // Handle different HTTP status codes
        final errorMessage = _getErrorMessageFromStatusCode(
          response.statusCode,
        );
        final errorResponse = _parseErrorResponse(response.body);

        throw DashboardServiceException(
          errorResponse?.message ?? errorMessage,
          statusCode: response.statusCode,
          error: errorResponse?.error,
        );
      }
    } on SocketException {
      log('DashboardService: No internet connection');
      throw DashboardServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } on http.ClientException catch (e) {
      log('DashboardService: Client exception: $e');
      throw DashboardServiceException(
        'Failed to connect to server. Please try again later.',
        statusCode: 0,
        error: e.toString(),
      );
    } on FormatException catch (e) {
      log('DashboardService: Format exception: $e');
      throw DashboardServiceException(
        'Invalid response format from server.',
        statusCode: 0,
        error: e.toString(),
      );
    } catch (e) {
      log('DashboardService: Unexpected error: $e');
      throw DashboardServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Refreshes dashboard data with optional force refresh and location parameters
  static Future<DashboardResponse> refreshDashboard({
    bool forceRefresh = false,
    double? latitude,
    double? longitude,
  }) async {
    log('DashboardService: Refreshing dashboard data (force: $forceRefresh)');

    // Add any caching logic here if needed
    return await getUserDashboard(latitude: latitude, longitude: longitude);
  }

  /// Gets error message based on HTTP status code
  static String _getErrorMessageFromStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your data and try again.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission to access this resource.';
      case 404:
        return 'Dashboard data not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Server is temporarily unavailable.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again.';
      default:
        return 'An error occurred (Status: $statusCode). Please try again.';
    }
  }

  /// Parses error response from API
  static DashboardErrorResponse? _parseErrorResponse(String responseBody) {
    try {
      if (responseBody.isNotEmpty) {
        return DashboardErrorResponse.fromString(responseBody);
      }
    } catch (e) {
      log('DashboardService: Failed to parse error response: $e');
    }
    return null;
  }

  /// Validates dashboard response data
  static bool validateDashboardData(DashboardData? data) {
    if (data == null) return false;

    // Basic validation - at least one of the lists should be present
    return data.nearestSalons.isNotEmpty ||
        data.popularSalons.isNotEmpty ||
        data.homeSaloon != null;
  }

  /// Gets cached dashboard data (placeholder for future caching implementation)
  static Future<DashboardResponse?> getCachedDashboard() async {
    // TODO: Implement caching mechanism using SharedPreferences or local database
    log('DashboardService: Getting cached dashboard data (not implemented)');
    return null;
  }

  /// Caches dashboard data (placeholder for future caching implementation)
  static Future<void> cacheDashboardData(DashboardResponse response) async {
    // TODO: Implement caching mechanism
    log('DashboardService: Caching dashboard data (not implemented)');
  }

  /// Clears cached dashboard data
  static Future<void> clearCachedData() async {
    // TODO: Implement cache clearing
    log('DashboardService: Clearing cached dashboard data (not implemented)');
  }

  // OWNER DASHBOARD METHODS

  /// Fetches owner dashboard data
  static Future<OwnerDashboardResponse> getOwnerDashboard() async {
    try {
      log('DashboardService: Fetching owner dashboard data...');

      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw DashboardServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }
      log(authToken);

      final response = await http
          .get(
            Uri.parse(ApiEndpoints.ownerDashboardUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log(
        'DashboardService: Owner dashboard response status: ${response.statusCode}',
      );
      log('DashboardService: Owner dashboard response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final dashboardResponse = OwnerDashboardResponse.fromJson(jsonData);

        if (dashboardResponse.success) {
          log('DashboardService: Owner dashboard data fetched successfully');
          return dashboardResponse;
        } else {
          throw DashboardServiceException(
            dashboardResponse.message,
            statusCode: response.statusCode,
          );
        }
      } else {
        final errorMessage = _getErrorMessageFromStatusCode(
          response.statusCode,
        );
        throw DashboardServiceException(
          errorMessage,
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      log('DashboardService: No internet connection');
      throw DashboardServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } catch (e) {
      log('DashboardService: Owner dashboard error: $e');
      if (e is DashboardServiceException) {
        rethrow;
      }
      throw DashboardServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Fetches weekly chart data for owner dashboard
  static Future<WeeklyDataResponse> getWeeklyData() async {
    try {
      log('DashboardService: Fetching weekly data...');

      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw DashboardServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      final response = await http
          .get(
            Uri.parse(ApiEndpoints.ownerWeeklyDataUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log(
        'DashboardService: Weekly data response status: ${response.statusCode}',
      );
      log('DashboardService: Weekly data response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final weeklyResponse = WeeklyDataResponse.fromJson(jsonData);

        if (weeklyResponse.success) {
          log('DashboardService: Weekly data fetched successfully');
          return weeklyResponse;
        } else {
          throw DashboardServiceException(
            weeklyResponse.message,
            statusCode: response.statusCode,
          );
        }
      } else {
        final errorMessage = _getErrorMessageFromStatusCode(
          response.statusCode,
        );
        throw DashboardServiceException(
          errorMessage,
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      log('DashboardService: No internet connection');
      throw DashboardServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } catch (e) {
      log('DashboardService: Weekly data error: $e');
      if (e is DashboardServiceException) {
        rethrow;
      }
      throw DashboardServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Fetches monthly chart data for owner dashboard
  static Future<MonthlyDataResponse> getMonthlyData() async {
    try {
      log('DashboardService: Fetching monthly data...');

      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw DashboardServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      final response = await http
          .get(
            Uri.parse(ApiEndpoints.ownerMonthlyDataUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log(
        'DashboardService: Monthly data response status: ${response.statusCode}',
      );
      log('DashboardService: Monthly data response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final monthlyResponse = MonthlyDataResponse.fromJson(jsonData);

        if (monthlyResponse.success) {
          log('DashboardService: Monthly data fetched successfully');
          return monthlyResponse;
        } else {
          throw DashboardServiceException(
            monthlyResponse.message,
            statusCode: response.statusCode,
          );
        }
      } else {
        final errorMessage = _getErrorMessageFromStatusCode(
          response.statusCode,
        );
        throw DashboardServiceException(
          errorMessage,
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      log('DashboardService: No internet connection');
      throw DashboardServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } catch (e) {
      log('DashboardService: Monthly data error: $e');
      if (e is DashboardServiceException) {
        rethrow;
      }
      throw DashboardServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Fetches weekly chart data with new structure for owner dashboard
  static Future<WeeklyChartResponse> getWeeklyChartData() async {
    try {
      log('DashboardService: Fetching weekly chart data...');

      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw DashboardServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      final response = await http
          .get(
            Uri.parse(ApiEndpoints.ownerWeeklyDataUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log(
        'DashboardService: Weekly chart data response status: ${response.statusCode}',
      );
      log(
        'DashboardService: Weekly chart data response body: ${response.body}',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return WeeklyChartResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        throw DashboardServiceException(
          'Authentication failed. Please login again.',
          statusCode: 401,
        );
      } else {
        throw DashboardServiceException(
          'Failed to fetch weekly chart data. Status: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      log('DashboardService: No internet connection');
      throw DashboardServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } catch (e) {
      log('DashboardService: Weekly chart data error: $e');
      if (e is DashboardServiceException) {
        rethrow;
      }
      throw DashboardServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Fetches monthly chart data with new structure for owner dashboard
  static Future<MonthlyChartResponse> getMonthlyChartData() async {
    try {
      log('DashboardService: Fetching monthly chart data...');

      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw DashboardServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      final response = await http
          .get(
            Uri.parse(ApiEndpoints.ownerMonthlyDataUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log(
        'DashboardService: Monthly chart data response status: ${response.statusCode}',
      );
      log(
        'DashboardService: Monthly chart data response body: ${response.body}',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return MonthlyChartResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        throw DashboardServiceException(
          'Authentication failed. Please login again.',
          statusCode: 401,
        );
      } else {
        throw DashboardServiceException(
          'Failed to fetch monthly chart data. Status: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      log('DashboardService: No internet connection');
      throw DashboardServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } catch (e) {
      log('DashboardService: Monthly chart data error: $e');
      if (e is DashboardServiceException) {
        rethrow;
      }
      throw DashboardServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  // Helper methods for owner dashboard

  /// Format currency for display
  static String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Calculate total appointments from dashboard data
  static int getTotalAppointments(OwnerDashboardData data) {
    return data.appointmentData.totalAppointments;
  }

  /// Get today's appointments
  static List<Appointment> getTodayAppointments(OwnerDashboardData data) {
    final today = DateTime.now();
    final todayString =
        '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

    return data.appointmentData.pending
        .where((appointment) => appointment.appointmentDate == todayString)
        .toList();
  }

  /// Get appointments by status
  static List<Appointment> getAppointmentsByStatus(
    OwnerDashboardData data,
    String status,
  ) {
    switch (status.toLowerCase()) {
      case 'pending':
        return data.appointmentData.pending;
      case 'completed':
        return data.appointmentData.completed;
      case 'cancelled':
        return data.appointmentData.cancelled;
      default:
        return [];
    }
  }
}

/// Custom exception class for dashboard service errors
class DashboardServiceException implements Exception {
  final String message;
  final int? statusCode;
  final String? error;

  DashboardServiceException(this.message, {this.statusCode, this.error});

  @override
  String toString() {
    return 'DashboardServiceException: $message (Status: $statusCode, Error: $error)';
  }

  /// Returns user-friendly error message
  String get userFriendlyMessage {
    if (statusCode == 0) {
      return message; // Network or client errors
    }

    if (statusCode == 401) {
      return 'Please login again to continue.';
    }

    if (statusCode != null && statusCode! >= 500) {
      return 'Server is temporarily unavailable. Please try again later.';
    }

    return message;
  }

  /// Returns whether the error is recoverable (user can retry)
  bool get isRecoverable {
    if (statusCode == null || statusCode == 0) return true; // Network errors
    if (statusCode! >= 500) return true; // Server errors
    if (statusCode == 429) return true; // Rate limiting
    return false; // Client errors (4xx) are usually not recoverable
  }
}
