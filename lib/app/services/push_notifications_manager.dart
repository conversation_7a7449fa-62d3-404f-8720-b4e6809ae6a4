import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'shared_preferences_service.dart';

bool? newNotification;

/// Firebase Cloud Messaging Manager for Salon Booking App
class PushNotificationsManager {
  static final PushNotificationsManager _singleton =
      PushNotificationsManager._internal();

  factory PushNotificationsManager() {
    return _singleton;
  }

  PushNotificationsManager._internal();

  AuthorizationStatus mobileAuthorizationStatus =
      AuthorizationStatus.authorized;
  static String? fcmToken = 'temp_deviceToken';
  static String _deviceId = 'temp_deviceId';
  static int notificationCount = 0;
  static late FirebaseMessaging _firebaseMessaging;
  static FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  AndroidInitializationSettings initializationSettingsAndroid =
      const AndroidInitializationSettings('@mipmap/ic_launcher');

  DarwinInitializationSettings initializationSettingsIOS =
      const DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
        defaultPresentAlert: true,
        defaultPresentBadge: true,
        defaultPresentSound: true,
      );

  /// Main method to initialize and configure notification
  Future<bool> init() async {
    bool notifStatus = false;

    try {
      // Ask Notification permission and initialize FCM
      notifStatus =
          (await flutterLocalNotificationsPlugin
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >()
              ?.requestNotificationsPermission()) ==
          true;

      _firebaseMessaging = FirebaseMessaging.instance;
      NotificationSettings settings;

      // For iOS we have to request permission
      settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        provisional: false,
        sound: true,
        announcement: true,
        carPlay: true,
        criticalAlert: true,
      );

      mobileAuthorizationStatus = settings.authorizationStatus;

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        notifStatus = true;
        _deviceId = await _getId();
        _deviceId = (_deviceId.isEmpty) ? 'temp_deviceId' : _deviceId;

        // Get Firebase Messaging token
        fcmToken = await _firebaseMessaging.getToken();
        await FirebaseMessaging.instance.setAutoInitEnabled(true);

        // Save FCM token to SharedPreferences
        await SharedPreferencesService.saveFcmToken(fcmToken ?? '');
        await SharedPreferencesService.saveDeviceId(_deviceId);

        log("FCM Token: $fcmToken");
        log("Device ID: $_deviceId");

        fcmToken = (fcmToken?.isNotEmpty == true)
            ? fcmToken
            : 'temp_deviceToken';

        _firebaseMessaging.setForegroundNotificationPresentationOptions(
          sound: true,
          badge: true,
          alert: true,
        );

        await initAwesomeNotification();

        // Listen for token refresh
        _firebaseMessaging.onTokenRefresh.listen((token) async {
          fcmToken = token;
          await SharedPreferencesService.saveFcmToken(token);
          log("FCM Token refreshed: $token");
        });
      }
    } catch (e) {
      log('Error initializing FCM: $e');
    }
    return notifStatus;
  }

  static bool onDidReceiveLocalNotification(
    int id,
    String? title,
    String? body,
    String? payload,
  ) => true;

  void resetNotificationCount() => notificationCount = 0;

  /// To show local notification
  void hitAwesomeLocalNotification(RemoteMessage message) async {
    if (Platform.isAndroid) {
      showNotificationCustomSound(flutterLocalNotificationsPlugin, message);
    }
  }

  void removeLocalNotification(int id) =>
      flutterLocalNotificationsPlugin.cancel(id);

  void removeAllLocalNotification() =>
      flutterLocalNotificationsPlugin.cancelAll();

  void notificationConfigure() {
    _getInitialNotification();

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((message) {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Handling a onMessage message: ${message.data.toString()}');
      if (message.notification != null) {
        newNotification = true;
        log('onMessage:: ${message.toMap()}');
        hitAwesomeLocalNotification(message);
      }
    });

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('A new onMessageOpenedApp event was published!');
      debugPrint(
        'Handling a onMessageOpenedApp message: ${message.data.toString()}',
      );
      log('onMessageOpenedApp:: ${message.toMap()}');
      notificationNavigation(message.data, hasDelay: true);
    });
  }

  void _getInitialNotification() async {
    log('getInitialMessage Started');
    RemoteMessage? message = await FirebaseMessaging.instance
        .getInitialMessage();
    log('getInitialMessage ::${message?.toMap()}');
    if (message != null) {
      Future.delayed(const Duration(seconds: 2), () {
        notificationNavigation(message.data, isAppTerminated: true);
      });
    }
  }

  /// Handle notification navigation
  static void notificationNavigation(
    Map<dynamic, dynamic> message, {
    bool hasDelay = false,
    bool isAppTerminated = false,
  }) async {
    // TODO: Handle Navigation based on notification data
    // Example: Navigate to specific screens based on notification type
    log('Notification navigation: $message');
  }

  Future<void> initAwesomeNotification() async {
    final InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
      onDidReceiveBackgroundNotificationResponse:
          onDidReceiveBackgroundNotificationResponse,
    );

    NotificationAppLaunchDetails? appLaunchDetails =
        await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();

    if (appLaunchDetails != null && appLaunchDetails.didNotificationLaunchApp) {
      await Future.delayed(const Duration(seconds: 2));
      final details = json.decode(
        appLaunchDetails.notificationResponse?.payload ?? '{}',
      );
      if (details['filePath'] != null) {
        // Handle specific notification data
      }
    }

    // Create notification channel for Android
    AndroidNotificationChannel channel = const AndroidNotificationChannel(
      "salon_booking_channel",
      "Salon Booking Notifications",
      description: "Notifications for salon booking app",
      importance: Importance.max,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);

    flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin
        >()
        ?.requestPermissions(alert: true, badge: true, sound: true);
  }

  Future<String> _getId() async {
    var deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
      return iosDeviceInfo.identifierForVendor ?? 'unknown_ios_device';
    } else {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
      return androidDeviceInfo.id;
    }
  }

  static void onDidReceiveNotificationResponse(NotificationResponse message) {
    log("Did receive notification called");
    try {
      final details = json.decode(message.payload ?? '{}');
      if (Platform.isIOS) {
        flutterLocalNotificationsPlugin.cancel(message.id ?? 0);
      }
      log('Notification Response: ${message.payload}');
      notificationNavigation(details);
    } catch (e) {
      log('Error handling notification response: $e');
    }
  }

  static void onDidReceiveBackgroundNotificationResponse(
    NotificationResponse message,
  ) {
    log("Did receive background notification called");
    try {
      final details = json.decode(message.payload ?? '{}');
      if (Platform.isIOS) {
        flutterLocalNotificationsPlugin.cancel(message.id ?? 0);
      }
      log('Background Notification Response: ${message.payload}');
      notificationNavigation(details);
    } catch (e) {
      log('Error handling background notification response: $e');
    }
  }

  /// Get current FCM token
  static String? getCurrentFcmToken() => fcmToken;

  /// Get current device ID
  static String getCurrentDeviceId() => _deviceId;

  /// Get device token (for salon booking app, this is the same as FCM token)
  static String? getDeviceToken() => fcmToken;

  Future<void> showNotificationCustomSound(
    FlutterLocalNotificationsPlugin? instance,
    RemoteMessage message,
  ) async {
    DarwinNotificationDetails iOSChannelSpecifics =
        const DarwinNotificationDetails(
          presentSound: true,
          presentAlert: true,
          presentBadge: true,
        );

    AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          "salon_booking_channel",
          "Salon Booking Notifications",
          channelDescription: "Notifications for salon booking app",
          icon: "@mipmap/ic_launcher",
          autoCancel: true,
          playSound: true,
          enableVibration: true,
          importance: Importance.max,
          priority: Priority.high,
          enableLights: true,
          showWhen: true,
          vibrationPattern: Int64List.fromList([0, 1000, 5000, 2000]),
        );

    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSChannelSpecifics,
    );

    if (message.notification?.title != null) {
      await (instance ?? FlutterLocalNotificationsPlugin()).show(
        math.Random().nextInt(100),
        message.notification?.title,
        message.notification?.body,
        platformChannelSpecifics,
        payload: json.encode(message.data),
      );
    }
  }

  Future<String> downloadAndSaveFile(String url, String fileName) async {
    final Directory directory = await getApplicationDocumentsDirectory();
    final String filePath = '${directory.path}/$fileName';
    final http.Response response = await http.get(Uri.parse(url));
    final File file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);
    return filePath;
  }

  Future<Uint8List> getImageBytes(String imageUrl) async {
    http.Response response = await http.get(Uri.parse(imageUrl));
    return response.bodyBytes;
  }
}
