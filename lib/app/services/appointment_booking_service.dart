import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/appointment_booking_models.dart';
import '../services/shared_preferences_service.dart';
import '../config/razorpay_config.dart';

class AppointmentBookingService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Create appointment booking
  /// Endpoint: POST /appointments/book
  static Future<AppointmentBookingResponse> createAppointment(
    AppointmentBookingRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        log('AppointmentBookingService: No auth token found');
        return AppointmentBookingResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/appointments/book');

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());

      log('AppointmentBookingService: Creating appointment');
      log('AppointmentBookingService: Request URL: ${url.toString()}');
      log('AppointmentBookingService: Request body: $body');

      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('AppointmentBookingService: Response status: ${response.statusCode}');
      log('AppointmentBookingService: Response body: ${response.body}');

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return AppointmentBookingResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('AppointmentBookingService: Unauthorized - token may be expired');
        return AppointmentBookingResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else if (response.statusCode == 400) {
        log('AppointmentBookingService: Bad request - validation error');
        return AppointmentBookingResponse(
          code: 400,
          success: false,
          message: jsonData['message'] ?? 'Invalid booking request',
        );
      } else {
        log(
          'AppointmentBookingService: Error ${response.statusCode}: ${response.body}',
        );
        return AppointmentBookingResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to create appointment',
        );
      }
    } on SocketException {
      log('AppointmentBookingService: No internet connection');
      return AppointmentBookingResponse(
        code: 0,
        success: false,
        message:
            'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log('AppointmentBookingService: Client exception: $e');
      return AppointmentBookingResponse(
        code: 0,
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log('AppointmentBookingService: Exception occurred: $e');
      return AppointmentBookingResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Confirm payment after Razorpay success
  /// Endpoint: POST /appointments/confirm-payment
  static Future<PaymentConfirmationResponse> confirmPayment(
    PaymentConfirmationRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        log(
          'AppointmentBookingService: No auth token found for payment confirmation',
        );
        return PaymentConfirmationResponse(
          code: 401,
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse(
        '${ApiEndpoints.baseUrl}/appointments/confirm-payment',
      );

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());

      log('AppointmentBookingService: Confirming payment');
      log('AppointmentBookingService: Request URL: ${url.toString()}');
      log('AppointmentBookingService: Request body: $body');

      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log(
        'AppointmentBookingService: Payment confirmation response status: ${response.statusCode}',
      );
      log(
        'AppointmentBookingService: Payment confirmation response body: ${response.body}',
      );

      final jsonData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return PaymentConfirmationResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('AppointmentBookingService: Unauthorized - token may be expired');
        return PaymentConfirmationResponse(
          code: 401,
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else if (response.statusCode == 400) {
        log(
          'AppointmentBookingService: Bad request - payment confirmation failed',
        );
        return PaymentConfirmationResponse(
          code: 400,
          success: false,
          message: jsonData['message'] ?? 'Payment confirmation failed',
        );
      } else {
        log(
          'AppointmentBookingService: Error ${response.statusCode}: ${response.body}',
        );
        return PaymentConfirmationResponse(
          code: response.statusCode,
          success: false,
          message: jsonData['message'] ?? 'Failed to confirm payment',
        );
      }
    } on SocketException {
      log(
        'AppointmentBookingService: No internet connection for payment confirmation',
      );
      return PaymentConfirmationResponse(
        code: 0,
        success: false,
        message:
            'No internet connection. Please check your network and try again.',
      );
    } on http.ClientException catch (e) {
      log(
        'AppointmentBookingService: Client exception during payment confirmation: $e',
      );
      return PaymentConfirmationResponse(
        code: 0,
        success: false,
        message: 'Network error. Please try again.',
      );
    } catch (e) {
      log(
        'AppointmentBookingService: Exception during payment confirmation: $e',
      );
      return PaymentConfirmationResponse(
        code: 0,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Calculate payment amount based on payment mode
  static double calculatePaymentAmount(
    double totalAmount,
    PaymentMode paymentMode,
  ) {
    switch (paymentMode) {
      case PaymentMode.fullPayment:
        return totalAmount;
      case PaymentMode.slotBooking:
        final slotAmount = totalAmount * 0.05; // 5% of total
        return slotAmount < 50.0 ? 50.0 : slotAmount; // Minimum ₹50
      case PaymentMode.payAtSalon:
        return 0.0;
    }
  }

  /// Validate appointment booking request
  static bool validateBookingRequest(AppointmentBookingRequest request) {
    if (request.appointmentDate.isEmpty) {
      log('AppointmentBookingService: Empty appointment date');
      return false;
    }

    if (request.startTime.isEmpty || request.endTime.isEmpty) {
      log('AppointmentBookingService: Empty time slots');
      return false;
    }

    if (request.serviceIds.isEmpty) {
      log('AppointmentBookingService: No services selected');
      return false;
    }

    if (request.barberId.isEmpty) {
      log('AppointmentBookingService: No barber selected');
      return false;
    }

    if (request.saloonId.isEmpty) {
      log('AppointmentBookingService: No salon ID provided');
      return false;
    }

    return true;
  }

  /// Format currency amount
  static String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Get payment mode from string value
  static PaymentMode? getPaymentModeFromValue(String value) {
    for (final mode in PaymentMode.values) {
      if (mode.value == value) {
        return mode;
      }
    }
    return null;
  }
}
