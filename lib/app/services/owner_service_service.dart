import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/owner_service_models.dart';
import '../services/shared_preferences_service.dart';

class OwnerServiceService {
  /// Add a new service
  static Future<OwnerServiceResponse> addService(
    OwnerServiceRequest request, [
    File? imageFile,
  ]) async {
    try {
      log('OwnerServiceService: Adding service: ${request.name}');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw ServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      // Try JSON request first (like other working APIs)
      final uri = Uri.parse(ApiEndpoints.addBarberService);

      // Use the model's toJson method to ensure consistent field mapping
      final requestBody = request.toJson();

      log('OwnerServiceService: Request body: ${jsonEncode(requestBody)}');
      log(
        'OwnerServiceService: Image URL being sent with key "serviceImage": ${request.serviceImage ?? 'No image'}',
      );
      log('OwnerServiceService: Token: ${token.substring(0, 20)}...');

      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      log(
        'OwnerServiceService: Add service response status: ${response.statusCode}',
      );
      log('OwnerServiceService: Add service response body: ${response.body}');
      log('OwnerServiceService: Response headers: ${response.headers}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Log the response type for debugging
        try {
          final decodedResponse = jsonDecode(response.body);
          log(
            'OwnerServiceService: Response type: ${decodedResponse.runtimeType}',
          );
          if (decodedResponse is List) {
            log(
              'OwnerServiceService: Response is a list with ${decodedResponse.length} items',
            );
            if (decodedResponse.isNotEmpty) {
              log('OwnerServiceService: First item: ${decodedResponse.first}');
            }
          } else if (decodedResponse is Map) {
            log(
              'OwnerServiceService: Response is a map with keys: ${decodedResponse.keys}',
            );
          }
        } catch (e) {
          log('OwnerServiceService: Error analyzing response: $e');
        }

        return OwnerServiceResponse.fromString(response.body);
      } else {
        final errorData = jsonDecode(response.body);

        // Extract detailed validation errors
        String detailedMessage =
            errorData['message'] ?? 'Failed to add service';
        if (errorData['errors'] != null) {
          final errors = errorData['errors'] as Map<String, dynamic>;
          List<String> errorMessages = [];

          errors.forEach((field, fieldErrors) {
            if (field != '_errors' &&
                fieldErrors is Map &&
                fieldErrors['_errors'] is List) {
              final fieldErrorList = fieldErrors['_errors'] as List;
              for (String error in fieldErrorList) {
                errorMessages.add('$field: $error');
              }
            }
          });

          if (errorMessages.isNotEmpty) {
            detailedMessage = '$detailedMessage\n${errorMessages.join('\n')}';
          }
        }

        throw ServiceException(
          detailedMessage,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('OwnerServiceService: Add service error: $e');
      if (e is ServiceException) {
        rethrow;
      }
      throw ServiceException('Network error occurred. Please try again.');
    }
  }

  /// Get all services for the owner (using new model)
  static Future<List<GetAllServiceModel>> getBarberService() async {
    try {
      log('OwnerServiceService: Fetching services with new model');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw ServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.get(
        Uri.parse(ApiEndpoints.getBarberServices),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log('OwnerServiceService: Get services token: $token');
      log(
        'OwnerServiceService: Get services response status: ${response.statusCode}',
      );
      log('OwnerServiceService: Get services response body: ${response.body}');

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);

        // Debug logging for image fields in response
        if (json['services'] is List) {
          for (var service in json['services']) {
            log(
              'OwnerServiceService: Service ${service['name']} - serviceimage (lowercase): ${service['serviceimage']}, image: ${service['image']}',
            );
          }
        }

        final serviceResponse = ServiceResponse.fromJson(json);
        return serviceResponse.services;
      } else {
        final errorResponse = ServiceErrorResponse.fromString(response.body);
        throw ServiceException(
          errorResponse.message,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('OwnerServiceService: Get services error: $e');
      if (e is ServiceException) {
        rethrow;
      }
      throw ServiceException('Network error occurred. Please try again.');
    }
  }

  /// Get all services for the owner (legacy method - keeping for backward compatibility)
  static Future<OwnerServicesListResponse> getServices() async {
    try {
      log('OwnerServiceService: Fetching services (legacy)');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw ServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final response = await http.get(
        Uri.parse(ApiEndpoints.getBarberServices),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log(
        'OwnerServiceService: Get services response status: ${response.statusCode}',
      );
      log('OwnerServiceService: Get services response body: ${response.body}');

      if (response.statusCode == 200) {
        return OwnerServicesListResponse.fromString(response.body);
      } else {
        final errorResponse = ServiceErrorResponse.fromString(response.body);
        throw ServiceException(
          errorResponse.message,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('OwnerServiceService: Get services error: $e');
      if (e is ServiceException) {
        rethrow;
      }
      throw ServiceException('Network error occurred. Please try again.');
    }
  }

  /// Update an existing service
  static Future<OwnerServiceResponse> updateService(
    String serviceId,
    OwnerServiceRequest request, [
    File? imageFile,
  ]) async {
    try {
      log('OwnerServiceService: Updating service: $serviceId');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw ServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final url = ApiEndpoints.getBarberServices;

      // Use the model's toJson method and add service ID
      final requestBody = request.toJson();
      requestBody['serviceId'] =
          serviceId; // Include service ID in the request body

      log("=== UPDATE SERVICE REQUEST ===");
      log("URL: $url");
      log("Token: $token");
      log(
        "Headers: {'Content-Type': 'application/json', 'Authorization': 'Bearer $token'}",
      );
      log("Request Body: ${jsonEncode(requestBody)}");
      log("================================");

      final response = await http.put(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      log("=== UPDATE SERVICE RESPONSE ===");
      log("Status Code: ${response.statusCode}");
      log("Body: ${response.body}");
      log("================================");

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = OwnerServiceResponse.fromJson(
          jsonDecode(response.body) as Map<String, dynamic>,
        );

        log("=== PARSED RESPONSE ===");
        log("Success: ${result.success}");
        log("Message: ${result.message}");
        log("=======================");

        return result;
      } else {
        throw ServiceException(
          'Failed to update service: Status ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      log("=== ERROR IN updateService ===");
      log("Error: $e");
      log("===============================");
      if (e is ServiceException) {
        rethrow;
      }
      throw ServiceException('Failed to update service: $e');
    }
  }

  /// Delete a service using new API method
  static Future<DeleteServiceResponse> deleteBarberService(
    String serviceId,
  ) async {
    try {
      log('OwnerServiceService: Deleting service: $serviceId');

      final token = await SharedPreferencesService.getToken();
      if (token == null || token.isEmpty) {
        throw ServiceException(
          'Authentication token not found. Please login again.',
        );
      }

      final deleteUrl = ApiEndpoints.deleteBarberService;

      final response = await http.delete(
        Uri.parse(deleteUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'id': serviceId}),
      );

      log('OwnerServiceService: Delete service token: $token');
      log('OwnerServiceService: Delete URL: $deleteUrl');
      log(
        'OwnerServiceService: Delete response status: ${response.statusCode}',
      );
      log('OwnerServiceService: Delete response body: ${response.body}');

      if (response.statusCode == 200) {
        return DeleteServiceResponse.fromJson(
          jsonDecode(response.body) as Map<String, dynamic>,
        );
      } else {
        final errorResponse = ServiceErrorResponse.fromString(response.body);
        throw ServiceException(
          errorResponse.message,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      log('OwnerServiceService: Delete service error: $e');
      if (e is ServiceException) {
        rethrow;
      }
      throw ServiceException('Network error occurred. Please try again.');
    }
  }

  /// Delete a service (legacy method - keeping for backward compatibility)
  static Future<bool> deleteService(String serviceId) async {
    try {
      final response = await deleteBarberService(serviceId);
      return response.success;
    } catch (e) {
      log('OwnerServiceService: Legacy delete service error: $e');
      return false;
    }
  }
}

/// Custom exception for service operations
class ServiceException implements Exception {
  final String message;
  final int? statusCode;
  final String? error;

  ServiceException(this.message, {this.statusCode, this.error});

  /// Returns user-friendly error message
  String get userFriendlyMessage {
    if (statusCode == 401) {
      return 'Please login again to continue.';
    }

    if (statusCode == 403) {
      return 'You do not have permission to perform this action.';
    }

    if (statusCode == 404) {
      return 'Service not found. It may have been deleted.';
    }

    if (statusCode != null && statusCode! >= 500) {
      return 'Server is temporarily unavailable. Please try again later.';
    }

    return message;
  }

  /// Returns whether the error is recoverable (user can retry)
  bool get isRecoverable {
    if (statusCode == null || statusCode == 0) return true; // Network errors
    if (statusCode! >= 500) return true; // Server errors
    if (statusCode == 429) return true; // Rate limiting
    return false; // Client errors (4xx) are usually not recoverable
  }

  @override
  String toString() {
    return 'ServiceException: $message (Status: $statusCode, Error: $error)';
  }
}
