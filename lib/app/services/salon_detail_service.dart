import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/salon_detail_models.dart';
import '../services/shared_preferences_service.dart';

/// Service class for handling salon detail-related API calls
class SalonDetailService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Fetches salon detail data including salon info, services, barbers, and reviews
  /// Returns [SalonDetailResponse] on success or throws [SalonDetailServiceException] on error
  static Future<SalonDetailResponse> getSalonDetail(String salonId) async {
    try {
      if (salonId.isEmpty) {
        throw SalonDetailServiceException(
          'Salon ID is required',
          statusCode: 400,
        );
      }

      log('SalonDetailService: Fetching salon detail for ID: $salonId');
      
      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw SalonDetailServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      final response = await http.get(
        Uri.parse(ApiEndpoints.salonDetailUrl(salonId)),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
      ).timeout(_timeoutDuration);

      log('SalonDetailService: Response status: ${response.statusCode}');
      log('SalonDetailService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final salonDetailResponse = SalonDetailResponse.fromString(response.body);
        
        if (salonDetailResponse.success) {
          log('SalonDetailService: Salon detail data fetched successfully');
          return salonDetailResponse;
        } else {
          throw SalonDetailServiceException(
            salonDetailResponse.message,
            statusCode: response.statusCode,
          );
        }
      } else {
        // Handle different HTTP status codes
        final errorMessage = _getErrorMessageFromStatusCode(response.statusCode);
        final errorResponse = _parseErrorResponse(response.body);
        
        throw SalonDetailServiceException(
          errorResponse?.message ?? errorMessage,
          statusCode: response.statusCode,
          error: errorResponse?.error,
        );
      }
    } on SocketException {
      log('SalonDetailService: No internet connection');
      throw SalonDetailServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } on http.ClientException catch (e) {
      log('SalonDetailService: Client exception: $e');
      throw SalonDetailServiceException(
        'Failed to connect to server. Please try again later.',
        statusCode: 0,
        error: e.toString(),
      );
    } on FormatException catch (e) {
      log('SalonDetailService: Format exception: $e');
      throw SalonDetailServiceException(
        'Invalid response format from server.',
        statusCode: 0,
        error: e.toString(),
      );
    } catch (e) {
      log('SalonDetailService: Unexpected error: $e');
      throw SalonDetailServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Refreshes salon detail data with optional force refresh
  static Future<SalonDetailResponse> refreshSalonDetail(String salonId, {bool forceRefresh = false}) async {
    log('SalonDetailService: Refreshing salon detail data (force: $forceRefresh)');
    
    // Add any caching logic here if needed
    return await getSalonDetail(salonId);
  }

  /// Gets error message based on HTTP status code
  static String _getErrorMessageFromStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your data and try again.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission to access this resource.';
      case 404:
        return 'Salon not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Server is temporarily unavailable.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again.';
      default:
        return 'An error occurred (Status: $statusCode). Please try again.';
    }
  }

  /// Parses error response from API
  static SalonDetailErrorResponse? _parseErrorResponse(String responseBody) {
    try {
      if (responseBody.isNotEmpty) {
        return SalonDetailErrorResponse.fromString(responseBody);
      }
    } catch (e) {
      log('SalonDetailService: Failed to parse error response: $e');
    }
    return null;
  }

  /// Validates salon detail response data
  static bool validateSalonDetailData(SalonDetailData? data) {
    if (data == null) return false;
    
    // Basic validation - salon data should be present
    return data.saloonData != null;
  }

  /// Gets cached salon detail data (placeholder for future caching implementation)
  static Future<SalonDetailResponse?> getCachedSalonDetail(String salonId) async {
    // TODO: Implement caching mechanism using SharedPreferences or local database
    log('SalonDetailService: Getting cached salon detail data (not implemented)');
    return null;
  }

  /// Caches salon detail data (placeholder for future caching implementation)
  static Future<void> cacheSalonDetailData(String salonId, SalonDetailResponse response) async {
    // TODO: Implement caching mechanism
    log('SalonDetailService: Caching salon detail data (not implemented)');
  }

  /// Clears cached salon detail data
  static Future<void> clearCachedData(String salonId) async {
    // TODO: Implement cache clearing
    log('SalonDetailService: Clearing cached salon detail data (not implemented)');
  }
}

/// Error response model for handling API errors
class SalonDetailErrorResponse {
  final bool success;
  final String message;
  final String? error;
  final int? statusCode;

  SalonDetailErrorResponse({
    required this.success,
    required this.message,
    this.error,
    this.statusCode,
  });

  factory SalonDetailErrorResponse.fromJson(Map<String, dynamic> json) {
    return SalonDetailErrorResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? 'Unknown error occurred',
      error: json['error'] as String?,
      statusCode: json['statusCode'] as int?,
    );
  }

  factory SalonDetailErrorResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return SalonDetailErrorResponse.fromJson(json);
    } catch (e) {
      return SalonDetailErrorResponse(
        success: false,
        message: 'Failed to parse error response',
        error: e.toString(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'statusCode': statusCode,
    };
  }

  @override
  String toString() {
    return 'SalonDetailErrorResponse(success: $success, message: $message, error: $error)';
  }
}

/// Custom exception class for salon detail service errors
class SalonDetailServiceException implements Exception {
  final String message;
  final int? statusCode;
  final String? error;

  SalonDetailServiceException(
    this.message, {
    this.statusCode,
    this.error,
  });

  @override
  String toString() {
    return 'SalonDetailServiceException: $message (Status: $statusCode, Error: $error)';
  }

  /// Returns user-friendly error message
  String get userFriendlyMessage {
    if (statusCode == 0) {
      return message; // Network or client errors
    }
    
    if (statusCode == 401) {
      return 'Please login again to continue.';
    }
    
    if (statusCode == 404) {
      return 'Salon not found. Please try again.';
    }
    
    if (statusCode != null && statusCode! >= 500) {
      return 'Server is temporarily unavailable. Please try again later.';
    }
    
    return message;
  }

  /// Returns whether the error is recoverable (user can retry)
  bool get isRecoverable {
    if (statusCode == null || statusCode == 0) return true; // Network errors
    if (statusCode! >= 500) return true; // Server errors
    if (statusCode == 429) return true; // Rate limiting
    if (statusCode == 404) return false; // Not found
    return false; // Client errors (4xx) are usually not recoverable
  }
}
