import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/owner_profile_model.dart';
import '../services/shared_preferences_service.dart';

class OwnerProfileService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Fetch owner profile data
  /// Endpoint: GET /owner/profile
  static Future<OwnerProfileResponse> getOwnerProfile() async {
    try {
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        log('OwnerProfileService: No auth token found');
        return OwnerProfileResponse(
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/owner/profile');

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      log('OwnerProfileService: Fetching owner profile from ${url.toString()}');

      final response = await http
          .get(url, headers: headers)
          .timeout(_timeoutDuration);

      log('OwnerProfileService: Response status: ${response.statusCode}');
      log('OwnerProfileService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return OwnerProfileResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('OwnerProfileService: Unauthorized - token may be expired');
        return OwnerProfileResponse(
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else {
        log(
          'OwnerProfileService: Error ${response.statusCode}: ${response.body}',
        );
        return OwnerProfileResponse(
          success: false,
          message:
              'Failed to fetch profile data. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      log('OwnerProfileService: Exception occurred: $e');
      return OwnerProfileResponse(
        success: false,
        message: 'Network error: ${e.toString()}',
      );
    }
  }

  /// Update owner profile data
  /// Endpoint: PUT /owner/profile
  static Future<OwnerProfileResponse> updateOwnerProfile(
    OwnerProfileUpdateRequest request,
  ) async {
    try {
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        log('OwnerProfileService: No auth token found for update');
        return OwnerProfileResponse(
          success: false,
          message: 'Authentication token not found',
        );
      }

      final url = Uri.parse('${ApiEndpoints.baseUrl}/owner/profile');

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      final body = jsonEncode(request.toJson());

      log('OwnerProfileService: Updating owner profile');
      log('OwnerProfileService: Request body: $body');

      final response = await http
          .put(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log(
        'OwnerProfileService: Update response status: ${response.statusCode}',
      );
      log('OwnerProfileService: Update response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return OwnerProfileResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        log('OwnerProfileService: Unauthorized during update');
        return OwnerProfileResponse(
          success: false,
          message: 'Authentication failed. Please login again.',
        );
      } else {
        log(
          'OwnerProfileService: Update error ${response.statusCode}: ${response.body}',
        );

        // Try to parse error message from response
        try {
          final errorData = jsonDecode(response.body);
          final errorMessage =
              errorData['message'] ?? 'Failed to update profile';
          return OwnerProfileResponse(success: false, message: errorMessage);
        } catch (parseError) {
          return OwnerProfileResponse(
            success: false,
            message: 'Failed to update profile. Status: ${response.statusCode}',
          );
        }
      }
    } catch (e) {
      log('OwnerProfileService: Update exception occurred: $e');
      return OwnerProfileResponse(
        success: false,
        message: 'Network error during update: ${e.toString()}',
      );
    }
  }

  /// Helper method to validate profile data before update
  static bool validateProfileData(OwnerProfileUpdateRequest request) {
    // Basic validation - at least one field should be provided
    final json = request.toJson();
    if (json.isEmpty) {
      log('OwnerProfileService: No data provided for update');
      return false;
    }

    // Validate time format if provided
    if (request.startTime != null && request.startTime!.isNotEmpty) {
      if (!_isValidTimeFormat(request.startTime!)) {
        log(
          'OwnerProfileService: Invalid start time format: ${request.startTime}',
        );
        return false;
      }
    }

    if (request.endTime != null && request.endTime!.isNotEmpty) {
      if (!_isValidTimeFormat(request.endTime!)) {
        log('OwnerProfileService: Invalid end time format: ${request.endTime}');
        return false;
      }
    }

    // Validate phone number if provided
    if (request.salonPhone != null && request.salonPhone!.isNotEmpty) {
      if (!_isValidPhoneNumber(request.salonPhone!)) {
        log(
          'OwnerProfileService: Invalid phone number format: ${request.salonPhone}',
        );
        return false;
      }
    }

    // Validate email if provided
    if (request.salonEmail != null && request.salonEmail!.isNotEmpty) {
      if (!_isValidEmailFormat(request.salonEmail!)) {
        log('OwnerProfileService: Invalid email format: ${request.salonEmail}');
        return false;
      }
    }

    return true;
  }

  /// Helper method to validate time format (HH:MM)
  static bool _isValidTimeFormat(String time) {
    final timeRegex = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
    return timeRegex.hasMatch(time);
  }

  /// Helper method to validate phone number
  static bool _isValidPhoneNumber(String phone) {
    // Remove any non-digit characters for validation
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');
    return digitsOnly.length >= 10 && digitsOnly.length <= 15;
  }

  /// Helper method to validate email format
  static bool _isValidEmailFormat(String email) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email.trim());
  }

  /// Helper method to format time from TimeOfDay to API format
  static String formatTimeForApi(int hour, int minute) {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// Helper method to parse time from API format to hour/minute
  static Map<String, int> parseTimeFromApi(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length == 2) {
        return {'hour': int.parse(parts[0]), 'minute': int.parse(parts[1])};
      }
    } catch (e) {
      log('OwnerProfileService: Error parsing time: $timeString');
    }

    // Return default values if parsing fails
    return {'hour': 9, 'minute': 0};
  }
}
