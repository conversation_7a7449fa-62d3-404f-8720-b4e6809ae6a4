import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/auth_models.dart';
import 'shared_preferences_service.dart';
import 'device_info_service.dart';

class AuthService {
  // Login with Email
  static Future<LoginResponse> loginWithEmail(LoginRequest request) async {
    try {
      log(
        'AuthService: Starting login for ${request.email} with role ${request.role}',
      );

      final response = await http.post(
        Uri.parse(ApiEndpoints.loginUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(request.toJson()),
      );
      log(request.toJson().toString());

      log('AuthService: Login response status: ${response.statusCode}');
      log('AuthService: Login response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final loginResponse = LoginResponse.fromJson(responseData);

        // Save token and role to SharedPreferences if login successful
        if (loginResponse.success && loginResponse.token != null) {
          await SharedPreferencesService.saveToken(loginResponse.token!);
          if (loginResponse.role != null) {
            await SharedPreferencesService.saveRole(loginResponse.role!);
          }
          await SharedPreferencesService.saveEmail(request.email);

          // Save verification and profile completion status
          if (loginResponse.data != null) {
            await SharedPreferencesService.saveVerificationStatus(
              loginResponse.data!.isVerified,
            );
            await SharedPreferencesService.saveProfileCompletionStatus(
              loginResponse.data!.isProfileCompleted,
            );
          }

          log('AuthService: Login data saved to SharedPreferences');
        }

        return loginResponse;
      } else {
        // Handle error response
        final errorMessage = responseData['message'] ?? 'Login failed';
        throw ApiError(message: errorMessage, statusCode: response.statusCode);
      }
    } catch (e) {
      log('AuthService: Login error: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(message: 'Network error occurred. Please try again.');
    }
  }

  // Email Registration
  static Future<AuthResponse> registerWithEmail(
    EmailRegisterRequest request,
  ) async {
    try {
      log('Registering with email: ${request.email}');

      final response = await http.post(
        Uri.parse(ApiEndpoints.emailRegisterUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(request.toJson()),
      );

      log('Registration response status: ${response.statusCode}');
      log('Registration response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return AuthResponse.fromJson(responseData);
      } else {
        throw ApiError.fromJson(responseData);
      }
    } catch (e) {
      log('Registration error: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(message: 'Network error occurred');
    }
  }

  // Phone Registration
  static Future<AuthResponse> registerWithPhone(
    PhoneRegisterRequest request,
  ) async {
    try {
      log('Registering with phone: ${request.phone}');

      final response = await http.post(
        Uri.parse(ApiEndpoints.phoneRegisterUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(request.toJson()),
      );

      log('Registration response status: ${response.statusCode}');
      log('Registration response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return AuthResponse.fromJson(responseData);
      } else {
        throw ApiError.fromJson(responseData);
      }
    } catch (e) {
      log('Registration error: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(message: 'Network error occurred');
    }
  }

  // Resend OTP
  static Future<AuthResponse> resendOtp(ResendOtpRequest request) async {
    try {
      log('Resending OTP for: ${request.identifier}');

      final response = await http.post(
        Uri.parse(ApiEndpoints.resendOtpUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(request.toJson()),
      );

      log('Resend OTP response status: ${response.statusCode}');
      log('Resend OTP response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return AuthResponse.fromJson(responseData);
      } else {
        throw ApiError.fromJson(responseData);
      }
    } catch (e) {
      log('Resend OTP error: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(message: 'Network error occurred');
    }
  }

  // Verify OTP
  static Future<AuthResponse> verifyOtp(VerifyOtpRequest request) async {
    try {
      log('Verifying OTP for: ${request.identifier}');

      final response = await http.post(
        Uri.parse(ApiEndpoints.verifyOtpUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(request.toJson()),
      );

      log('Verify OTP response status: ${response.statusCode}');
      log('Verify OTP response body: ${response.body}');

      final responseData = jsonDecode(response.body);
      log(request.toJson().toString());

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(responseData);

        // Save user data to shared preferences
        if (authResponse.token != null) {
          await SharedPreferencesService.saveToken(authResponse.token!);
          log("Auth token saved: ${authResponse.token!}");
        }

        if (authResponse.role != null) {
          await SharedPreferencesService.saveRole(authResponse.role!);
          log("User role saved: ${authResponse.role!}");
        }

        // Save user email/phone from identifier
        if (request.identifier.contains('@')) {
          await SharedPreferencesService.saveEmail(request.identifier);
        } else {
          await SharedPreferencesService.savePhone(request.identifier);
        }

        // Save verification and profile completion status
        if (authResponse.data != null) {
          await SharedPreferencesService.saveVerificationStatus(
            authResponse.data!.isVerified,
          );
          await SharedPreferencesService.saveProfileCompletionStatus(
            authResponse.data!.isProfileCompleted,
          );
        }

        return authResponse;
      } else {
        throw ApiError.fromJson(responseData);
      }
    } catch (e) {
      log('Verify OTP error: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(message: 'Network error occurred');
    }
  }

  // Logout
  static Future<bool> logout() async {
    try {
      log('AuthService: Starting logout process...');

      // Clear all user data from SharedPreferences
      final success = await SharedPreferencesService.clearUserData();

      if (success) {
        log('AuthService: User data cleared successfully');
        return true;
      } else {
        log('AuthService: Failed to clear user data');
        return false;
      }
    } catch (e) {
      log('AuthService: Logout error: $e');
      return false;
    }
  }

  // Enhanced logout with server-side logout (for future implementation)
  static Future<bool> logoutWithServerCall() async {
    try {
      log('AuthService: Starting enhanced logout process...');

      // TODO: Implement server-side logout API call when available
      // final token = await getCurrentUserToken();
      // if (token != null && token.isNotEmpty) {
      //   final response = await http.post(
      //     Uri.parse(ApiEndpoints.logoutUrl),
      //     headers: {
      //       'Content-Type': 'application/json',
      //       'Authorization': 'Bearer $token',
      //     },
      //   );
      //   log('Server logout response: ${response.statusCode}');
      // }

      // Clear local data regardless of server response
      final success = await SharedPreferencesService.clearUserData();

      if (success) {
        log('AuthService: Enhanced logout completed successfully');
        return true;
      } else {
        log('AuthService: Failed to clear user data during enhanced logout');
        return false;
      }
    } catch (e) {
      log('AuthService: Enhanced logout error: $e');
      // Even if server call fails, try to clear local data
      try {
        await SharedPreferencesService.clearUserData();
        return true;
      } catch (clearError) {
        log('AuthService: Failed to clear local data: $clearError');
        return false;
      }
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    return await SharedPreferencesService.isLoggedIn();
  }

  // Get current user role
  static Future<String?> getCurrentUserRole() async {
    return await SharedPreferencesService.getRole();
  }

  // Get current user token
  static Future<String?> getCurrentUserToken() async {
    return await SharedPreferencesService.getToken();
  }

  // ============ FCM and Device Info Helper Methods ============

  /// Create LoginRequest with device info
  static Future<LoginRequest> createLoginRequestWithDeviceInfo({
    required String email,
    required String password,
    required String role,
  }) async {
    try {
      final deviceInfo = await DeviceInfoService().getDeviceInfoForApi();

      return LoginRequest(
        email: email,
        password: password,
        role: role,
        deviceToken: deviceInfo['deviceToken'],
        fcmToken: deviceInfo['fcmToken'],
        deviceId: deviceInfo['deviceId'],
      );
    } catch (e) {
      log('AuthService: Error getting device info for login: $e');
      // Return request without device info if there's an error
      return LoginRequest(email: email, password: password, role: role);
    }
  }

  /// Create EmailRegisterRequest with device info
  static Future<EmailRegisterRequest> createEmailRegisterRequestWithDeviceInfo({
    required String email,
    required String password,
    required String role,
  }) async {
    try {
      final deviceInfo = await DeviceInfoService().getDeviceInfoForApi();

      return EmailRegisterRequest(
        email: email,
        password: password,
        role: role,
        deviceToken: deviceInfo['deviceToken'],
        fcmToken: deviceInfo['fcmToken'],
        deviceId: deviceInfo['deviceId'],
      );
    } catch (e) {
      log('AuthService: Error getting device info for email registration: $e');
      // Return request without device info if there's an error
      return EmailRegisterRequest(email: email, password: password, role: role);
    }
  }

  /// Create PhoneRegisterRequest with device info
  static Future<PhoneRegisterRequest> createPhoneRegisterRequestWithDeviceInfo({
    required String phone,
    required String password,
    required String role,
  }) async {
    try {
      final deviceInfo = await DeviceInfoService().getDeviceInfoForApi();

      return PhoneRegisterRequest(
        phone: phone,
        password: password,
        role: role,
        deviceToken: deviceInfo['deviceToken'],
        fcmToken: deviceInfo['fcmToken'],
        deviceId: deviceInfo['deviceId'],
      );
    } catch (e) {
      log('AuthService: Error getting device info for phone registration: $e');
      // Return request without device info if there's an error
      return PhoneRegisterRequest(phone: phone, password: password, role: role);
    }
  }

  /// Initialize FCM and device info (call this at app startup)
  static Future<void> initializeFcmAndDeviceInfo() async {
    try {
      log('AuthService: Initializing FCM and device info...');
      await DeviceInfoService().initializeDeviceInfo();
      log('AuthService: FCM and device info initialized successfully');
    } catch (e) {
      log('AuthService: Error initializing FCM and device info: $e');
    }
  }
}
