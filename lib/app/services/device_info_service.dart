import 'dart:io';
import 'dart:developer';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'shared_preferences_service.dart';

/// Service to handle device information and FCM tokens
class DeviceInfoService {
  static final DeviceInfoService _instance = DeviceInfoService._internal();
  factory DeviceInfoService() => _instance;
  DeviceInfoService._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  /// Get device ID based on platform
  Future<String> getDeviceId() async {
    try {
      if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? 'unknown_ios_device';
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return androidInfo.id;
      } else {
        return 'unknown_device';
      }
    } catch (e) {
      log('DeviceInfoService: Error getting device ID: $e');
      return 'error_device_id';
    }
  }

  /// Get FCM token
  Future<String?> getFcmToken() async {
    try {
      final fcmToken = await FirebaseMessaging.instance.getToken();
      log('DeviceInfoService: FCM Token retrieved: $fcmToken');
      return fcmToken;
    } catch (e) {
      log('DeviceInfoService: Error getting FCM token: $e');
      return null;
    }
  }

  /// Get device token (for this app, it's the same as FCM token)
  Future<String?> getDeviceToken() async {
    return await getFcmToken();
  }

  /// Initialize and get all device information
  Future<Map<String, String?>> initializeDeviceInfo() async {
    try {
      log('DeviceInfoService: Initializing device info...');
      
      // Get device ID
      final deviceId = await getDeviceId();
      
      // Get FCM token
      final fcmToken = await getFcmToken();
      
      // Device token is the same as FCM token for this app
      final deviceToken = fcmToken;
      
      // Save to SharedPreferences
      await SharedPreferencesService.saveDeviceId(deviceId);
      if (fcmToken != null) {
        await SharedPreferencesService.saveFcmToken(fcmToken);
        await SharedPreferencesService.saveDeviceToken(fcmToken);
      }
      
      final deviceInfo = {
        'deviceId': deviceId,
        'fcmToken': fcmToken,
        'deviceToken': deviceToken,
      };
      
      log('DeviceInfoService: Device info initialized successfully');
      log('DeviceInfoService: Device ID: $deviceId');
      log('DeviceInfoService: FCM Token: $fcmToken');
      
      return deviceInfo;
    } catch (e) {
      log('DeviceInfoService: Error initializing device info: $e');
      return {
        'deviceId': 'error_device_id',
        'fcmToken': null,
        'deviceToken': null,
      };
    }
  }

  /// Get cached device info from SharedPreferences
  Future<Map<String, String?>> getCachedDeviceInfo() async {
    try {
      return await SharedPreferencesService.getDeviceInfo();
    } catch (e) {
      log('DeviceInfoService: Error getting cached device info: $e');
      return {
        'deviceId': null,
        'fcmToken': null,
        'deviceToken': null,
      };
    }
  }

  /// Refresh FCM token and update stored values
  Future<String?> refreshFcmToken() async {
    try {
      log('DeviceInfoService: Refreshing FCM token...');
      
      // Delete the current token to force refresh
      await FirebaseMessaging.instance.deleteToken();
      
      // Get new token
      final newToken = await FirebaseMessaging.instance.getToken();
      
      if (newToken != null) {
        // Save new token
        await SharedPreferencesService.saveFcmToken(newToken);
        await SharedPreferencesService.saveDeviceToken(newToken);
        
        log('DeviceInfoService: FCM token refreshed: $newToken');
      }
      
      return newToken;
    } catch (e) {
      log('DeviceInfoService: Error refreshing FCM token: $e');
      return null;
    }
  }

  /// Get device information for API requests
  Future<Map<String, String>> getDeviceInfoForApi() async {
    try {
      final cachedInfo = await getCachedDeviceInfo();
      
      // If cached info is incomplete, reinitialize
      if (cachedInfo['deviceId'] == null || cachedInfo['fcmToken'] == null) {
        log('DeviceInfoService: Cached info incomplete, reinitializing...');
        final freshInfo = await initializeDeviceInfo();
        return {
          'deviceId': freshInfo['deviceId'] ?? 'unknown_device',
          'fcmToken': freshInfo['fcmToken'] ?? 'no_fcm_token',
          'deviceToken': freshInfo['deviceToken'] ?? 'no_device_token',
        };
      }
      
      return {
        'deviceId': cachedInfo['deviceId'] ?? 'unknown_device',
        'fcmToken': cachedInfo['fcmToken'] ?? 'no_fcm_token',
        'deviceToken': cachedInfo['deviceToken'] ?? 'no_device_token',
      };
    } catch (e) {
      log('DeviceInfoService: Error getting device info for API: $e');
      return {
        'deviceId': 'error_device',
        'fcmToken': 'error_fcm_token',
        'deviceToken': 'error_device_token',
      };
    }
  }

  /// Get device platform information
  Future<Map<String, String>> getDevicePlatformInfo() async {
    try {
      if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'platform': 'iOS',
          'model': iosInfo.model,
          'systemVersion': iosInfo.systemVersion,
          'name': iosInfo.name,
        };
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'platform': 'Android',
          'model': androidInfo.model,
          'systemVersion': androidInfo.version.release,
          'manufacturer': androidInfo.manufacturer,
        };
      } else {
        return {
          'platform': 'Unknown',
          'model': 'Unknown',
          'systemVersion': 'Unknown',
          'name': 'Unknown',
        };
      }
    } catch (e) {
      log('DeviceInfoService: Error getting platform info: $e');
      return {
        'platform': 'Error',
        'model': 'Error',
        'systemVersion': 'Error',
        'name': 'Error',
      };
    }
  }

  /// Clear all device info (useful for logout)
  Future<void> clearDeviceInfo() async {
    try {
      await SharedPreferencesService.clearFcmAndDeviceInfo();
      log('DeviceInfoService: Device info cleared');
    } catch (e) {
      log('DeviceInfoService: Error clearing device info: $e');
    }
  }

  /// Debug method to print all device information
  Future<void> debugPrintDeviceInfo() async {
    try {
      log('=== DeviceInfoService Debug Info ===');
      
      final deviceInfo = await getDeviceInfoForApi();
      final platformInfo = await getDevicePlatformInfo();
      
      log('Device ID: ${deviceInfo['deviceId']}');
      log('FCM Token: ${deviceInfo['fcmToken']}');
      log('Device Token: ${deviceInfo['deviceToken']}');
      log('Platform: ${platformInfo['platform']}');
      log('Model: ${platformInfo['model']}');
      log('System Version: ${platformInfo['systemVersion']}');
      
      log('====================================');
    } catch (e) {
      log('DeviceInfoService: Error in debug print: $e');
    }
  }
}
