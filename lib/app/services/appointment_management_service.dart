import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/appointment_management_models.dart';
import 'shared_preferences_service.dart';

/// Service class for appointment management API operations
class AppointmentManagementService {
  static final AppointmentManagementService _instance =
      AppointmentManagementService._internal();
  factory AppointmentManagementService() => _instance;
  AppointmentManagementService._internal();

  /// Get authorization headers with token
  Future<Map<String, String>> _getHeaders() async {
    final token = await SharedPreferencesService.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Get main appointments (pending and confirmed)
  Future<AppointmentsResponse> getMainAppointments() async {
    try {
      log('AppointmentManagementService: Fetching main appointments');

      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse(ApiEndpoints.getSalonAppointments),
        headers: headers,
      );

      log(
        'AppointmentManagementService: Main appointments response status: ${response.statusCode}',
      );
      log(
        'AppointmentManagementService: Main appointments response body: ${response.body}',
      );

      final jsonData = json.decode(response.body) as Map<String, dynamic>;
      final appointmentsResponse = AppointmentsResponse.fromJson(jsonData);

      if (response.statusCode == 200) {
        log(
          'AppointmentManagementService: Successfully fetched main appointments',
        );
        log(
          'AppointmentManagementService: Pending: ${appointmentsResponse.data?.pending.length ?? 0}',
        );
        log(
          'AppointmentManagementService: Confirmed: ${appointmentsResponse.data?.confirmed.length ?? 0}',
        );
        return appointmentsResponse;
      } else {
        log(
          'AppointmentManagementService: Failed to fetch main appointments - Status: ${response.statusCode}',
        );
        return AppointmentsResponse(
          code: response.statusCode,
          success: false,
          message: appointmentsResponse.message.isNotEmpty
              ? appointmentsResponse.message
              : 'Failed to fetch appointments',
        );
      }
    } on FormatException catch (e) {
      log('AppointmentManagementService: JSON parsing error: $e');
      return AppointmentsResponse(
        code: 500,
        success: false,
        message: 'Invalid response format from server',
      );
    } on http.ClientException catch (e) {
      log('AppointmentManagementService: Network error: $e');
      return AppointmentsResponse(
        code: 500,
        success: false,
        message:
            'Network connection error. Please check your internet connection.',
      );
    } catch (e) {
      log('AppointmentManagementService: Unexpected error: $e');
      return AppointmentsResponse(
        code: 500,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get completed appointments
  Future<CompletedAppointmentsResponse> getCompletedAppointments() async {
    try {
      log('AppointmentManagementService: Fetching completed appointments');

      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse(ApiEndpoints.getCompletedAppointments),
        headers: headers,
      );

      log(
        'AppointmentManagementService: Completed appointments response status: ${response.statusCode}',
      );
      log(
        'AppointmentManagementService: Completed appointments response body: ${response.body}',
      );

      final jsonData = json.decode(response.body) as Map<String, dynamic>;
      final completedResponse = CompletedAppointmentsResponse.fromJson(
        jsonData,
      );

      if (response.statusCode == 200) {
        log(
          'AppointmentManagementService: Successfully fetched completed appointments',
        );
        log(
          'AppointmentManagementService: Completed count: ${completedResponse.data.length}',
        );
        return completedResponse;
      } else {
        log(
          'AppointmentManagementService: Failed to fetch completed appointments - Status: ${response.statusCode}',
        );
        return CompletedAppointmentsResponse(
          code: response.statusCode,
          success: false,
          message: completedResponse.message.isNotEmpty
              ? completedResponse.message
              : 'Failed to fetch completed appointments',
          data: [],
        );
      }
    } on FormatException catch (e) {
      log('AppointmentManagementService: JSON parsing error: $e');
      return CompletedAppointmentsResponse(
        code: 500,
        success: false,
        message: 'Invalid response format from server',
        data: [],
      );
    } on http.ClientException catch (e) {
      log('AppointmentManagementService: Network error: $e');
      return CompletedAppointmentsResponse(
        code: 500,
        success: false,
        message:
            'Network connection error. Please check your internet connection.',
        data: [],
      );
    } catch (e) {
      log('AppointmentManagementService: Unexpected error: $e');
      return CompletedAppointmentsResponse(
        code: 500,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
        data: [],
      );
    }
  }

  /// Make appointment decision (Accept/Reject)
  Future<AppointmentActionResponse> makeAppointmentDecision(
    AppointmentDecisionRequest request,
  ) async {
    try {
      log(
        'AppointmentManagementService: Making appointment decision: ${request.toString()}',
      );

      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse(ApiEndpoints.appointmentDecision),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      log(
        'AppointmentManagementService: Decision response status: ${response.statusCode}',
      );
      log(
        'AppointmentManagementService: Decision response body: ${response.body}',
      );

      final jsonData = json.decode(response.body) as Map<String, dynamic>;
      final actionResponse = AppointmentActionResponse.fromJson(jsonData);

      if (response.statusCode == 200) {
        log(
          'AppointmentManagementService: Successfully made appointment decision',
        );
        return actionResponse;
      } else {
        log(
          'AppointmentManagementService: Failed to make appointment decision - Status: ${response.statusCode}',
        );
        return AppointmentActionResponse(
          code: response.statusCode,
          success: false,
          message: actionResponse.message.isNotEmpty
              ? actionResponse.message
              : 'Failed to process appointment decision',
        );
      }
    } on FormatException catch (e) {
      log('AppointmentManagementService: JSON parsing error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message: 'Invalid response format from server',
      );
    } on http.ClientException catch (e) {
      log('AppointmentManagementService: Network error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message:
            'Network connection error. Please check your internet connection.',
      );
    } catch (e) {
      log('AppointmentManagementService: Unexpected error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Start service with OTP verification
  Future<AppointmentActionResponse> startService(
    StartServiceRequest request,
  ) async {
    try {
      log('AppointmentManagementService: ===== START SERVICE API CALL =====');
      log('AppointmentManagementService: Request: ${request.toString()}');
      log(
        'AppointmentManagementService: Request JSON: ${json.encode(request.toJson())}',
      );
      log(
        'AppointmentManagementService: API Endpoint: ${ApiEndpoints.startService}',
      );

      final headers = await _getHeaders();
      log('AppointmentManagementService: Request Headers: $headers');

      // Check if we have a valid token
      final token = await SharedPreferencesService.getToken();
      log(
        'AppointmentManagementService: Auth token present: ${token != null ? "YES" : "NO"}',
      );
      if (token != null) {
        log('AppointmentManagementService: Token length: ${token.length}');
        log(
          'AppointmentManagementService: Token starts with: ${token.substring(0, token.length > 20 ? 20 : token.length)}...',
        );
      }

      final response = await http.post(
        Uri.parse(ApiEndpoints.startService),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      log('AppointmentManagementService: ===== API RESPONSE =====');
      log(
        'AppointmentManagementService: Response status: ${response.statusCode}',
      );
      log(
        'AppointmentManagementService: Response headers: ${response.headers}',
      );
      log('AppointmentManagementService: Response body: ${response.body}');

      // Check if response body is empty
      if (response.body.isEmpty) {
        log('AppointmentManagementService: ERROR - Response body is empty!');
        return AppointmentActionResponse(
          code: response.statusCode,
          success: false,
          message: 'Server returned empty response',
        );
      }

      AppointmentActionResponse actionResponse;
      try {
        final jsonData = json.decode(response.body) as Map<String, dynamic>;
        actionResponse = AppointmentActionResponse.fromJson(jsonData);
        log('AppointmentManagementService: Successfully parsed response JSON');
      } catch (e) {
        log('AppointmentManagementService: ERROR - Failed to parse JSON: $e');
        log(
          'AppointmentManagementService: Raw response that failed to parse: ${response.body}',
        );
        return AppointmentActionResponse(
          code: response.statusCode,
          success: false,
          message: 'Invalid server response format: ${e.toString()}',
        );
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        log('AppointmentManagementService: Successfully started service');
        return actionResponse;
      } else {
        log('AppointmentManagementService: ===== SERVER ERROR =====');
        log(
          'AppointmentManagementService: Failed to start service - Status: ${response.statusCode}',
        );
        log(
          'AppointmentManagementService: Server error message: ${actionResponse.message}',
        );
        log(
          'AppointmentManagementService: Full error response: ${response.body}',
        );
        return AppointmentActionResponse(
          code: response.statusCode,
          success: false,
          message: actionResponse.message.isNotEmpty
              ? actionResponse.message
              : 'Failed to start service',
        );
      }
    } on FormatException catch (e) {
      log('AppointmentManagementService: JSON parsing error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message: 'Invalid response format from server',
      );
    } on http.ClientException catch (e) {
      log('AppointmentManagementService: Network error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message:
            'Network connection error. Please check your internet connection.',
      );
    } catch (e) {
      log('AppointmentManagementService: Unexpected error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Complete service
  Future<AppointmentActionResponse> completeService(
    CompleteServiceRequest request,
  ) async {
    try {
      log(
        'AppointmentManagementService: Completing service: ${request.toString()}',
      );

      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse(ApiEndpoints.completeService),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      log(
        'AppointmentManagementService: Complete service response status: ${response.statusCode}',
      );
      log(
        'AppointmentManagementService: Complete service response body: ${response.body}',
      );

      final jsonData = json.decode(response.body) as Map<String, dynamic>;
      final actionResponse = AppointmentActionResponse.fromJson(jsonData);

      if (response.statusCode == 200) {
        log('AppointmentManagementService: Successfully completed service');
        return actionResponse;
      } else {
        log(
          'AppointmentManagementService: Failed to complete service - Status: ${response.statusCode}',
        );
        return AppointmentActionResponse(
          code: response.statusCode,
          success: false,
          message: actionResponse.message.isNotEmpty
              ? actionResponse.message
              : 'Failed to complete service',
        );
      }
    } on FormatException catch (e) {
      log('AppointmentManagementService: JSON parsing error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message: 'Invalid response format from server',
      );
    } on http.ClientException catch (e) {
      log('AppointmentManagementService: Network error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message:
            'Network connection error. Please check your internet connection.',
      );
    } catch (e) {
      log('AppointmentManagementService: Unexpected error: $e');
      return AppointmentActionResponse(
        code: 500,
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }
}
