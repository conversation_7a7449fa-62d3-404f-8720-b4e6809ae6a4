import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/user_profile_model.dart';
import '../services/shared_preferences_service.dart';

class UserProfileService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Create user and owner profile
  /// Endpoint: /auth/profile/owner
  Future<UserProfileResponse> createUserAndOwnerProfile(
    UserProfileRequest request,
  ) async {
    try {
      final body = jsonEncode(request.toMap());
      final authToken = await SharedPreferencesService.getToken();

      log('Creating user/owner profile with data: $body');

      final response = await http
          .post(
            Uri.parse('${ApiEndpoints.baseUrl}/auth/profile/owner'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${authToken ?? ''}',
            },
            body: body,
          )
          .timeout(_timeoutDuration);

      log('Profile creation response: ${response.body}');
      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection. Please check your network.');
    } on http.ClientException {
      throw Exception('Failed to connect to server. Please try again.');
    } catch (e) {
      log('Profile creation error: $e');
      return UserProfileResponse(
        success: false,
        message: 'Something went wrong: $e',
        saloonId: '',
      );
    }
  }

  /// Update owner profile
  /// Endpoint: /auth/profile/owner/{saloonId}
  Future<UserProfileResponse> updateOwnerProfile(
    String saloonId,
    UserProfileRequest request,
  ) async {
    try {
      final url = Uri.parse(
        '${ApiEndpoints.baseUrl}/auth/profile/owner/$saloonId',
      );
      final authToken = await SharedPreferencesService.getToken();

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (authToken != null && authToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $authToken';
      }

      final body = jsonEncode(request.toMap());
      log('Updating owner profile: $body');

      final response = await http
          .put(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('Owner profile update response: ${response.body}');
      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection. Please check your network.');
    } on http.ClientException {
      throw Exception('Failed to connect to server. Please try again.');
    } catch (e) {
      log('Owner profile update error: $e');
      throw Exception('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Upload user details for regular users (not owners)
  /// Endpoint: /auth/profile/user
  Future<UserProfileResponse> uploadUserDetails({
    required String firstName,
    required String lastName,
    required String phone,
    required String email,
    required String area,
    required String city,
    required String state,
    required String country,
    required String pinCode,
    String? profileImage,
  }) async {
    try {
      final url = Uri.parse('${ApiEndpoints.baseUrl}/auth/profile/user');
      final authToken = await SharedPreferencesService.getToken();

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (authToken != null && authToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $authToken';
      }

      final bodyData = {
        'firstName': firstName.trim(),
        'lastName': lastName.trim(),
        'phone': phone.trim(),
        'email': email.trim(),
        'area': area.trim(),
        'city': city.trim(),
        'state': state.trim(),
        'country': country.trim(),
        'pinCode': pinCode.trim(),
      };

      // Add profile image if provided
      if (profileImage != null && profileImage.isNotEmpty) {
        bodyData['profileImage'] = profileImage;
      }

      final body = jsonEncode(bodyData);
      log('User Details Upload Request: $body');

      final response = await http
          .post(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('User Details Upload Response: ${response.body}');
      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection. Please check your network.');
    } on http.ClientException {
      throw Exception('Failed to connect to server. Please try again.');
    } catch (e) {
      log('User details upload error: $e');
      throw Exception('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Update owner profile with new data structure
  /// Endpoint: /owner/profile
  Future<UserProfileResponse> updateOwnerProfileNew(
    Map<String, dynamic> updateData,
  ) async {
    try {
      final url = Uri.parse('${ApiEndpoints.baseUrl}/owner/profile');
      final authToken = await SharedPreferencesService.getToken();

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (authToken != null && authToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $authToken';
      }

      // Remove null and empty values from updateData
      final cleanedData = <String, dynamic>{};
      updateData.forEach((key, value) {
        if (value != null && value.toString().trim().isNotEmpty) {
          cleanedData[key] = value;
        }
      });

      final body = jsonEncode(cleanedData);
      log('Updating owner profile (new): $body');

      final response = await http
          .put(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('Owner profile update (new) response: ${response.body}');
      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection. Please check your network.');
    } on http.ClientException {
      throw Exception('Failed to connect to server. Please try again.');
    } catch (e) {
      log('Owner profile update (new) error: $e');
      throw Exception('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Create update profile data map with profile image support
  Map<String, dynamic> createUpdateProfileData({
    String? firstName,
    String? lastName,
    String? salonName,
    String? salonDescription,
    String? salonPhone,
    String? salonEmail,
    String? area,
    String? city,
    String? state,
    String? country,
    String? pinCode,
    String? salonRegId,
    String? profileImage,
  }) {
    final updateData = <String, dynamic>{};

    if (firstName != null && firstName.trim().isNotEmpty) {
      updateData['firstName'] = firstName.trim();
    }
    if (lastName != null && lastName.trim().isNotEmpty) {
      updateData['lastName'] = lastName.trim();
    }
    if (salonName != null && salonName.trim().isNotEmpty) {
      updateData['salonName'] = salonName.trim();
    }
    if (salonDescription != null && salonDescription.trim().isNotEmpty) {
      updateData['salonDescription'] = salonDescription.trim();
    }
    if (salonPhone != null && salonPhone.trim().isNotEmpty) {
      updateData['salonPhone'] = salonPhone.trim();
    }
    if (salonEmail != null && salonEmail.trim().isNotEmpty) {
      updateData['salonEmail'] = salonEmail.trim();
    }
    if (area != null && area.trim().isNotEmpty) {
      updateData['area'] = area.trim();
    }
    if (city != null && city.trim().isNotEmpty) {
      updateData['city'] = city.trim();
    }
    if (state != null && state.trim().isNotEmpty) {
      updateData['state'] = state.trim();
    }
    if (country != null && country.trim().isNotEmpty) {
      updateData['country'] = country.trim();
    }
    if (pinCode != null && pinCode.trim().isNotEmpty) {
      updateData['pinCode'] = pinCode.trim();
    }
    if (salonRegId != null && salonRegId.trim().isNotEmpty) {
      updateData['salonRegId'] = salonRegId.trim();
    }
    if (profileImage != null && profileImage.trim().isNotEmpty) {
      updateData['profileImage'] = profileImage.trim();
    }

    return updateData;
  }

  /// Handle HTTP response and return UserProfileResponse
  UserProfileResponse _handleResponse(http.Response response) {
    try {
      final responseData = jsonDecode(response.body);

      switch (response.statusCode) {
        case 200:
        case 201:
          return UserProfileResponse.fromMap(responseData);
        case 400:
          return UserProfileResponse(
            success: false,
            message:
                responseData['message'] ??
                'Bad request. Please check your input.',
            saloonId: '',
          );
        case 401:
          return UserProfileResponse(
            success: false,
            message: 'Unauthorized. Please login again.',
            saloonId: '',
          );
        case 403:
          return UserProfileResponse(
            success: false,
            message: 'Access forbidden. You don\'t have permission.',
            saloonId: '',
          );
        case 404:
          return UserProfileResponse(
            success: false,
            message: 'Service not found. Please try again later.',
            saloonId: '',
          );
        case 422:
          return UserProfileResponse(
            success: false,
            message:
                responseData['message'] ??
                'Validation failed. Please check your input.',
            saloonId: '',
          );
        case 500:
          return UserProfileResponse(
            success: false,
            message: 'Server error. Please try again later.',
            saloonId: '',
          );
        default:
          return UserProfileResponse(
            success: false,
            message: 'Something went wrong. Please try again.',
            saloonId: '',
          );
      }
    } catch (e) {
      log('Response handling error: $e');
      return UserProfileResponse(
        success: false,
        message: 'Failed to process server response.',
        saloonId: '',
      );
    }
  }
}
