import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../services/shared_preferences_service.dart';

class UserDataService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Get user profile data
  /// Endpoint: /auth/profile/user or /auth/profile/owner
  static Future<UserProfileData?> getUserProfile() async {
    try {
      final authToken = await SharedPreferencesService.getToken();
      final userRole = await SharedPreferencesService.getRole();

      if (authToken == null || authToken.isEmpty) {
        log('No auth token found');
        return null;
      }
      log(authToken);
      ;

      // Determine endpoint based on user role
      String endpoint;
      if (userRole == 'owner') {
        endpoint = '${ApiEndpoints.baseUrl}/auth/profile/owner';
      } else {
        endpoint = '${ApiEndpoints.baseUrl}/auth/profile/user';
      }

      final response = await http
          .get(
            Uri.parse(endpoint),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
          )
          .timeout(_timeoutDuration);

      log('User profile response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true && responseData['data'] != null) {
          return UserProfileData.fromJson(responseData['data']);
        }
      }

      return null;
    } on SocketException {
      log('No internet connection');
      return null;
    } on http.ClientException {
      log('Failed to connect to server');
      return null;
    } catch (e) {
      log('Error fetching user profile: $e');
      return null;
    }
  }

  /// Save user profile data to SharedPreferences for quick access
  static Future<void> saveUserProfileToCache(UserProfileData profile) async {
    try {
      // Save user name
      if (profile.fullName.isNotEmpty) {
        await SharedPreferencesService.saveUserName(profile.fullName);
      }

      // Save user location (locality, city)
      if (profile.shortAddress != 'Select your location') {
        await SharedPreferencesService.saveUserLocation(profile.shortAddress);
      }

      // Save email and phone
      if (profile.email.isNotEmpty) {
        await SharedPreferencesService.saveEmail(profile.email);
      }

      if (profile.phone.isNotEmpty) {
        await SharedPreferencesService.savePhone(profile.phone);
      }

      log('User profile cached successfully');
    } catch (e) {
      log('Error saving user profile to cache: $e');
    }
  }
}

class UserProfileData {
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String area;
  final String city;
  final String state;
  final String country;
  final String pinCode;
  final String? profileImage;

  // Owner specific fields
  final String? salonName;
  final String? salonDescription;
  final String? salonPhone;
  final String? salonEmail;
  final String? salonRegId;

  UserProfileData({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.area,
    required this.city,
    required this.state,
    required this.country,
    required this.pinCode,
    this.profileImage,
    this.salonName,
    this.salonDescription,
    this.salonPhone,
    this.salonEmail,
    this.salonRegId,
  });

  factory UserProfileData.fromJson(Map<String, dynamic> json) {
    return UserProfileData(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      area: json['area'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      country: json['country'] ?? '',
      pinCode: json['pinCode'] ?? '',
      profileImage: json['profileImage'],
      salonName: json['salonName'],
      salonDescription: json['salonDescription'],
      salonPhone: json['salonPhone'],
      salonEmail: json['salonEmail'],
      salonRegId: json['salonRegId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'area': area,
      'city': city,
      'state': state,
      'country': country,
      'pinCode': pinCode,
      'profileImage': profileImage,
      'salonName': salonName,
      'salonDescription': salonDescription,
      'salonPhone': salonPhone,
      'salonEmail': salonEmail,
      'salonRegId': salonRegId,
    };
  }

  String get fullName => '$firstName $lastName'.trim();

  String get fullAddress {
    final addressParts = [
      area,
      city,
      state,
    ].where((part) => part.isNotEmpty).toList();
    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Location not set';
  }

  String get shortAddress {
    // For header display, show locality and city
    final addressParts = [area, city].where((part) => part.isNotEmpty).toList();
    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Select your location';
  }
}
