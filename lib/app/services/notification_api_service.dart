import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/notification_model.dart';
import '../models/auth_models.dart';
import '../services/shared_preferences_service.dart';

class NotificationApiService {
  /// Get notifications with pagination
  static Future<NotificationResponse> getNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      log(
        'NotificationApiService: Fetching notifications - Page: $page, Limit: $limit',
      );

      final token = await SharedPreferencesService.getToken();
      log(
        'NotificationApiService: Retrieved token: ${token != null ? "Present" : "NULL"}',
      );

      if (token == null) {
        log('NotificationApiService: ERROR - No authentication token found');
        throw ApiError(message: 'Authentication token not found');
      }

      final uri = Uri.parse(ApiEndpoints.notifications).replace(
        queryParameters: {'page': page.toString(), 'limit': limit.toString()},
      );

      log('NotificationApiService: Request URL: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log('NotificationApiService: ===== RAW API RESPONSE =====');
      log('NotificationApiService: Response status: ${response.statusCode}');
      log('NotificationApiService: Response headers: ${response.headers}');
      log('NotificationApiService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        log('NotificationApiService: ===== PARSED JSON DATA =====');
        log('NotificationApiService: JSON structure: ${jsonData.toString()}');

        final notificationResponse = NotificationResponse.fromJson(jsonData);

        log('NotificationApiService: ===== PARSED RESPONSE OBJECT =====');
        log('NotificationApiService: Success: ${notificationResponse.success}');
        log(
          'NotificationApiService: Notifications count: ${notificationResponse.notifications.length}',
        );
        log(
          'NotificationApiService: Pagination: ${notificationResponse.pagination.toString()}',
        );

        return notificationResponse;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage =
            errorData['message'] ?? 'Failed to fetch notifications';
        log('NotificationApiService: Error - $errorMessage');
        throw ApiError(message: errorMessage);
      }
    } catch (e) {
      log('NotificationApiService: Exception occurred: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(
        message: 'Network error. Please check your connection and try again.',
      );
    }
  }

  /// Mark notification as read
  static Future<MarkAsReadResponse> markAsRead(String notificationId) async {
    try {
      log(
        'NotificationApiService: Marking notification as read - ID: $notificationId',
      );

      final token = await SharedPreferencesService.getToken();
      if (token == null) {
        throw ApiError(message: 'Authentication token not found');
      }

      final uri = Uri.parse(
        ApiEndpoints.notificationMarkAsRead(notificationId),
      );

      log('NotificationApiService: Mark as read URL: $uri');

      final response = await http.patch(
        uri,
        headers: {'Authorization': 'Bearer $token'},
      );

      log(
        'NotificationApiService: Mark as read response status: ${response.statusCode}',
      );
      log(
        'NotificationApiService: Mark as read response body: ${response.body}',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final markAsReadResponse = MarkAsReadResponse.fromJson(jsonData);

        log('NotificationApiService: Successfully marked notification as read');
        return markAsReadResponse;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage =
            errorData['message'] ?? 'Failed to mark notification as read';
        log('NotificationApiService: Error - $errorMessage');
        throw ApiError(message: errorMessage);
      }
    } catch (e) {
      log('NotificationApiService: Exception occurred: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(
        message: 'Network error. Please check your connection and try again.',
      );
    }
  }

  /// Mark all notifications as read
  static Future<MarkAsReadResponse> markAllAsRead() async {
    try {
      log('NotificationApiService: Marking all notifications as read');

      final token = await SharedPreferencesService.getToken();
      if (token == null) {
        throw ApiError(message: 'Authentication token not found');
      }

      final uri = Uri.parse(ApiEndpoints.notificationsReadAll);

      log('NotificationApiService: Mark all as read URL: $uri');

      final response = await http.patch(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log(
        'NotificationApiService: Mark all as read response status: ${response.statusCode}',
      );
      log(
        'NotificationApiService: Mark all as read response body: ${response.body}',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final markAsReadResponse = MarkAsReadResponse.fromJson(jsonData);

        log(
          'NotificationApiService: Successfully marked all notifications as read',
        );
        return markAsReadResponse;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage =
            errorData['message'] ?? 'Failed to mark all notifications as read';
        log('NotificationApiService: Error - $errorMessage');
        throw ApiError(message: errorMessage);
      }
    } catch (e) {
      log('NotificationApiService: Exception occurred: $e');
      if (e is ApiError) {
        rethrow;
      }
      throw ApiError(
        message: 'Network error. Please check your connection and try again.',
      );
    }
  }

  /// Get unread notification count
  static Future<int> getUnreadCount() async {
    try {
      log('NotificationApiService: Getting unread notification count');

      final token = await SharedPreferencesService.getToken();
      if (token == null) {
        throw ApiError(message: 'Authentication token not found');
      }

      final uri = Uri.parse(ApiEndpoints.notificationsUnreadCount);

      log('NotificationApiService: Unread count URL: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      log(
        'NotificationApiService: Unread count response status: ${response.statusCode}',
      );
      log(
        'NotificationApiService: Unread count response body: ${response.body}',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final count = jsonData['data']?['count'] ?? 0;

        log('NotificationApiService: Unread count: $count');
        return count;
      } else {
        log('NotificationApiService: Failed to get unread count, returning 0');
        return 0;
      }
    } catch (e) {
      log(
        'NotificationApiService: Exception occurred while getting unread count: $e',
      );
      return 0;
    }
  }
}
