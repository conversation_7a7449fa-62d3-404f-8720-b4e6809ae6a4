import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/user_profile_models.dart';
import 'shared_preferences_service.dart';

class UserProfileApiService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Get user profile from /user/profile endpoint
  Future<UserProfileResponse> getUserProfile() async {
    try {
      final url = Uri.parse(ApiEndpoints.userProfile);
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        return UserProfileResponse(
          success: false,
          message: 'Authentication token not found',
          error: 'UNAUTHORIZED',
        );
      }

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      log('Getting user profile from: $url');
      log('Headers: $headers');

      final response = await http
          .get(url, headers: headers)
          .timeout(_timeoutDuration);

      log('User Profile Response Status: ${response.statusCode}');
      log('User Profile Response Body: ${response.body}');

      return _handleProfileResponse(response);
    } on SocketException {
      return UserProfileResponse(
        success: false,
        message: 'No internet connection. Please check your network.',
        error: 'NETWORK_ERROR',
      );
    } on http.ClientException {
      return UserProfileResponse(
        success: false,
        message: 'Failed to connect to server. Please try again.',
        error: 'CONNECTION_ERROR',
      );
    } catch (e) {
      log('Error getting user profile: $e');
      return UserProfileResponse(
        success: false,
        message: 'Unexpected error occurred: ${e.toString()}',
        error: 'UNKNOWN_ERROR',
      );
    }
  }

  /// Update user profile
  Future<UserProfileResponse> updateUserProfile(UserProfile profile) async {
    try {
      final url = Uri.parse(ApiEndpoints.userProfile);
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        return UserProfileResponse(
          success: false,
          message: 'Authentication token not found',
          error: 'UNAUTHORIZED',
        );
      }

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      // Remove null values from the profile data
      final profileData = profile.toJson();
      profileData.removeWhere((key, value) => value == null);

      final body = jsonEncode(profileData);

      log('Updating user profile to: $url');
      log('Request body: $body');

      final response = await http
          .put(url, headers: headers, body: body)
          .timeout(_timeoutDuration);

      log('Update Profile Response Status: ${response.statusCode}');
      log('Update Profile Response Body: ${response.body}');

      return _handleProfileResponse(response);
    } on SocketException {
      return UserProfileResponse(
        success: false,
        message: 'No internet connection. Please check your network.',
        error: 'NETWORK_ERROR',
      );
    } on http.ClientException {
      return UserProfileResponse(
        success: false,
        message: 'Failed to connect to server. Please try again.',
        error: 'CONNECTION_ERROR',
      );
    } catch (e) {
      log('Error updating user profile: $e');
      return UserProfileResponse(
        success: false,
        message: 'Unexpected error occurred: ${e.toString()}',
        error: 'UNKNOWN_ERROR',
      );
    }
  }

  /// Upload profile image
  Future<UserProfileResponse> uploadProfileImage(File imageFile) async {
    try {
      final url = Uri.parse(ApiEndpoints.userProfileImage);
      final authToken = await SharedPreferencesService.getToken();

      if (authToken == null || authToken.isEmpty) {
        return UserProfileResponse(
          success: false,
          message: 'Authentication token not found',
          error: 'UNAUTHORIZED',
        );
      }

      var request = http.MultipartRequest('POST', url);
      request.headers.addAll({
        'Authorization': 'Bearer $authToken',
        'Accept': 'application/json',
      });

      request.files.add(
        await http.MultipartFile.fromPath('profileImage', imageFile.path),
      );

      log('Uploading profile image to: $url');

      final streamedResponse = await request.send().timeout(
        const Duration(seconds: 60),
      );

      final response = await http.Response.fromStream(streamedResponse);

      log('Upload Image Response Status: ${response.statusCode}');
      log('Upload Image Response Body: ${response.body}');

      return _handleProfileResponse(response);
    } on SocketException {
      return UserProfileResponse(
        success: false,
        message: 'No internet connection. Please check your network.',
        error: 'NETWORK_ERROR',
      );
    } catch (e) {
      log('Error uploading profile image: $e');
      return UserProfileResponse(
        success: false,
        message: 'Unexpected error occurred: ${e.toString()}',
        error: 'UNKNOWN_ERROR',
      );
    }
  }

  /// Handle API response and convert to UserProfileResponse
  UserProfileResponse _handleProfileResponse(http.Response response) {
    try {
      final responseData = jsonDecode(response.body);

      switch (response.statusCode) {
        case 200:
        case 201:
          // Success response
          if (responseData['success'] == true || response.statusCode == 200) {
            UserProfile? profile;

            // Try to parse profile data from different possible structures
            if (responseData['data'] != null) {
              profile = UserProfile.fromJson(responseData['data']);
            } else if (responseData['user'] != null) {
              profile = UserProfile.fromJson(responseData['user']);
            } else if (responseData['profile'] != null) {
              profile = UserProfile.fromJson(responseData['profile']);
            } else {
              // If no nested data, try parsing the response directly
              profile = UserProfile.fromJson(responseData);
            }

            return UserProfileResponse(
              success: true,
              message: responseData['message'] ?? 'Success',
              data: profile,
            );
          } else {
            return UserProfileResponse(
              success: false,
              message: responseData['message'] ?? 'Operation failed',
              error: 'API_ERROR',
            );
          }

        case 400:
          return UserProfileResponse(
            success: false,
            message:
                responseData['message'] ??
                'Bad request. Please check your input.',
            error: 'BAD_REQUEST',
          );

        case 401:
          return UserProfileResponse(
            success: false,
            message: 'Authentication failed. Please login again.',
            error: 'UNAUTHORIZED',
          );

        case 403:
          return UserProfileResponse(
            success: false,
            message: 'Access forbidden. You don\'t have permission.',
            error: 'FORBIDDEN',
          );

        case 404:
          return UserProfileResponse(
            success: false,
            message: 'Profile not found.',
            error: 'NOT_FOUND',
          );

        case 422:
          return UserProfileResponse(
            success: false,
            message:
                responseData['message'] ??
                'Validation failed. Please check your input.',
            error: 'VALIDATION_ERROR',
          );

        case 500:
          return UserProfileResponse(
            success: false,
            message: 'Server error. Please try again later.',
            error: 'SERVER_ERROR',
          );

        default:
          return UserProfileResponse(
            success: false,
            message:
                responseData['message'] ??
                'Something went wrong. Please try again.',
            error: 'UNKNOWN_ERROR',
          );
      }
    } catch (e) {
      log('Error parsing response: $e');
      return UserProfileResponse(
        success: false,
        message: 'Failed to process server response.',
        error: 'PARSE_ERROR',
      );
    }
  }
}
