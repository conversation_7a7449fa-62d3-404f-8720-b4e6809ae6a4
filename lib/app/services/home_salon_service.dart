import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../services/shared_preferences_service.dart';

class HomeSalonService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Add salon to home salon
  static Future<HomeSalonResponse> addHomeSalon(String saloonId) async {
    try {
      log('HomeSalonService: Adding salon to home salon - ID: $saloonId');

      // Get auth token
      final authToken = await SharedPreferencesService.getToken();
      if (authToken == null || authToken.isEmpty) {
        throw HomeSalonServiceException(
          'Authentication token not found. Please login again.',
          statusCode: 401,
        );
      }

      // Prepare request body
      final requestBody = {'saloonId': saloonId};

      log('HomeSalonService: Request body: ${jsonEncode(requestBody)}');

      // Make API call
      final response = await http
          .post(
            Uri.parse(ApiEndpoints.addHomeSalon),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(_timeoutDuration);

      log('HomeSalonService: Response status: ${response.statusCode}');
      log('HomeSalonService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final homeSalonResponse = HomeSalonResponse.fromString(response.body);

          log(
            'HomeSalonService: Parsed response - success: ${homeSalonResponse.success}',
          );
          log(
            'HomeSalonService: Parsed response - message: ${homeSalonResponse.message}',
          );

          if (homeSalonResponse.success) {
            log('HomeSalonService: Home salon added successfully');
            return homeSalonResponse;
          } else {
            throw HomeSalonServiceException(
              homeSalonResponse.message.isNotEmpty
                  ? homeSalonResponse.message
                  : 'Failed to add home salon',
              statusCode: response.statusCode,
            );
          }
        } catch (e) {
          log('HomeSalonService: Error parsing response: $e');
          throw HomeSalonServiceException(
            'Failed to parse home salon response: $e',
            statusCode: response.statusCode,
            error: e.toString(),
          );
        }
      } else {
        // Handle different HTTP status codes
        final errorMessage = _getErrorMessageFromStatusCode(
          response.statusCode,
        );

        try {
          final errorResponse = jsonDecode(response.body);
          final apiMessage = errorResponse['message'] as String?;

          throw HomeSalonServiceException(
            apiMessage ?? errorMessage,
            statusCode: response.statusCode,
            error: response.body,
          );
        } catch (e) {
          throw HomeSalonServiceException(
            errorMessage,
            statusCode: response.statusCode,
            error: response.body,
          );
        }
      }
    } on HomeSalonServiceException {
      rethrow;
    } catch (e) {
      log('HomeSalonService: Unexpected error: $e');
      throw HomeSalonServiceException(
        'Failed to add home salon: $e',
        error: e.toString(),
      );
    }
  }

  /// Get error message from HTTP status code
  static String _getErrorMessageFromStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Invalid request. Please check salon ID.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission to perform this action.';
      case 404:
        return 'Salon not found.';
      case 409:
        return 'Salon is already added to home salon.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return 'Failed to add home salon. Please try again.';
    }
  }
}

/// Home salon response model
class HomeSalonResponse {
  final bool success;
  final String message;
  final String timestamp;

  HomeSalonResponse({
    required this.success,
    required this.message,
    required this.timestamp,
  });

  factory HomeSalonResponse.fromJson(Map<String, dynamic> json) {
    return HomeSalonResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      timestamp: json['timestamp'] as String? ?? '',
    );
  }

  factory HomeSalonResponse.fromString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return HomeSalonResponse.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message, 'timestamp': timestamp};
  }

  @override
  String toString() {
    return 'HomeSalonResponse(success: $success, message: $message)';
  }
}

/// Home salon service exception
class HomeSalonServiceException implements Exception {
  final String message;
  final int? statusCode;
  final String? error;

  HomeSalonServiceException(this.message, {this.statusCode, this.error});

  @override
  String toString() {
    return 'HomeSalonServiceException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  }
}
