import 'dart:developer';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class PermissionService {
  /// Check current permission statuses (without requesting)
  static Future<void> checkPermissionStatuses() async {
    try {
      log('PermissionService: Checking current permission statuses');

      // Check current permission statuses (without requesting)
      final Map<Permission, PermissionStatus> statuses = {
        Permission.camera: await Permission.camera.status,
        Permission.photos: await Permission.photos.status,
        Permission.location: await Permission.location.status,
        Permission.locationWhenInUse: await Permission.locationWhenInUse.status,
      };

      // Log permission statuses
      statuses.forEach((permission, status) {
        log(
          'PermissionService: ${permission.toString()} = ${status.toString()}',
        );
      });

      log('PermissionService: Permission status check completed');
    } catch (e) {
      log('PermissionService: Error checking permissions: $e');
    }
  }

  /// Initialize and request essential permissions at app startup (DEPRECATED - use just-in-time requests)
  @Deprecated('Use just-in-time permission requests instead')
  static Future<void> initializePermissions() async {
    await checkPermissionStatuses();
  }

  /// Check if camera permission is granted
  static Future<bool> isCameraPermissionGranted() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Check if photos permission is granted
  static Future<bool> isPhotosPermissionGranted() async {
    final status = await Permission.photos.status;
    return status.isGranted;
  }

  /// Check if location permission is granted
  static Future<bool> isLocationPermissionGranted() async {
    final status = await Permission.locationWhenInUse.status;
    return status.isGranted;
  }

  /// Request camera permission specifically
  static Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      log('PermissionService: Camera permission status: $status');

      // If permission is already granted, return true
      if (status.isGranted) {
        return true;
      }

      // If permission is permanently denied, show error and return false
      if (status.isPermanentlyDenied) {
        _showPermissionDeniedSnackbar('Camera', true);
        return false;
      }

      // If permission is denied or not determined, try to request it
      if (status.isDenied || status.isRestricted) {
        final result = await Permission.camera.request();
        log('PermissionService: Camera permission requested, result: $result');

        if (result.isPermanentlyDenied) {
          _showPermissionDeniedSnackbar('Camera', true);
          return false;
        }

        // For denied status, let the image picker handle it (system will show its own dialog)
        if (result.isDenied) {
          log(
            'PermissionService: Camera permission denied, but allowing image picker to handle it',
          );
          return true; // Let the image picker try anyway
        }

        return result.isGranted;
      }

      // For any other status, allow the image picker to try
      return true;
    } catch (e) {
      log('PermissionService: Error requesting camera permission: $e');
      // On error, let the image picker try anyway
      return true;
    }
  }

  /// Request photos permission specifically
  static Future<bool> requestPhotosPermission() async {
    try {
      final status = await Permission.photos.status;
      log('PermissionService: Photos permission status: $status');

      // If permission is already granted, return true
      if (status.isGranted) {
        return true;
      }

      // If permission is permanently denied, show error and return false
      if (status.isPermanentlyDenied) {
        _showPermissionDeniedSnackbar('Photo Library', true);
        return false;
      }

      // If permission is denied or not determined, try to request it
      if (status.isDenied || status.isRestricted) {
        final result = await Permission.photos.request();
        log('PermissionService: Photos permission requested, result: $result');

        if (result.isPermanentlyDenied) {
          _showPermissionDeniedSnackbar('Photo Library', true);
          return false;
        }

        // For denied status, let the image picker handle it (system will show its own dialog)
        if (result.isDenied) {
          log(
            'PermissionService: Photos permission denied, but allowing image picker to handle it',
          );
          return true; // Let the image picker try anyway
        }

        return result.isGranted;
      }

      // For any other status, allow the image picker to try
      return true;
    } catch (e) {
      log('PermissionService: Error requesting photos permission: $e');
      // On error, let the image picker try anyway
      return true;
    }
  }

  /// Show permission denied snackbar
  static void _showPermissionDeniedSnackbar(
    String permissionName,
    bool isPermanent,
  ) {
    Get.snackbar(
      'Permission ${isPermanent ? 'Permanently ' : ''}Denied',
      isPermanent
          ? '$permissionName permission is permanently denied. Please enable it in app settings.'
          : '$permissionName permission is required for this feature.',
      backgroundColor: isPermanent ? Colors.red : Colors.orange,
      colorText: AppConstants.primaryWhite,
      snackPosition: SnackPosition.TOP,
      duration: Duration(seconds: isPermanent ? 5 : 3),
    );
  }
}
