import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/user_appointment_models.dart';
import 'shared_preferences_service.dart';

class UserAppointmentService {
  static final UserAppointmentService _instance =
      UserAppointmentService._internal();
  factory UserAppointmentService() => _instance;
  UserAppointmentService._internal();

  /// Get authorization headers with token
  Future<Map<String, String>> _getHeaders() async {
    final token = await SharedPreferencesService.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Fetch user appointments (upcoming and completed)
  Future<UserAppointmentsResponse> getUserAppointments() async {
    try {
      log('UserAppointmentService: Fetching user appointments');

      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse(ApiEndpoints.getUserAppointments),
        headers: headers,
      );

      log('UserAppointmentService: Response status: ${response.statusCode}');
      log('UserAppointmentService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final appointmentsResponse = UserAppointmentsResponse.fromJson(
          jsonData,
        );

        log(
          'UserAppointmentService: Successfully fetched appointments - '
          'Upcoming: ${appointmentsResponse.data?.upcoming.length ?? 0}, '
          'Completed: ${appointmentsResponse.data?.completed.length ?? 0}',
        );

        return appointmentsResponse;
      } else {
        log(
          'UserAppointmentService: Failed to fetch appointments - Status: ${response.statusCode}',
        );
        return UserAppointmentsResponse(
          code: response.statusCode,
          success: false,
          message: 'Failed to fetch appointments',
        );
      }
    } catch (e) {
      log('UserAppointmentService: Error fetching appointments: $e');
      return UserAppointmentsResponse(
        code: 500,
        success: false,
        message: 'Network error: ${e.toString()}',
      );
    }
  }

  /// Submit review for completed appointment
  Future<SubmitReviewResponse> submitReview(SubmitReviewRequest request) async {
    try {
      log(
        'UserAppointmentService: Submitting review for appointment: ${request.appointmentId}',
      );

      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse(ApiEndpoints.submitSalonReview),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      log(
        'UserAppointmentService: Review submission response status: ${response.statusCode}',
      );
      log(
        'UserAppointmentService: Review submission response body: ${response.body}',
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        final reviewResponse = SubmitReviewResponse.fromJson(jsonData);

        log(
          'UserAppointmentService: Successfully submitted review with ${request.stars} stars',
        );

        return reviewResponse;
      } else {
        log(
          'UserAppointmentService: Failed to submit review - Status: ${response.statusCode}',
        );
        return SubmitReviewResponse(
          code: response.statusCode,
          success: false,
          message: 'Failed to submit review',
        );
      }
    } catch (e) {
      log('UserAppointmentService: Error submitting review: $e');
      return SubmitReviewResponse(
        code: 500,
        success: false,
        message: 'Network error: ${e.toString()}',
      );
    }
  }

  /// Cancel appointment
  Future<Map<String, dynamic>> cancelAppointment(String appointmentId) async {
    try {
      log('UserAppointmentService: Cancelling appointment: $appointmentId');

      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse(ApiEndpoints.cancelAppointment),
        headers: headers,
        body: json.encode({'appointmentId': appointmentId}),
      );

      log(
        'UserAppointmentService: Cancel appointment response status: ${response.statusCode}',
      );
      log(
        'UserAppointmentService: Cancel appointment response body: ${response.body}',
      );

      final jsonData = json.decode(response.body);

      if (response.statusCode == 200) {
        log('UserAppointmentService: Successfully cancelled appointment');
        return {
          'success': true,
          'message':
              jsonData['message'] ?? 'Appointment cancelled successfully',
        };
      } else {
        log(
          'UserAppointmentService: Failed to cancel appointment - Status: ${response.statusCode}',
        );
        return {
          'success': false,
          'message': jsonData['message'] ?? 'Failed to cancel appointment',
        };
      }
    } catch (e) {
      log('UserAppointmentService: Error cancelling appointment: $e');
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Reschedule appointment
  Future<Map<String, dynamic>> rescheduleAppointment({
    required String appointmentId,
    required DateTime newDate,
    required String newTimeSlot,
  }) async {
    try {
      log('UserAppointmentService: Rescheduling appointment: $appointmentId');

      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse(ApiEndpoints.rescheduleAppointment),
        headers: headers,
        body: json.encode({
          'appointmentId': appointmentId,
          'newDate': newDate.toIso8601String(),
          'newTimeSlot': newTimeSlot,
        }),
      );

      log(
        'UserAppointmentService: Reschedule appointment response status: ${response.statusCode}',
      );
      log(
        'UserAppointmentService: Reschedule appointment response body: ${response.body}',
      );

      final jsonData = json.decode(response.body);

      if (response.statusCode == 200) {
        log('UserAppointmentService: Successfully rescheduled appointment');
        return {
          'success': true,
          'message':
              jsonData['message'] ?? 'Appointment rescheduled successfully',
        };
      } else {
        log(
          'UserAppointmentService: Failed to reschedule appointment - Status: ${response.statusCode}',
        );
        return {
          'success': false,
          'message': jsonData['message'] ?? 'Failed to reschedule appointment',
        };
      }
    } catch (e) {
      log('UserAppointmentService: Error rescheduling appointment: $e');
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Get appointment details by ID
  Future<UserAppointment?> getAppointmentById(String appointmentId) async {
    try {
      log(
        'UserAppointmentService: Fetching appointment details: $appointmentId',
      );

      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('${ApiEndpoints.getAppointmentDetails}/$appointmentId'),
        headers: headers,
      );

      log(
        'UserAppointmentService: Get appointment details response status: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] == true && jsonData['data'] != null) {
          return UserAppointment.fromJson(jsonData['data']);
        }
      }

      return null;
    } catch (e) {
      log('UserAppointmentService: Error fetching appointment details: $e');
      return null;
    }
  }
}
