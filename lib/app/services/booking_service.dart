import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/api_endpoints.dart';
import '../models/booking_models.dart';
import '../services/shared_preferences_service.dart';

/// Service class for handling booking-related API calls
class BookingService {
  static const Duration _timeoutDuration = Duration(seconds: 30);

  /// Fetches available time slots for booking
  /// Returns [AvailableSlotsResponse] on success or throws [BookingServiceException] on error
  static Future<AvailableSlotsResponse> getAvailableSlots(
    AvailableSlotsRequest request,
  ) async {
    try {
      if (!request.isValid) {
        throw BookingServiceException(
          'Invalid request data. Please check salon ID, services, and date.',
          statusCode: 400,
        );
      }

      log('BookingService: Fetching available slots for request: $request');

      final authToken = await SharedPreferencesService.getToken();
      log(authToken.toString());

      if (authToken == null || authToken.isEmpty) {
        throw BookingServiceException(
          'Authentication token not found',
          statusCode: 401,
        );
      }

      final requestBody = jsonEncode(request.toJson());
      log('BookingService: Request body: $requestBody');

      final response = await http
          .post(
            Uri.parse(ApiEndpoints.availableSlotsUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $authToken',
            },
            body: requestBody,
          )
          .timeout(_timeoutDuration);

      log('BookingService: Response status: ${response.statusCode}');
      log('BookingService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final slotsResponse = AvailableSlotsResponse.fromString(response.body);

        if (slotsResponse.success) {
          log('BookingService: Available slots fetched successfully');
          log('BookingService: Slots data: ${slotsResponse.data}');
          return slotsResponse;
        } else {
          throw BookingServiceException(
            slotsResponse.message.isNotEmpty
                ? slotsResponse.message
                : 'Failed to fetch available slots',
            statusCode: response.statusCode,
          );
        }
      } else {
        // Handle different HTTP status codes
        final errorMessage = _getErrorMessageFromStatusCode(
          response.statusCode,
        );
        final errorResponse = _parseErrorResponse(response.body);

        throw BookingServiceException(
          errorResponse?.message ?? errorMessage,
          statusCode: response.statusCode,
          error: errorResponse?.error,
        );
      }
    } on SocketException {
      log('BookingService: No internet connection');
      throw BookingServiceException(
        'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } on http.ClientException catch (e) {
      log('BookingService: Client exception: $e');
      throw BookingServiceException(
        'Failed to connect to server. Please try again later.',
        statusCode: 0,
        error: e.toString(),
      );
    } on FormatException catch (e) {
      log('BookingService: Format exception: $e');
      throw BookingServiceException(
        'Invalid response format from server.',
        statusCode: 0,
        error: e.toString(),
      );
    } on BookingServiceException {
      // Re-throw BookingServiceException without wrapping
      rethrow;
    } catch (e) {
      log('BookingService: Unexpected error: $e');
      throw BookingServiceException(
        'An unexpected error occurred. Please try again.',
        statusCode: 0,
        error: e.toString(),
      );
    }
  }

  /// Refreshes available slots with optional force refresh
  static Future<AvailableSlotsResponse> refreshAvailableSlots(
    AvailableSlotsRequest request, {
    bool forceRefresh = false,
  }) async {
    log('BookingService: Refreshing available slots (force: $forceRefresh)');

    // Add any caching logic here if needed
    return await getAvailableSlots(request);
  }

  /// Gets error message based on HTTP status code
  static String _getErrorMessageFromStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your selection and try again.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission to book appointments.';
      case 404:
        return 'No available slots found for the selected date and services.';
      case 409:
        return 'Selected time slot is no longer available. Please choose another time.';
      case 422:
        return 'Invalid booking data. Please check your selection.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Server is temporarily unavailable.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again.';
      default:
        return 'An error occurred (Status: $statusCode). Please try again.';
    }
  }

  /// Parses error response from API
  static BookingErrorResponse? _parseErrorResponse(String responseBody) {
    try {
      if (responseBody.isNotEmpty) {
        return BookingErrorResponse.fromString(responseBody);
      }
    } catch (e) {
      log('BookingService: Failed to parse error response: $e');
    }
    return null;
  }

  /// Validates available slots response data
  static bool validateAvailableSlotsData(AvailableSlotsData? data) {
    if (data == null) return false;

    // Basic validation - should have valid date and salon hours
    return data.date.isNotEmpty && data.salonHours.isValid;
  }

  /// Gets cached available slots data (placeholder for future caching implementation)
  static Future<AvailableSlotsResponse?> getCachedAvailableSlots(
    AvailableSlotsRequest request,
  ) async {
    // TODO: Implement caching mechanism using SharedPreferences or local database
    log(
      'BookingService: Getting cached available slots data (not implemented)',
    );
    return null;
  }

  /// Caches available slots data (placeholder for future caching implementation)
  static Future<void> cacheAvailableSlotsData(
    AvailableSlotsRequest request,
    AvailableSlotsResponse response,
  ) async {
    // TODO: Implement caching mechanism
    log('BookingService: Caching available slots data (not implemented)');
  }

  /// Clears cached available slots data
  static Future<void> clearCachedData() async {
    // TODO: Implement cache clearing
    log(
      'BookingService: Clearing cached available slots data (not implemented)',
    );
  }
}

/// Error response model for handling API errors
class BookingErrorResponse {
  final bool success;
  final String message;
  final String? error;
  final int? statusCode;

  BookingErrorResponse({
    required this.success,
    required this.message,
    this.error,
    this.statusCode,
  });

  factory BookingErrorResponse.fromJson(Map<String, dynamic> json) {
    return BookingErrorResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? 'Unknown error occurred',
      error: json['error'] as String?,
      statusCode: json['statusCode'] as int?,
    );
  }

  factory BookingErrorResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return BookingErrorResponse.fromJson(json);
    } catch (e) {
      return BookingErrorResponse(
        success: false,
        message: 'Failed to parse error response',
        error: e.toString(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'statusCode': statusCode,
    };
  }

  @override
  String toString() {
    return 'BookingErrorResponse(success: $success, message: $message, error: $error)';
  }
}

/// Custom exception class for booking service errors
class BookingServiceException implements Exception {
  final String message;
  final int? statusCode;
  final String? error;

  BookingServiceException(this.message, {this.statusCode, this.error});

  @override
  String toString() {
    return 'BookingServiceException: $message (Status: $statusCode, Error: $error)';
  }

  /// Returns user-friendly error message
  String get userFriendlyMessage {
    if (statusCode == 0) {
      return message; // Network or client errors
    }

    // Handle specific error messages
    if (message.toLowerCase().contains(
      'barber does not provide all requested services',
    )) {
      return 'The selected barber does not offer all the services you chose. Please select different services or choose a different barber.';
    }

    if (message.toLowerCase().contains('no available slots')) {
      return 'No available time slots found for the selected date and services. Please try a different date.';
    }

    if (statusCode == 401) {
      return 'Please login again to continue booking.';
    }

    if (statusCode == 404) {
      return 'No available time slots found. Please try a different date.';
    }

    if (statusCode == 409) {
      return 'Selected time slot is no longer available. Please choose another time.';
    }

    if (statusCode != null && statusCode! >= 500) {
      // For 500 errors, check if we have a specific message from the API
      if (message.isNotEmpty &&
          !message.toLowerCase().contains('internal server error')) {
        return message;
      }
      return 'Server is temporarily unavailable. Please try again later.';
    }

    return message;
  }

  /// Returns whether the error is recoverable (user can retry)
  bool get isRecoverable {
    if (statusCode == null || statusCode == 0) return true; // Network errors

    // Handle specific error messages
    if (message.toLowerCase().contains(
      'barber does not provide all requested services',
    )) {
      return true; // User can select different services or barber
    }

    if (statusCode! >= 500) return true; // Server errors
    if (statusCode == 429) return true; // Rate limiting
    if (statusCode == 404) {
      return true; // No slots found - user can try different date
    }
    if (statusCode == 409) {
      return true; // Conflict - user can select different slot
    }
    return false; // Client errors (4xx) are usually not recoverable
  }
}
