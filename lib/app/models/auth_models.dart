// Login Request Model
class LoginRequest {
  final String email;
  final String password;
  final String role;
  final String? deviceToken;
  final String? fcmToken;
  final String? deviceId;

  LoginRequest({
    required this.email,
    required this.password,
    required this.role,
    this.deviceToken,
    this.fcmToken,
    this.deviceId,
  });

  Map<String, dynamic> toJson() {
    final json = {'email': email, 'password': password, 'role': role};

    // Add device info if available
    if (deviceToken != null && deviceToken!.isNotEmpty) {
      json['deviceToken'] = deviceToken!;
    }
    if (fcmToken != null && fcmToken!.isNotEmpty) {
      json['fcmToken'] = fcmToken!;
    }
    if (deviceId != null && deviceId!.isNotEmpty) {
      json['deviceId'] = deviceId!;
    }

    return json;
  }

  @override
  String toString() {
    return 'LoginRequest(email: $email, role: $role, deviceId: $deviceId)';
  }
}

// Email Register Request Model
class EmailRegisterRequest {
  final String email;
  final String password;
  final String role;
  final String? deviceToken;
  final String? fcmToken;
  final String? deviceId;

  EmailRegisterRequest({
    required this.email,
    required this.password,
    required this.role,
    this.deviceToken,
    this.fcmToken,
    this.deviceId,
  });

  Map<String, dynamic> toJson() {
    final json = {'email': email, 'password': password, 'role': role};

    // Add device info if available
    if (deviceToken != null && deviceToken!.isNotEmpty) {
      json['deviceToken'] = deviceToken!;
    }
    if (fcmToken != null && fcmToken!.isNotEmpty) {
      json['fcmToken'] = fcmToken!;
    }
    if (deviceId != null && deviceId!.isNotEmpty) {
      json['deviceId'] = deviceId!;
    }

    return json;
  }
}

// Phone Register Request Model
class PhoneRegisterRequest {
  final String phone;
  final String password;
  final String role;
  final String? deviceToken;
  final String? fcmToken;
  final String? deviceId;

  PhoneRegisterRequest({
    required this.phone,
    required this.password,
    required this.role,
    this.deviceToken,
    this.fcmToken,
    this.deviceId,
  });

  Map<String, dynamic> toJson() {
    final json = {'phone': phone, 'password': password, 'role': role};

    // Add device info if available
    if (deviceToken != null && deviceToken!.isNotEmpty) {
      json['deviceToken'] = deviceToken!;
    }
    if (fcmToken != null && fcmToken!.isNotEmpty) {
      json['fcmToken'] = fcmToken!;
    }
    if (deviceId != null && deviceId!.isNotEmpty) {
      json['deviceId'] = deviceId!;
    }

    return json;
  }
}

// Resend OTP Request Model
class ResendOtpRequest {
  final String identifier;

  ResendOtpRequest({required this.identifier});

  Map<String, dynamic> toJson() {
    return {'identifier': identifier};
  }
}

// Verify OTP Request Model
class VerifyOtpRequest {
  final String identifier;
  final String otp;

  VerifyOtpRequest({required this.identifier, required this.otp});

  Map<String, dynamic> toJson() {
    return {'identifier': identifier, 'otp': otp};
  }
}

// Login Response Model
class LoginResponse {
  final int code;
  final bool success;
  final String message;
  final String? token;
  final String? role;
  final AuthData? data;

  LoginResponse({
    required this.code,
    required this.success,
    required this.message,
    this.token,
    this.role,
    this.data,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // Handle different response structures
    String? token;
    String? role;
    AuthData? authData;

    // Check if token and role are at root level
    if (json['token'] != null) {
      token = json['token'] as String?;
      role = json['role'] as String?;
      // Create AuthData from root level fields
      authData = AuthData(
        isVerified: json['isVerified'] as bool? ?? false,
        isProfileCompleted: json['isProfileCompleted'] as bool? ?? false,
      );
    }
    // Check if they are inside data object
    else if (json['data'] != null) {
      final dataObj = json['data'] as Map<String, dynamic>;
      token = dataObj['token'] as String?;
      role = dataObj['role'] as String?;

      // Create AuthData from data object fields
      authData = AuthData(
        isVerified: dataObj['isVerified'] as bool? ?? false,
        isProfileCompleted: dataObj['isProfileCompleted'] as bool? ?? false,
      );
    }

    return LoginResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      token: token,
      role: role,
      data: authData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'token': token,
      'role': role,
      'data': data?.toJson(),
    };
  }

  @override
  String toString() {
    return 'LoginResponse(code: $code, success: $success, message: $message, role: $role, isVerified: ${data?.isVerified}, isProfileCompleted: ${data?.isProfileCompleted})';
  }
}

// Auth Data Model for login/verify responses
class AuthData {
  final bool isVerified;
  final bool isProfileCompleted;

  AuthData({required this.isVerified, required this.isProfileCompleted});

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      isVerified: json['isVerified'] as bool? ?? false,
      isProfileCompleted: json['isProfileCompleted'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {'isVerified': isVerified, 'isProfileCompleted': isProfileCompleted};
  }

  @override
  String toString() {
    return 'AuthData(isVerified: $isVerified, isProfileCompleted: $isProfileCompleted)';
  }
}

// Auth Response Model
class AuthResponse {
  final int code;
  final bool success;
  final String message;
  final String? token;
  final String? role;
  final AuthData? data;

  AuthResponse({
    required this.code,
    required this.success,
    required this.message,
    this.token,
    this.role,
    this.data,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    bool inferredSuccess = false;
    if (json.containsKey('success')) {
      inferredSuccess = json['success'] ?? false;
    } else if (json['message'] != null) {
      final msg = json['message'].toString().toLowerCase();
      if (msg.contains('otp sent') ||
          msg.contains('otp verified') ||
          msg.contains('success')) {
        inferredSuccess = true;
      }
    }
    // If token is present, also treat as success
    if (json['token'] != null && json['token'].toString().isNotEmpty) {
      inferredSuccess = true;
    }

    // Handle different response structures
    String? token;
    String? role;
    AuthData? authData;

    // Check if token and role are at root level
    if (json['token'] != null) {
      token = json['token'];
      role = json['role'];
      // Create AuthData from root level fields
      authData = AuthData(
        isVerified: json['isVerified'] ?? false,
        isProfileCompleted: json['isProfileCompleted'] ?? false,
      );
    }
    // Check if they are inside data object
    else if (json['data'] != null) {
      final dataObj = json['data'] as Map<String, dynamic>;
      token = dataObj['token'];
      role = dataObj['role'];

      // Check if isVerified/isProfileCompleted are in data object
      if (dataObj.containsKey('isVerified') ||
          dataObj.containsKey('isProfileCompleted')) {
        authData = AuthData(
          isVerified: dataObj['isVerified'] ?? false,
          isProfileCompleted: dataObj['isProfileCompleted'] ?? false,
        );
      } else {
        // Try to parse as nested AuthData
        authData = AuthData.fromJson(dataObj);
      }
    }

    return AuthResponse(
      code: json['code'] as int? ?? 0,
      success: inferredSuccess,
      message: json['message'] ?? '',
      token: token,
      role: role,
      data: authData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'token': token,
      'role': role,
      'data': data?.toJson(),
    };
  }

  @override
  String toString() {
    return 'AuthResponse(code: $code, success: $success, message: $message, role: $role, isVerified: ${data?.isVerified}, isProfileCompleted: ${data?.isProfileCompleted})';
  }
}

// API Error Model
class ApiError implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiError({required this.message, this.statusCode, this.errors});

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      message: json['message'] ?? 'An error occurred',
      statusCode: json['statusCode'],
      errors: json['errors'],
    );
  }

  @override
  String toString() {
    return 'ApiError: $message';
  }
}
