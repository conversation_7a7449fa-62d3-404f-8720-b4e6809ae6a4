/// User Profile Model
class UserProfile {
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? profileImage;
  final String? dateOfBirth;
  final String? gender;
  final Address? address;
  final Preferences? preferences;
  final String? createdAt;
  final String? updatedAt;
  final bool? isEmailVerified;
  final bool? isPhoneVerified;

  UserProfile({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.profileImage,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.preferences,
    this.createdAt,
    this.updatedAt,
    this.isEmailVerified,
    this.isPhoneVerified,
  });

  /// Get full name
  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();

  /// Get initials for avatar
  String get initials {
    final first = firstName?.isNotEmpty == true ? firstName![0] : '';
    final last = lastName?.isNotEmpty == true ? lastName![0] : '';
    return '$first$last'.toUpperCase();
  }

  /// Check if profile has image
  bool get hasProfileImage => profileImage != null && profileImage!.isNotEmpty;

  /// Get display name (fallback to email if no name)
  String get displayName {
    if (fullName.isNotEmpty) return fullName;
    if (email != null && email!.isNotEmpty) return email!;
    return 'User';
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id']?.toString(),
      firstName: json['firstName'] ?? json['first_name'],
      lastName: json['lastName'] ?? json['last_name'],
      email: json['email'],
      phone: json['phone'],
      profileImage: json['profileImage'] ?? json['profile_image'],
      dateOfBirth: json['dateOfBirth'] ?? json['date_of_birth'],
      gender: json['gender'],
      address: json['address'] != null
          ? Address.fromJson(json['address'])
          : null,
      preferences: json['preferences'] != null
          ? Preferences.fromJson(json['preferences'])
          : null,
      createdAt: json['createdAt'] ?? json['created_at'],
      updatedAt: json['updatedAt'] ?? json['updated_at'],
      isEmailVerified:
          json['isEmailVerified'] ?? json['is_email_verified'] ?? false,
      isPhoneVerified:
          json['isPhoneVerified'] ?? json['is_phone_verified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'address': address?.toJson(),
      'preferences': preferences?.toJson(),
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
    };
  }

  UserProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? profileImage,
    String? dateOfBirth,
    String? gender,
    Address? address,
    Preferences? preferences,
    String? createdAt,
    String? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
  }) {
    return UserProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
    );
  }

  @override
  String toString() {
    return 'UserProfile(id: $id, firstName: $firstName, lastName: $lastName, email: $email, phone: $phone)';
  }
}

/// Address Model
class Address {
  final String? street;
  final String? city;
  final String? state;
  final String? country;
  final String? pinCode;
  final String? area;
  final double? latitude;
  final double? longitude;

  Address({
    this.street,
    this.city,
    this.state,
    this.country,
    this.pinCode,
    this.area,
    this.latitude,
    this.longitude,
  });

  String get fullAddress {
    final parts = [
      street,
      area,
      city,
      state,
      country,
      pinCode,
    ].where((part) => part?.isNotEmpty == true).toList();
    return parts.join(', ');
  }

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      pinCode: json['pinCode'] ?? json['pin_code'],
      area: json['area'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'country': country,
      'pinCode': pinCode,
      'area': area,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

/// User Preferences Model
class Preferences {
  final bool? notifications;
  final bool? emailNotifications;
  final bool? smsNotifications;
  final String? language;
  final String? currency;
  final String? theme;

  Preferences({
    this.notifications,
    this.emailNotifications,
    this.smsNotifications,
    this.language,
    this.currency,
    this.theme,
  });

  factory Preferences.fromJson(Map<String, dynamic> json) {
    return Preferences(
      notifications: json['notifications'] ?? true,
      emailNotifications:
          json['emailNotifications'] ?? json['email_notifications'] ?? true,
      smsNotifications:
          json['smsNotifications'] ?? json['sms_notifications'] ?? true,
      language: json['language'] ?? 'en',
      currency: json['currency'] ?? 'INR',
      theme: json['theme'] ?? 'dark',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notifications': notifications,
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'language': language,
      'currency': currency,
      'theme': theme,
    };
  }
}

/// API Response wrapper
class UserProfileResponse {
  final bool success;
  final String message;
  final UserProfile? data;
  final String? error;

  UserProfileResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
  });

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) {
    return UserProfileResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? UserProfile.fromJson(json['data']) : null,
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
      'error': error,
    };
  }
}
