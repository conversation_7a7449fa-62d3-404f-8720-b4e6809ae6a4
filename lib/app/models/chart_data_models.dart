import 'dart:convert';
import 'dart:developer';

/// Weekly Chart Data Response Model
class WeeklyChartResponse {
  final bool success;
  final int code;
  final String message;
  final List<WeeklyDataPoint> data;

  WeeklyChartResponse({
    required this.success,
    required this.code,
    required this.message,
    required this.data,
  });

  factory WeeklyChartResponse.fromJson(Map<String, dynamic> json) {
    return WeeklyChartResponse(
      success: json['success'] as bool? ?? false,
      code: json['code'] as int? ?? 0,
      message: json['message'] as String? ?? '',
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => WeeklyDataPoint.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'code': code,
      'message': message,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }

  // Helper methods for chart integration
  List<double> get earningsData => data.map((point) => point.totalEarning.toDouble()).toList();
  List<double> get bookingsData => data.map((point) => point.totalBookings.toDouble()).toList();
  
  double get totalEarnings => data.fold(0.0, (sum, point) => sum + point.totalEarning);
  int get totalBookings => data.fold(0, (sum, point) => sum + point.totalBookings);

  @override
  String toString() {
    return 'WeeklyChartResponse(success: $success, dataPoints: ${data.length})';
  }
}

/// Weekly Data Point Model
class WeeklyDataPoint {
  final DateTime week;
  final int totalBookings;
  final double totalEarning;

  WeeklyDataPoint({
    required this.week,
    required this.totalBookings,
    required this.totalEarning,
  });

  factory WeeklyDataPoint.fromJson(Map<String, dynamic> json) {
    return WeeklyDataPoint(
      week: _parseDateTime(json['week']),
      totalBookings: json['totalBookings'] as int? ?? 0,
      totalEarning: _parseDouble(json['totalEarning']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'week': week.toIso8601String(),
      'totalBookings': totalBookings,
      'totalEarning': totalEarning,
    };
  }

  // Helper getter for display
  String get weekLabel {
    final weekStart = week;
    final weekEnd = weekStart.add(const Duration(days: 6));
    return '${weekStart.day}/${weekStart.month} - ${weekEnd.day}/${weekEnd.month}';
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        log('Error parsing date: $value');
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  @override
  String toString() {
    return 'WeeklyDataPoint(week: $week, bookings: $totalBookings, earning: $totalEarning)';
  }
}

/// Monthly Chart Data Response Model
class MonthlyChartResponse {
  final bool success;
  final int code;
  final String message;
  final List<MonthlyDataPoint> data;

  MonthlyChartResponse({
    required this.success,
    required this.code,
    required this.message,
    required this.data,
  });

  factory MonthlyChartResponse.fromJson(Map<String, dynamic> json) {
    return MonthlyChartResponse(
      success: json['success'] as bool? ?? false,
      code: json['code'] as int? ?? 0,
      message: json['message'] as String? ?? '',
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => MonthlyDataPoint.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'code': code,
      'message': message,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }

  // Helper methods for chart integration
  List<double> get earningsData => data.map((point) => point.totalEarning.toDouble()).toList();
  List<double> get bookingsData => data.map((point) => point.totalBookings.toDouble()).toList();
  
  double get totalEarnings => data.fold(0.0, (sum, point) => sum + point.totalEarning);
  int get totalBookings => data.fold(0, (sum, point) => sum + point.totalBookings);

  @override
  String toString() {
    return 'MonthlyChartResponse(success: $success, dataPoints: ${data.length})';
  }
}

/// Monthly Data Point Model
class MonthlyDataPoint {
  final DateTime month;
  final int totalBookings;
  final double totalEarning;

  MonthlyDataPoint({
    required this.month,
    required this.totalBookings,
    required this.totalEarning,
  });

  factory MonthlyDataPoint.fromJson(Map<String, dynamic> json) {
    return MonthlyDataPoint(
      month: _parseDateTime(json['month']),
      totalBookings: json['totalBookings'] as int? ?? 0,
      totalEarning: _parseDouble(json['totalEarning']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month.toIso8601String(),
      'totalBookings': totalBookings,
      'totalEarning': totalEarning,
    };
  }

  // Helper getter for display
  String get monthLabel {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month.month - 1];
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        log('Error parsing date: $value');
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  @override
  String toString() {
    return 'MonthlyDataPoint(month: $month, bookings: $totalBookings, earning: $totalEarning)';
  }
}

/// Chart Statistics Helper Class
class ChartStatistics {
  static double calculateGrowthPercentage(List<double> data) {
    if (data.length < 2) return 0.0;
    
    final current = data.last;
    final previous = data[data.length - 2];
    
    if (previous == 0) return current > 0 ? 100.0 : 0.0;
    
    return ((current - previous) / previous) * 100;
  }

  static String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(0)}';
  }

  static String formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    }
    return number.toString();
  }
}
