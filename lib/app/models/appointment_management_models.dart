// Appointment Management Models for Owner Side

/// Main appointment model
class OwnerAppointment {
  final String id;
  final String userName;
  final String barberName;
  final double amount;
  final String bookingType;
  final String status;
  final String appointmentDate;
  final String startTime;
  final String endTime;
  final List<AppointmentService> services;
  final String? otp;
  final DateTime createdAt;
  final DateTime updatedAt;

  OwnerAppointment({
    required this.id,
    required this.userName,
    required this.barberName,
    required this.amount,
    required this.bookingType,
    required this.status,
    required this.appointmentDate,
    required this.startTime,
    required this.endTime,
    required this.services,
    this.otp,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OwnerAppointment.fromJson(Map<String, dynamic> json) {
    return OwnerAppointment(
      id: json['id']?.toString() ?? '',
      userName: json['userName']?.toString() ?? 'Unknown User',
      barberName: json['barberName']?.toString() ?? 'Unknown Barber',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      bookingType: json['bookingType']?.toString() ?? 'Unknown',
      status: json['status']?.toString() ?? 'pending',
      appointmentDate: json['appointmentDate']?.toString() ?? '',
      startTime: json['startTime']?.toString() ?? '',
      endTime: json['endTime']?.toString() ?? '',
      services:
          (json['services'] as List<dynamic>?)
              ?.map(
                (service) => AppointmentService.fromJson(
                  service as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      otp: json['otp']?.toString(),
      createdAt:
          DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
      updatedAt:
          DateTime.tryParse(json['updatedAt']?.toString() ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName,
      'barberName': barberName,
      'amount': amount,
      'bookingType': bookingType,
      'status': status,
      'appointmentDate': appointmentDate,
      'startTime': startTime,
      'endTime': endTime,
      'services': services.map((service) => service.toJson()).toList(),
      'otp': otp,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Get formatted date for display (e.g., "30 Dec 2025")
  String get formattedDate {
    try {
      final date = DateTime.parse(appointmentDate);
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${date.day} ${months[date.month - 1]} ${date.year}';
    } catch (e) {
      return appointmentDate;
    }
  }

  /// Get formatted time range (e.g., "10:00 AM - 11:30 AM")
  String get formattedTimeRange {
    return '${_formatTime(startTime)} - ${_formatTime(endTime)}';
  }

  /// Calculate service duration in minutes
  int get durationInMinutes {
    try {
      final start = _parseTime(startTime);
      final end = _parseTime(endTime);
      return end.difference(start).inMinutes;
    } catch (e) {
      return 0;
    }
  }

  /// Get formatted duration (e.g., "1h 30m")
  String get formattedDuration {
    final minutes = durationInMinutes;
    if (minutes == 0) return 'N/A';

    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;

    if (hours > 0 && remainingMinutes > 0) {
      return '${hours}h ${remainingMinutes}m';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${remainingMinutes}m';
    }
  }

  /// Get service names as comma-separated string
  String get serviceNames {
    if (services.isEmpty) return 'No services';
    return services.map((service) => service.serviceName).join(', ');
  }

  /// Helper method to format time to 12-hour format
  String _formatTime(String time24) {
    try {
      final parts = time24.split(':');
      if (parts.length < 2) return time24;

      final hour = int.parse(parts[0]);
      final minute = parts[1];

      if (hour == 0) {
        return '12:$minute AM';
      } else if (hour < 12) {
        return '$hour:$minute AM';
      } else if (hour == 12) {
        return '12:$minute PM';
      } else {
        return '${hour - 12}:$minute PM';
      }
    } catch (e) {
      return time24;
    }
  }

  /// Helper method to parse time string to DateTime
  DateTime _parseTime(String timeStr) {
    final parts = timeStr.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }

  @override
  String toString() {
    return 'OwnerAppointment(id: $id, userName: $userName, status: $status, date: $appointmentDate)';
  }
}

/// Appointment service model
class AppointmentService {
  final String id;
  final String serviceName;
  final double price;
  final int duration;

  AppointmentService({
    required this.id,
    required this.serviceName,
    required this.price,
    required this.duration,
  });

  factory AppointmentService.fromJson(Map<String, dynamic> json) {
    return AppointmentService(
      id: json['id']?.toString() ?? '',
      serviceName: json['serviceName']?.toString() ?? 'Unknown Service',
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      duration: (json['duration'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'serviceName': serviceName,
      'price': price,
      'duration': duration,
    };
  }
}

/// Appointments response model
class AppointmentsResponse {
  final int code;
  final bool success;
  final String message;
  final AppointmentsData? data;

  AppointmentsResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory AppointmentsResponse.fromJson(Map<String, dynamic> json) {
    // Handle code field that can be either String or int
    int code = 0;
    if (json['code'] != null) {
      if (json['code'] is int) {
        code = json['code'] as int;
      } else if (json['code'] is String) {
        code = int.tryParse(json['code'] as String) ?? 0;
      }
    }

    return AppointmentsResponse(
      code: code,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null
          ? AppointmentsData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }

  bool get isSuccess => success && code == 200;
}

/// Appointments data model
class AppointmentsData {
  final List<OwnerAppointment> pending;
  final List<OwnerAppointment> confirmed;
  final List<OwnerAppointment> inProgress;

  AppointmentsData({
    required this.pending,
    required this.confirmed,
    required this.inProgress,
  });

  factory AppointmentsData.fromJson(Map<String, dynamic> json) {
    return AppointmentsData(
      pending:
          (json['pending'] as List<dynamic>?)
              ?.map(
                (appointment) => OwnerAppointment.fromJson(
                  appointment as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      confirmed:
          (json['confirmed'] as List<dynamic>?)
              ?.map(
                (appointment) => OwnerAppointment.fromJson(
                  appointment as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      inProgress:
          (json['in_progress'] as List<dynamic>?)
              ?.map(
                (appointment) => OwnerAppointment.fromJson(
                  appointment as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pending': pending.map((appointment) => appointment.toJson()).toList(),
      'confirmed': confirmed
          .map((appointment) => appointment.toJson())
          .toList(),
      'in_progress': inProgress
          .map((appointment) => appointment.toJson())
          .toList(),
    };
  }
}

/// Completed appointments response model
class CompletedAppointmentsResponse {
  final int code;
  final bool success;
  final String message;
  final List<OwnerAppointment> data;

  CompletedAppointmentsResponse({
    required this.code,
    required this.success,
    required this.message,
    required this.data,
  });

  factory CompletedAppointmentsResponse.fromJson(Map<String, dynamic> json) {
    // Handle code field that can be either String or int
    int code = 0;
    if (json['code'] != null) {
      if (json['code'] is int) {
        code = json['code'] as int;
      } else if (json['code'] is String) {
        code = int.tryParse(json['code'] as String) ?? 0;
      }
    }

    return CompletedAppointmentsResponse(
      code: code,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data:
          (json['data'] as List<dynamic>?)
              ?.map(
                (appointment) => OwnerAppointment.fromJson(
                  appointment as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data.map((appointment) => appointment.toJson()).toList(),
    };
  }

  bool get isSuccess => success && code == 200;
}

/// Appointment decision request model
class AppointmentDecisionRequest {
  final String appointmentId;
  final String decision; // "Accepted" or "Rejected"

  AppointmentDecisionRequest({
    required this.appointmentId,
    required this.decision,
  });

  Map<String, dynamic> toJson() {
    return {'appointmentId': appointmentId, 'decision': decision};
  }

  @override
  String toString() {
    return 'AppointmentDecisionRequest(appointmentId: $appointmentId, decision: $decision)';
  }
}

/// Start service request model
class StartServiceRequest {
  final String appointmentId;
  final String otp;

  StartServiceRequest({required this.appointmentId, required this.otp});

  Map<String, dynamic> toJson() {
    return {'appointmentId': appointmentId, 'otp': otp};
  }

  @override
  String toString() {
    return 'StartServiceRequest(appointmentId: $appointmentId, otp: $otp)';
  }
}

/// Complete service request model
class CompleteServiceRequest {
  final String appointmentId;

  CompleteServiceRequest({required this.appointmentId});

  Map<String, dynamic> toJson() {
    return {'appointmentId': appointmentId};
  }

  @override
  String toString() {
    return 'CompleteServiceRequest(appointmentId: $appointmentId)';
  }
}

/// Generic appointment action response model
class AppointmentActionResponse {
  final int code;
  final bool success;
  final String message;

  AppointmentActionResponse({
    required this.code,
    required this.success,
    required this.message,
  });

  factory AppointmentActionResponse.fromJson(Map<String, dynamic> json) {
    // Handle code field that can be either String or int
    int code = 0;
    if (json['code'] != null) {
      if (json['code'] is int) {
        code = json['code'] as int;
      } else if (json['code'] is String) {
        code = int.tryParse(json['code'] as String) ?? 0;
      }
    }

    return AppointmentActionResponse(
      code: code,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'code': code, 'success': success, 'message': message};
  }

  bool get isSuccess => success && code == 200;

  /// Get user-friendly error message
  String get userFriendlyMessage {
    if (isSuccess) return message;

    // Handle common error cases
    switch (code) {
      case 400:
        return 'Invalid request. Please check your input.';
      case 401:
        return 'You are not authorized to perform this action.';
      case 404:
        return 'Appointment not found.';
      case 409:
        return 'This action cannot be performed on the current appointment status.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return message.isNotEmpty
            ? message
            : 'An error occurred. Please try again.';
    }
  }

  @override
  String toString() {
    return 'AppointmentActionResponse(code: $code, success: $success, message: $message)';
  }
}
