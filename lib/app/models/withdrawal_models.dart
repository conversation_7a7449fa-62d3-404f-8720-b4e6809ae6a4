import 'dart:developer';

/// Withdrawal Request Model
class WithdrawalRequest {
  final double amount;
  final String withdrawalMethod;
  final String bankAccountNumber;
  final String bankIfscCode;
  final String bankAccountName;
  final String? upiId;

  WithdrawalRequest({
    required this.amount,
    required this.withdrawalMethod,
    required this.bankAccountNumber,
    required this.bankIfscCode,
    required this.bankAccountName,
    this.upiId,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'withdrawalMethod': withdrawalMethod,
      'bankAccountNumber': bankAccountNumber,
      'bankIfscCode': bankIfscCode,
      'bankAccountName': bankAccountName,
      'upiId': upiId ?? 'null',
    };
  }

  @override
  String toString() {
    return 'WithdrawalRequest(amount: $amount, method: $withdrawalMethod, account: $bankAccountNumber)';
  }
}

/// Withdrawal Response Model
class WithdrawalResponse {
  final int code;
  final bool success;
  final WithdrawalData? data;
  final String? message;

  WithdrawalResponse({
    required this.code,
    required this.success,
    this.data,
    this.message,
  });

  factory WithdrawalResponse.fromJson(Map<String, dynamic> json) {
    return WithdrawalResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      data: json['data'] != null
          ? WithdrawalData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
      message: json['message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'data': data?.toJson(),
      'message': message,
    };
  }

  @override
  String toString() {
    return 'WithdrawalResponse(code: $code, success: $success, message: $message)';
  }
}

/// Withdrawal Data Model
class WithdrawalData {
  final String withdrawalId;
  final double requestedAmount;
  final double processedAmount;
  final double charges;
  final String status;

  WithdrawalData({
    required this.withdrawalId,
    required this.requestedAmount,
    required this.processedAmount,
    required this.charges,
    required this.status,
  });

  factory WithdrawalData.fromJson(Map<String, dynamic> json) {
    return WithdrawalData(
      withdrawalId: json['withdrawalId'] as String? ?? '',
      requestedAmount: _parseDouble(json['requestedAmount']),
      processedAmount: _parseDouble(json['processedAmount']),
      charges: _parseDouble(json['charges']),
      status: json['status'] as String? ?? 'unknown',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'withdrawalId': withdrawalId,
      'requestedAmount': requestedAmount,
      'processedAmount': processedAmount,
      'charges': charges,
      'status': status,
    };
  }

  // Helper getters
  String get formattedRequestedAmount => '₹${requestedAmount.toStringAsFixed(2)}';
  String get formattedProcessedAmount => '₹${processedAmount.toStringAsFixed(2)}';
  String get formattedCharges => '₹${charges.toStringAsFixed(2)}';
  String get displayStatus => status.toUpperCase();
  
  bool get isPending => status.toLowerCase() == 'pending';
  bool get isCompleted => status.toLowerCase() == 'completed';
  bool get isFailed => status.toLowerCase() == 'failed';

  @override
  String toString() {
    return 'WithdrawalData(id: $withdrawalId, amount: $requestedAmount, status: $status)';
  }
}

/// Withdrawal Method Enum
enum WithdrawalMethod {
  bankTransfer('bank_transfer', 'Bank Transfer'),
  upi('upi', 'UPI');

  const WithdrawalMethod(this.value, this.displayName);
  
  final String value;
  final String displayName;
}
