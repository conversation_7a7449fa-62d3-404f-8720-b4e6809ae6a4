import 'dart:convert';

/// Main salon detail response model
class SalonDetailResponse {
  final int code;
  final bool success;
  final String message;
  final SalonDetailData? data;

  SalonDetailResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory SalonDetailResponse.fromJson(Map<String, dynamic> json) {
    return SalonDetailResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null
          ? SalonDetailData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  factory SalonDetailResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return SalonDetailResponse.fromJson(json);
    } catch (e) {
      return SalonDetailResponse(
        code: 0,
        success: false,
        message: 'Failed to parse response',
        data: null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }

  @override
  String toString() {
    return 'SalonDetailResponse(code: $code, success: $success, message: $message)';
  }
}

/// Salon detail data containing all information
class SalonDetailData {
  final SalonData? saloonData;
  final List<SalonService> saloonServices;
  final List<SalonBarber> saloonBarbers;
  final List<SalonReview> saloonReviews;

  SalonDetailData({
    this.saloonData,
    required this.saloonServices,
    required this.saloonBarbers,
    required this.saloonReviews,
  });

  factory SalonDetailData.fromJson(Map<String, dynamic> json) {
    return SalonDetailData(
      saloonData: json['saloonData'] != null
          ? SalonData.fromJson(json['saloonData'] as Map<String, dynamic>)
          : null,
      saloonServices: _parseServiceList(json['saloonServices']),
      saloonBarbers: _parseBarberList(json['saloonBarbers']),
      saloonReviews: _parseReviewList(json['saloonReviews']),
    );
  }

  static List<SalonService> _parseServiceList(dynamic serviceData) {
    if (serviceData == null) return [];
    if (serviceData is List) {
      return serviceData
          .where((item) => item != null)
          .map((item) => SalonService.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  static List<SalonBarber> _parseBarberList(dynamic barberData) {
    if (barberData == null) return [];
    if (barberData is List) {
      return barberData
          .where((item) => item != null)
          .map((item) => SalonBarber.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  static List<SalonReview> _parseReviewList(dynamic reviewData) {
    if (reviewData == null) return [];
    if (reviewData is List) {
      return reviewData
          .where((item) => item != null)
          .map((item) => SalonReview.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'saloonData': saloonData?.toJson(),
      'saloonServices': saloonServices
          .map((service) => service.toJson())
          .toList(),
      'saloonBarbers': saloonBarbers.map((barber) => barber.toJson()).toList(),
      'saloonReviews': saloonReviews.map((review) => review.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'SalonDetailData(services: ${saloonServices.length}, barbers: ${saloonBarbers.length}, reviews: ${saloonReviews.length})';
  }
}

/// Salon basic information model
class SalonData {
  final String id;
  final String saloonName;
  final String shopNo;
  final String area;
  final String city;
  final String state;
  final String saloonStart;
  final String saloonEnd;
  final String offDays;
  final double stars;
  final List<SalonImage> images;

  SalonData({
    required this.id,
    required this.saloonName,
    required this.shopNo,
    required this.area,
    required this.city,
    required this.state,
    required this.saloonStart,
    required this.saloonEnd,
    required this.offDays,
    required this.stars,
    required this.images,
  });

  factory SalonData.fromJson(Map<String, dynamic> json) {
    return SalonData(
      id: json['id'] as String? ?? '',
      saloonName: json['name'] as String? ?? '',
      shopNo: json['shopNo'] as String? ?? '',
      area: json['area'] as String? ?? '',
      city: json['city'] as String? ?? '',
      state: json['state'] as String? ?? '',
      saloonStart: json['saloonStart'] as String? ?? '',
      saloonEnd: json['saloonEnd'] as String? ?? '',
      offDays: json['offDays'] as String? ?? '',
      stars: _parseDouble(json['stars']),
      images: _parseImageList(json['images']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  static List<SalonImage> _parseImageList(dynamic imageData) {
    if (imageData == null) return [];
    if (imageData is List) {
      return imageData
          .where((item) => item != null)
          .map((item) => SalonImage.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'saloonName': saloonName,
      'id': id,
      'shopNo': shopNo,
      'area': area,
      'city': city,
      'state': state,
      'saloonStart': saloonStart,
      'saloonEnd': saloonEnd,
      'offDays': offDays,
      'stars': stars,
      'images': images.map((image) => image.toJson()).toList(),
    };
  }

  // Helper getters
  String get fullAddress {
    final addressParts = [
      area,
      city,
      state,
    ].where((part) => part.isNotEmpty).toList();
    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Address not available';
  }

  String get operatingHours => '$saloonStart - $saloonEnd';
  String get displayStars => stars > 0 ? stars.toStringAsFixed(1) : '0.0';
  bool get hasImages => images.isNotEmpty;
  String get displayShopNo => shopNo.isNotEmpty ? 'Shop #$shopNo' : '';

  @override
  String toString() {
    return 'SalonData(id: $id, area: $area, city: $city, stars: $stars)';
  }
}

/// Salon image model
class SalonImage {
  final String imageUrl;

  SalonImage({required this.imageUrl});

  factory SalonImage.fromJson(Map<String, dynamic> json) {
    return SalonImage(imageUrl: json['imageUrl'] as String? ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'imageUrl': imageUrl};
  }

  bool get isValid => imageUrl.isNotEmpty;

  @override
  String toString() {
    return 'SalonImage(imageUrl: $imageUrl)';
  }
}

/// Salon service model
class SalonService {
  final String id;
  final double price;
  final int length;
  final String name;
  final String categoryname;
  final String saloonid;
  final String serviceimage;
  final bool isDelete;
  final String createdAt;
  final String updatedAt;

  SalonService({
    required this.id,
    required this.price,
    required this.length,
    required this.name,
    required this.categoryname,
    required this.saloonid,
    required this.serviceimage,
    required this.isDelete,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalonService.fromJson(Map<String, dynamic> json) {
    return SalonService(
      id: json['id'] as String? ?? '',
      price: _parseDouble(json['price']),
      length: json['length'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      categoryname: json['categoryname'] as String? ?? '',
      saloonid: json['saloonid'] as String? ?? '',
      serviceimage: json['serviceimage'] as String? ?? '',
      isDelete: json['isDelete'] as bool? ?? false,
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'price': price,
      'length': length,
      'name': name,
      'categoryname': categoryname,
      'saloonid': saloonid,
      'serviceimage': serviceimage,
      'isDelete': isDelete,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Helper getters
  String get displayPrice => '₹${price.toStringAsFixed(0)}';
  String get displayDuration => '${length} min';
  String get displayName => name.isNotEmpty ? name : 'Service';
  String get displayCategory =>
      categoryname.isNotEmpty ? categoryname : 'General';
  bool get hasImage => serviceimage.isNotEmpty;

  @override
  String toString() {
    return 'SalonService(id: $id, name: $name, price: $price, duration: $length)';
  }
}

/// Salon barber model
class SalonBarber {
  final String id;
  final String firstname;
  final String lastname;
  final String bio;
  final String profileimage;
  final bool available;
  final double totalStars;
  final int totalReviews;
  final String saloonid;
  final bool isDelete;
  final String createdAt;
  final String updatedAt;

  SalonBarber({
    required this.id,
    required this.firstname,
    required this.lastname,
    required this.bio,
    required this.profileimage,
    required this.available,
    required this.totalStars,
    required this.totalReviews,
    required this.saloonid,
    required this.isDelete,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalonBarber.fromJson(Map<String, dynamic> json) {
    return SalonBarber(
      id: json['id'] as String? ?? '',
      firstname: json['firstname'] as String? ?? '',
      lastname: json['lastname'] as String? ?? '',
      bio: json['bio'] as String? ?? '',
      profileimage: json['profileimage'] as String? ?? '',
      available: json['available'] as bool? ?? false,
      totalStars: _parseDouble(json['totalStars']),
      totalReviews: json['totalReviews'] as int? ?? 0,
      saloonid: json['saloonid'] as String? ?? '',
      isDelete: json['isDelete'] as bool? ?? false,
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstname': firstname,
      'lastname': lastname,
      'bio': bio,
      'profileimage': profileimage,
      'available': available,
      'totalStars': totalStars,
      'totalReviews': totalReviews,
      'saloonid': saloonid,
      'isDelete': isDelete,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Helper getters
  String get fullName => '$firstname $lastname'.trim();
  String get displayName => fullName.isNotEmpty ? fullName : 'Barber';
  String get displayBio => bio.isNotEmpty ? bio : 'Professional barber';
  String get displayRating =>
      totalStars > 0 ? totalStars.toStringAsFixed(1) : '0.0';
  String get displayReviews => '$totalReviews reviews';
  bool get hasProfileImage => profileimage.isNotEmpty;
  String get availabilityStatus => available ? 'Available' : 'Busy';

  @override
  String toString() {
    return 'SalonBarber(id: $id, name: $fullName, available: $available, rating: $totalStars)';
  }
}

/// Salon review model (for future use)
class SalonReview {
  final String id;
  final String userId;
  final String userName;
  final String reviewText;
  final double rating;
  final String createdAt;

  SalonReview({
    required this.id,
    required this.userId,
    required this.userName,
    required this.reviewText,
    required this.rating,
    required this.createdAt,
  });

  factory SalonReview.fromJson(Map<String, dynamic> json) {
    return SalonReview(
      id: json['id'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      userName: json['userName'] as String? ?? '',
      reviewText: json['reviewText'] as String? ?? '',
      rating: _parseDouble(json['rating']),
      createdAt: json['createdAt'] as String? ?? '',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'reviewText': reviewText,
      'rating': rating,
      'createdAt': createdAt,
    };
  }

  String get displayRating => rating.toStringAsFixed(1);
  String get displayUserName => userName.isNotEmpty ? userName : 'Anonymous';

  @override
  String toString() {
    return 'SalonReview(id: $id, userName: $userName, rating: $rating)';
  }
}
