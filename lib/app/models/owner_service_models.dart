import 'dart:convert';
import 'dart:developer';

/// Owner service request model for adding/updating services
class OwnerServiceRequest {
  final String name;
  final String categoryname;
  final double price;
  final int length;
  final String? serviceImage;

  OwnerServiceRequest({
    required this.name,
    required this.categoryname,
    required this.price,
    required this.length,
    this.serviceImage,
  });

  Map<String, dynamic> toJson() {
    return {
      'serviceName': name, // API expects 'serviceName' not 'name'
      'categoryname': categoryname,
      'price': price,
      'duration': length, // API expects 'duration' not 'length'
      if (serviceImage != null)
        'serviceImage':
            serviceImage, // API expects 'serviceImage' (capital I) for requests
    };
  }

  /// Validation method
  bool isValid() {
    return name.trim().isNotEmpty &&
        categoryname.trim().isNotEmpty &&
        price > 0 &&
        length > 0;
  }

  @override
  String toString() {
    return 'OwnerServiceRequest(name: $name, category: $categoryname, price: $price, duration: $length)';
  }
}

/// Owner service response model for create/update operations
class OwnerServiceResponse {
  final bool success;
  final String message;
  final OwnerServiceData? service;

  OwnerServiceResponse({
    required this.success,
    required this.message,
    this.service,
  });

  factory OwnerServiceResponse.fromJson(Map<String, dynamic> json) {
    OwnerServiceData? serviceData;

    // Handle different service data formats
    if (json['service'] != null) {
      // Standard service field
      serviceData = OwnerServiceData.fromJson(
        json['service'] as Map<String, dynamic>,
      );
    } else if (json['data'] != null) {
      // Data field - could be object or array
      final data = json['data'];
      if (data is Map<String, dynamic>) {
        serviceData = OwnerServiceData.fromJson(data);
      } else if (data is List &&
          data.isNotEmpty &&
          data.first is Map<String, dynamic>) {
        // Data is an array with service objects
        serviceData = OwnerServiceData.fromJson(
          data.first as Map<String, dynamic>,
        );
      }
    }

    return OwnerServiceResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      service: serviceData,
    );
  }

  factory OwnerServiceResponse.fromString(String jsonString) {
    try {
      log('OwnerServiceResponse: Parsing JSON string: $jsonString');
      final dynamic decodedJson = jsonDecode(jsonString);

      // Handle different response formats
      if (decodedJson is Map<String, dynamic>) {
        // Standard object response - let fromJson handle the data parsing
        return OwnerServiceResponse.fromJson(decodedJson);
      } else if (decodedJson is List) {
        // API returned a list directly - treat as successful operation
        if (decodedJson.isNotEmpty &&
            decodedJson.first is Map<String, dynamic>) {
          final serviceData = decodedJson.first as Map<String, dynamic>;
          return OwnerServiceResponse(
            success: true,
            message: 'Service created successfully',
            service: OwnerServiceData.fromJson(serviceData),
          );
        } else {
          // Empty list or unexpected format
          return OwnerServiceResponse(
            success: true,
            message: 'Service operation completed successfully',
          );
        }
      } else {
        // Unexpected format
        log(
          'OwnerServiceResponse: Unexpected response format: ${decodedJson.runtimeType}',
        );
        return OwnerServiceResponse(
          success: false,
          message: 'Unexpected response format from server',
        );
      }
    } catch (e) {
      log('OwnerServiceResponse: Error parsing JSON string: $e');
      return OwnerServiceResponse(
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'service': service?.toJson(),
    };
  }

  @override
  String toString() {
    return 'OwnerServiceResponse(success: $success, message: $message)';
  }
}

/// Owner service data model
class OwnerServiceData {
  final String id;
  final String name;
  final String categoryname;
  final double price;
  final int length;
  final String serviceImage;
  final String saloonid;
  final bool isDelete;
  final String createdAt;
  final String updatedAt;

  OwnerServiceData({
    required this.id,
    required this.name,
    required this.categoryname,
    required this.price,
    required this.length,
    required this.serviceImage,
    required this.saloonid,
    required this.isDelete,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OwnerServiceData.fromJson(Map<String, dynamic> json) {
    // Validate and clean image URL - API returns 'serviceimage' (lowercase i)
    String imageUrl =
        json['serviceimage'] as String? ?? json['image'] as String? ?? '';
    if (imageUrl.isNotEmpty && !_isValidImageUrl(imageUrl)) {
      log('OwnerServiceData: Invalid image URL detected: $imageUrl');
      imageUrl = ''; // Clear invalid URLs
    }

    return OwnerServiceData(
      id: json['id'] as String? ?? '',
      // Handle both 'name' and 'serviceName' fields
      name: json['name'] as String? ?? json['serviceName'] as String? ?? '',
      categoryname: json['categoryname'] as String? ?? '',
      price: _parseDouble(json['price']),
      // Handle both 'length' and 'duration' fields
      length: json['length'] as int? ?? json['duration'] as int? ?? 0,
      // Use validated image URL
      serviceImage: imageUrl,
      saloonid: json['saloonid'] as String? ?? '',
      isDelete: json['isDelete'] as bool? ?? false,
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
    );
  }

  /// Validate if the image URL is valid
  static bool _isValidImageUrl(String url) {
    if (url.isEmpty) return false;

    // Check if it's a valid HTTP/HTTPS URL
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'categoryname': categoryname,
      'price': price,
      'length': length,
      'serviceImage': serviceImage,
      'saloonid': saloonid,
      'isDelete': isDelete,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Helper getters
  String get displayPrice => '₹${price.toStringAsFixed(0)}';
  String get displayDuration => '$length min';
  String get displayName => name.isNotEmpty ? name : 'Service';
  String get displayCategory =>
      categoryname.isNotEmpty ? categoryname : 'General';
  bool get hasServiceImage => serviceImage.isNotEmpty;

  @override
  String toString() {
    return 'OwnerServiceData(id: $id, name: $name, price: $price, duration: $length)';
  }
}

/// Owner services list response model
class OwnerServicesListResponse {
  final int code;
  final bool success;
  final String message;
  final List<OwnerServiceData> data;

  OwnerServicesListResponse({
    required this.code,
    required this.success,
    required this.message,
    required this.data,
  });

  factory OwnerServicesListResponse.fromJson(Map<String, dynamic> json) {
    return OwnerServicesListResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: _parseServiceList(json['data']),
    );
  }

  static List<OwnerServiceData> _parseServiceList(dynamic serviceData) {
    if (serviceData == null) return [];
    if (serviceData is List) {
      return serviceData
          .where((item) => item != null)
          .map(
            (item) => OwnerServiceData.fromJson(item as Map<String, dynamic>),
          )
          .toList();
    }
    return [];
  }

  factory OwnerServicesListResponse.fromString(String jsonString) {
    try {
      log('OwnerServicesListResponse: Parsing JSON string: $jsonString');
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return OwnerServicesListResponse.fromJson(json);
    } catch (e) {
      log('OwnerServicesListResponse: Error parsing JSON string: $e');
      return OwnerServicesListResponse(
        code: 0,
        success: false,
        message: 'Failed to parse response: $e',
        data: [],
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data.map((service) => service.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'OwnerServicesListResponse(code: $code, success: $success, services: ${data.length})';
  }
}

/// Get all services response model
class ServiceResponse {
  final String message;
  final List<GetAllServiceModel> services;

  ServiceResponse({required this.message, required this.services});

  factory ServiceResponse.fromJson(Map<String, dynamic> json) {
    return ServiceResponse(
      message: json['message'] ?? '',
      services: (json['services'] as List<dynamic>)
          .map((item) => GetAllServiceModel.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'services': services.map((service) => service.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ServiceResponse(message: $message, services: ${services.length})';
  }
}

/// Get all service model
class GetAllServiceModel {
  final String id;
  final int price;
  final int length;
  final String name;
  final String categoryName;
  final String saloonId;
  final bool isDelete;
  final String createdAt;
  final String updatedAt;
  final String serviceImages;

  GetAllServiceModel({
    required this.id,
    required this.price,
    required this.length,
    required this.name,
    required this.categoryName,
    required this.saloonId,
    required this.isDelete,
    required this.createdAt,
    required this.updatedAt,
    required this.serviceImages,
  });

  factory GetAllServiceModel.fromJson(Map<String, dynamic> json) {
    // Validate and clean image URL - API returns 'serviceimage' (lowercase i)
    String imageUrl =
        json['serviceimage'] as String? ?? json['image'] as String? ?? '';
    if (imageUrl.isNotEmpty && !_isValidImageUrl(imageUrl)) {
      log('GetAllServiceModel: Invalid image URL detected: $imageUrl');
      imageUrl = ''; // Clear invalid URLs
    }

    return GetAllServiceModel(
      id: json['id'] ?? '',
      price: json['price'] ?? 0,
      length: json['length'] ?? 0,
      name: json['name'] ?? '',
      categoryName: json['categoryname'] ?? '',
      saloonId: json['saloonid'] ?? '',
      isDelete: json['isDelete'] ?? false,
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      serviceImages: imageUrl,
    );
  }

  /// Validate if the image URL is valid
  static bool _isValidImageUrl(String url) {
    if (url.isEmpty) return false;

    // Check if it's a valid HTTP/HTTPS URL
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'price': price,
      'length': length,
      'name': name,
      'categoryname': categoryName,
      'saloonid': saloonId,
      'isDelete': isDelete,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'serviceImage': serviceImages,
    };
  }

  // Helper getters
  String get displayPrice => '₹${price.toStringAsFixed(0)}';
  String get displayDuration => '$length min';
  String get displayName => name.isNotEmpty ? name : 'Service';
  String get displayCategory =>
      categoryName.isNotEmpty ? categoryName : 'General';
  bool get hasImage => serviceImages.isNotEmpty;

  // Backward compatibility getter
  String get images => serviceImages;

  @override
  String toString() {
    return 'GetAllServiceModel(id: $id, name: $name, price: $price, duration: $length)';
  }
}

/// Delete service response model
class DeleteServiceResponse {
  final int code;
  final bool success;
  final String message;

  DeleteServiceResponse({
    required this.code,
    required this.success,
    required this.message,
  });

  factory DeleteServiceResponse.fromJson(Map<String, dynamic> json) {
    return DeleteServiceResponse(
      code: json['code'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'code': code, 'success': success, 'message': message};
  }

  @override
  String toString() {
    return 'DeleteServiceResponse(code: $code, success: $success, message: $message)';
  }
}

/// Service error response model
class ServiceErrorResponse {
  final bool success;
  final String message;
  final String? error;
  final int? statusCode;

  ServiceErrorResponse({
    required this.success,
    required this.message,
    this.error,
    this.statusCode,
  });

  factory ServiceErrorResponse.fromJson(Map<String, dynamic> json) {
    return ServiceErrorResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? 'Unknown error occurred',
      error: json['error'] as String?,
      statusCode: json['statusCode'] as int?,
    );
  }

  factory ServiceErrorResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return ServiceErrorResponse.fromJson(json);
    } catch (e) {
      return ServiceErrorResponse(
        success: false,
        message: 'Failed to parse error response',
        error: e.toString(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'statusCode': statusCode,
    };
  }

  @override
  String toString() {
    return 'ServiceErrorResponse(success: $success, message: $message, error: $error)';
  }
}
