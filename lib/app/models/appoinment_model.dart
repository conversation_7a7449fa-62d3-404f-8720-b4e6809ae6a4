class AppoinmentOwnerSideModel {
  int code;
  bool success;
  Data data;

  AppoinmentOwnerSideModel({
    required this.code,
    required this.success,
    required this.data,
  });

  factory AppoinmentOwnerSideModel.fromJson(Map<String, dynamic> json) {
    return AppoinmentOwnerSideModel(
      code: json['code'] ?? 0,
      success: json['success'] ?? false,
      data: Data.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() => {
    'code': code,
    'success': success,
    'data': data.toJson(),
  };
}

class Data {
  List<Confirmed> pending;
  List<Confirmed> inProgress;
  List<Confirmed> confirmed;

  Data({
    required this.pending,
    required this.inProgress,
    required this.confirmed,
  });

  factory Data.fromJson(dynamic json) {
    // Handle case when data is an empty array []
    if (json is List) {
      return Data(pending: [], inProgress: [], confirmed: []);
    }

    // Handle case when data is a Map (Record<string, []>)
    if (json is Map<String, dynamic>) {
      return Data(
        pending: _parseAppointmentList(json['pending']),
        inProgress:
            _parseAppointmentList(json['in_progress']) ??
            _parseAppointmentList(
              json['inProgress'],
            ), // Handle both snake_case and camelCase
        confirmed: _parseAppointmentList(json['confirmed']),
      );
    }

    // Default case - return empty lists
    return Data(pending: [], inProgress: [], confirmed: []);
  }

  static List<Confirmed> _parseAppointmentList(dynamic list) {
    if (list == null) return [];
    if (list is! List) return [];

    return list
        .map((e) => e is Map<String, dynamic> ? Confirmed.fromJson(e) : null)
        .where((e) => e != null)
        .cast<Confirmed>()
        .toList();
  }

  Map<String, dynamic> toJson() => {
    'pending': pending.map((e) => e.toJson()).toList(),
    'in_progress': inProgress.map((e) => e.toJson()).toList(),
    'confirmed': confirmed.map((e) => e.toJson()).toList(),
  };
}

class Confirmed {
  String id;
  String barberName;
  String userName;
  int amount;
  String bookingType;
  String status;
  String startTime;
  String endTime;
  DateTime appointmentDate;
  DateTime createdAt;

  Confirmed({
    required this.id,
    required this.barberName,
    required this.userName,
    required this.amount,
    required this.bookingType,
    required this.status,
    required this.startTime,
    required this.endTime,
    required this.appointmentDate,
    required this.createdAt,
  });

  factory Confirmed.fromJson(Map<String, dynamic> json) {
    // Removes any extra space in time like '10: 00: 00'
    String _cleanTime(String? time) => (time ?? '').replaceAll(' ', '');

    return Confirmed(
      id: json['id']?.toString() ?? '',
      barberName: json['barberName']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      amount: _parseAmount(json['amount']),
      bookingType: json['bookingType']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      startTime: _cleanTime(json['startTime']?.toString()),
      endTime: _cleanTime(json['endTime']?.toString()),
      appointmentDate: _parseDateTime(json['appointmentDate']),
      createdAt: _parseDateTime(json['createdAt']),
    );
  }

  static int _parseAmount(dynamic amount) {
    if (amount == null) return 0;
    if (amount is int) return amount;
    if (amount is double) return amount.toInt();
    if (amount is String) return int.tryParse(amount) ?? 0;
    return 0;
  }

  static DateTime _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return DateTime.fromMillisecondsSinceEpoch(0);
    if (dateTime is String) {
      return DateTime.tryParse(dateTime) ??
          DateTime.fromMillisecondsSinceEpoch(0);
    }
    return DateTime.fromMillisecondsSinceEpoch(0);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'barberName': barberName,
      'userName': userName,
      'amount': amount,
      'bookingType': bookingType,
      'status': status,
      'startTime': startTime,
      'endTime': endTime,
      'appointmentDate': appointmentDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Helper methods for display
  String get displayCustomer =>
      userName.isNotEmpty ? userName : 'Unknown Customer';
  String get displayAmount => '₹${amount.toString()}';
  String get displayServices =>
      bookingType.isNotEmpty ? bookingType : 'Standard Service';

  String get displayDate {
    if (appointmentDate.millisecondsSinceEpoch == 0)
      return 'Date not available';
    return '${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}';
  }

  String get displayTime {
    if (startTime.isEmpty || endTime.isEmpty) return 'Time not available';
    return '${_formatTime(startTime)} - ${_formatTime(endTime)}';
  }

  // Helper method to format time as HH:MM
  String _formatTime(String time) {
    if (time.isEmpty) return '';

    // Handle different time formats
    if (time.contains(':')) {
      final parts = time.split(':');
      if (parts.length >= 2) {
        final hour = parts[0].padLeft(2, '0');
        final minute = parts[1].padLeft(2, '0');
        return '$hour:$minute';
      }
    }

    return time; // Return as-is if format is unexpected
  }
}
