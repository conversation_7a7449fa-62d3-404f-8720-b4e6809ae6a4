/// Response model for user appointments API
class UserAppointmentsResponse {
  final int code;
  final bool success;
  final String message;
  final UserAppointmentsData? data;

  UserAppointmentsResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory UserAppointmentsResponse.fromJson(Map<String, dynamic> json) {
    return UserAppointmentsResponse(
      code: json['code'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? UserAppointmentsData.fromJson(json['data'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

/// Data container for appointments
class UserAppointmentsData {
  final List<UserAppointment> upcoming;
  final List<UserAppointment> past;

  UserAppointmentsData({required this.upcoming, required this.past});

  factory UserAppointmentsData.fromJson(Map<String, dynamic> json) {
    return UserAppointmentsData(
      upcoming:
          (json['upcoming'] as List<dynamic>?)
              ?.map((item) => UserAppointment.fromJson(item))
              .toList() ??
          [],
      past:
          (json['past'] as List<dynamic>?)
              ?.map((item) => UserAppointment.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upcoming': upcoming.map((item) => item.toJson()).toList(),
      'past': past.map((item) => item.toJson()).toList(),
    };
  }

  // Getter for backward compatibility
  List<UserAppointment> get completed => past;
}

/// Individual appointment model
class UserAppointment {
  final String id;
  final String saloonId;
  final String saloonName;
  final String barberId;
  final String barberName;
  final double amount;
  final String bookingType;
  final String status;
  final String startTime;
  final String date;
  final String endTime;
  final String appointmentDate;
  final String? otp;
  final bool canRescheduled;
  final DateTime createdAt;
  final List<AppointmentService> services;
  final bool? isReviewed;
  final AppointmentReview? review;

  UserAppointment({
    required this.id,
    required this.saloonId,
    required this.saloonName,
    required this.barberId,
    required this.barberName,
    required this.amount,
    required this.bookingType,
    required this.status,
    required this.startTime,
    required this.date,
    required this.endTime,
    required this.appointmentDate,
    this.otp,
    required this.canRescheduled,
    required this.createdAt,
    required this.services,
    this.isReviewed,
    this.review,
  });

  factory UserAppointment.fromJson(Map<String, dynamic> json) {
    return UserAppointment(
      id: json['id']?.toString() ?? '',
      saloonId: json['saloonId']?.toString() ?? '',
      saloonName: json['saloonName']?.toString() ?? '',
      barberId: json['barberId']?.toString() ?? '',
      barberName: json['barberName']?.toString() ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      bookingType: json['bookingType']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      startTime: json['startTime']?.toString() ?? '',
      date: json['date']?.toString() ?? '',
      endTime: json['endTime']?.toString() ?? '',
      appointmentDate: json['appointmentDate']?.toString() ?? '',
      otp: json['otp']?.toString(),
      canRescheduled: json['canRescheduled'] ?? false,
      createdAt:
          DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
      services:
          (json['services'] as List<dynamic>?)
              ?.map((item) => AppointmentService.fromJson(item))
              .toList() ??
          [],
      isReviewed: json['isReviewed'] as bool?,
      review: json['review'] != null
          ? AppointmentReview.fromJson(json['review'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'saloonId': saloonId,
      'saloonName': saloonName,
      'barberId': barberId,
      'barberName': barberName,
      'amount': amount,
      'bookingType': bookingType,
      'status': status,
      'startTime': startTime,
      'date': date,
      'endTime': endTime,
      'appointmentDate': appointmentDate,
      'otp': otp,
      'canRescheduled': canRescheduled,
      'createdAt': createdAt.toIso8601String(),
      'services': services.map((item) => item.toJson()).toList(),
      'isReviewed': isReviewed,
      'review': review?.toJson(),
    };
  }

  /// Get formatted date and time string
  String get formattedDateTime {
    try {
      final dateTime = DateTime.tryParse(appointmentDate);
      if (dateTime != null) {
        final months = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ];

        final day = dateTime.day;
        final month = months[dateTime.month - 1];
        final year = dateTime.year;

        return '$day $month $year at $startTime';
      }
    } catch (e) {
      // Fallback to raw string if parsing fails
    }
    return '$appointmentDate at $startTime';
  }

  /// Get formatted amount
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';

  /// Get service names as comma-separated string
  String get serviceNames => services.map((s) => s.serviceName).join(', ');

  /// Get category names as comma-separated string
  String get categoryNames => services
      .map((s) => s.categoryName ?? '')
      .where((name) => name.isNotEmpty)
      .join(', ');

  /// Check if appointment is upcoming
  bool get isUpcoming =>
      status.toLowerCase() == 'pending' || status.toLowerCase() == 'confirmed';

  /// Check if appointment is completed
  bool get isCompleted => status.toLowerCase() == 'completed';
}

/// Service model for appointments
class AppointmentService {
  final String serviceId;
  final String appointmentId;
  final String serviceName;
  final String? categoryName;
  final double price;

  AppointmentService({
    required this.serviceId,
    required this.appointmentId,
    required this.serviceName,
    this.categoryName,
    required this.price,
  });

  factory AppointmentService.fromJson(Map<String, dynamic> json) {
    return AppointmentService(
      serviceId: json['serviceId']?.toString() ?? '',
      appointmentId: json['appointmentId']?.toString() ?? '',
      serviceName: json['serviceName']?.toString() ?? '',
      categoryName: json['categoryName']?.toString(),
      price: (json['price'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceId': serviceId,
      'appointmentId': appointmentId,
      'serviceName': serviceName,
      'categoryName': categoryName,
      'price': price,
    };
  }

  /// Get formatted price
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
}

/// Review model for appointments
class AppointmentReview {
  final String id;
  final String appointmentId;
  final String userId;
  final int stars;
  final String? comment;
  final DateTime createdAt;

  AppointmentReview({
    required this.id,
    required this.appointmentId,
    required this.userId,
    required this.stars,
    this.comment,
    required this.createdAt,
  });

  factory AppointmentReview.fromJson(Map<String, dynamic> json) {
    return AppointmentReview(
      id: json['id']?.toString() ?? '',
      appointmentId: json['appointmentId']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      stars: json['stars'] ?? 0,
      comment: json['comment']?.toString(),
      createdAt:
          DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'appointmentId': appointmentId,
      'userId': userId,
      'stars': stars,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

/// Request model for submitting reviews
class SubmitReviewRequest {
  final String appointmentId;
  final int stars;
  final String? comment;

  SubmitReviewRequest({
    required this.appointmentId,
    required this.stars,
    this.comment,
  });

  Map<String, dynamic> toJson() {
    return {
      'appointmentId': appointmentId,
      'stars': stars,
      if (comment != null) 'comment': comment,
    };
  }
}

/// Response model for review submission
class SubmitReviewResponse {
  final int code;
  final bool success;
  final String message;
  final AppointmentReview? data;

  SubmitReviewResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory SubmitReviewResponse.fromJson(Map<String, dynamic> json) {
    return SubmitReviewResponse(
      code: json['code'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? AppointmentReview.fromJson(json['data'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
