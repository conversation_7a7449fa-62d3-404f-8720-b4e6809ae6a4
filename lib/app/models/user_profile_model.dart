import 'dart:convert';

class UserProfileRequest {
  final String firstName;
  final String lastName;
  final String salonName;
  final String salonDescription;
  final String salonPhone;
  final String salonEmail;
  final String area;
  final String city;
  final String state;
  final String country;
  final String pinCode;
  final String salonRegid;
  final String shopNo;
  final String? profileImage; // Added profileImage field
  final String? logoUrl; // Added logoUrl field for salon logo

  UserProfileRequest({
    required this.firstName,
    required this.lastName,
    required this.salonName,
    required this.salonDescription,
    required this.salonPhone,
    required this.salonEmail,
    required this.area,
    required this.city,
    required this.state,
    required this.country,
    required this.pinCode,
    required this.salonRegid,
    required this.shopNo,
    this.profileImage, // Added profileImage parameter
    this.logoUrl, // Added logoUrl parameter
  });

  Map<String, dynamic> toMap() {
    final map = {
      'firstName': firstName,
      'lastName': lastName,
      'salonName': salonName,
      'salonDescription': salonDescription,
      'salonPhone': salonPhone,
      'salonEmail': salonEmail,
      'area': area,
      'city': city,
      'state': state,
      'country': country,
      'pinCode': pinCode,
      'salonRegId': salonRegid,
      'shopNo': shopNo,
    };

    // Only add profileImage if it's not null
    if (profileImage != null && profileImage!.isNotEmpty) {
      map['profileImage'] = profileImage!;
    }

    // Only add logoUrl if it's not null
    if (logoUrl != null && logoUrl!.isNotEmpty) {
      map['logoUrl'] = logoUrl!;
    }

    return map;
  }

  /// Create from Map
  factory UserProfileRequest.fromMap(Map<String, dynamic> map) {
    return UserProfileRequest(
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      salonName: map['salonName'] ?? '',
      salonDescription: map['salonDescription'] ?? '',
      salonPhone: map['salonPhone'] ?? '',
      salonEmail: map['salonEmail'] ?? '',
      area: map['street'] ?? map['area'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      country: map['country'] ?? '',
      pinCode: map['zipCode'] ?? map['pinCode'] ?? '',
      salonRegid: map['salonRegisterId'] ?? map['salonRegId'] ?? '',
      shopNo: map['shopNo'] ?? '',
      profileImage: map['profileImage'],
      logoUrl: map['logoUrl'],
    );
  }

  /// Convert to JSON string
  String toJson() => jsonEncode(toMap());

  /// Create from JSON string
  factory UserProfileRequest.fromJson(String source) =>
      UserProfileRequest.fromMap(jsonDecode(source));

  /// Copy with modified values
  UserProfileRequest copyWith({
    String? firstName,
    String? lastName,
    String? salonName,
    String? salonDescription,
    String? salonPhone,
    String? salonEmail,
    String? street,
    String? city,
    String? state,
    String? country,
    String? zipCode,
    String? salonRegisterId,
    String? shopNo,
    String? profileImage,
    String? logoUrl,
  }) {
    return UserProfileRequest(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      salonName: salonName ?? this.salonName,
      salonDescription: salonDescription ?? this.salonDescription,
      salonPhone: salonPhone ?? this.salonPhone,
      salonEmail: salonEmail ?? this.salonEmail,
      area: street ?? this.area,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      pinCode: zipCode ?? this.pinCode,
      salonRegid: salonRegisterId ?? this.salonRegid,
      shopNo: shopNo ?? this.shopNo,
      profileImage: profileImage ?? this.profileImage,
      logoUrl: logoUrl ?? this.logoUrl,
    );
  }

  @override
  String toString() {
    return 'UserProfileRequest(firstName: $firstName, lastName: $lastName, salonName: $salonName, salonDescription: $salonDescription, salonPhone: $salonPhone, salonEmail: $salonEmail, street: $area, city: $city, state: $state, country: $country, zipCode: $pinCode, salonRegisterId: $salonRegid, shopNo: $shopNo, profileImage: $profileImage, logoUrl: $logoUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UserProfileRequest &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.salonName == salonName &&
        other.salonDescription == salonDescription &&
        other.salonPhone == salonPhone &&
        other.salonEmail == salonEmail &&
        other.area == area &&
        other.city == city &&
        other.state == state &&
        other.country == country &&
        other.pinCode == pinCode &&
        other.salonRegid == salonRegid &&
        other.shopNo == shopNo &&
        other.profileImage == profileImage &&
        other.logoUrl == logoUrl;
  }

  @override
  int get hashCode {
    return firstName.hashCode ^
        lastName.hashCode ^
        salonName.hashCode ^
        salonDescription.hashCode ^
        salonPhone.hashCode ^
        salonEmail.hashCode ^
        area.hashCode ^
        city.hashCode ^
        state.hashCode ^
        country.hashCode ^
        pinCode.hashCode ^
        salonRegid.hashCode ^
        shopNo.hashCode ^
        profileImage.hashCode ^
        logoUrl.hashCode;
  }
}

class UserProfileResponse {
  final bool success;
  final String message;
  final String saloonId;

  UserProfileResponse({
    required this.success,
    required this.message,
    required this.saloonId,
  });

  /// Create from Map
  factory UserProfileResponse.fromMap(Map<String, dynamic> map) {
    return UserProfileResponse(
      success: map['success'] ?? false,
      message: map['message'] ?? '',
      saloonId: map['saloonId'] ?? '',
    );
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {'success': success, 'message': message, 'saloonId': saloonId};
  }

  /// Create from JSON string
  factory UserProfileResponse.fromJson(String source) =>
      UserProfileResponse.fromMap(jsonDecode(source));

  /// Convert to JSON string
  String toJson() => jsonEncode(toMap());

  /// Copy with modified values
  UserProfileResponse copyWith({
    bool? success,
    String? message,
    String? saloonId,
  }) {
    return UserProfileResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      saloonId: saloonId ?? this.saloonId,
    );
  }

  @override
  String toString() =>
      'UserProfileResponse(success: $success, message: $message, saloonId: $saloonId)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UserProfileResponse &&
        other.success == success &&
        other.message == message &&
        other.saloonId == saloonId;
  }

  @override
  int get hashCode => success.hashCode ^ message.hashCode ^ saloonId.hashCode;
}
