import 'dart:convert';
import 'dart:developer';

/// Time slot model for available booking times
class TimeSlot {
  final String startTime;
  final String endTime;

  TimeSlot({required this.startTime, required this.endTime});

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: json['startTime'] as String? ?? '',
      endTime: json['endTime'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'startTime': startTime, 'endTime': endTime};
  }

  /// Display formatted time slot in 24-hour format
  String get displayTime {
    if (startTime.isEmpty || endTime.isEmpty) return 'Invalid Time';
    return '$startTime - $endTime';
  }

  /// Display formatted time slot in 12-hour format (user-friendly)
  String get displayTime12Hour {
    if (startTime.isEmpty || endTime.isEmpty) return 'Invalid Time';

    try {
      final startFormatted = _formatTimeTo12Hour(startTime);
      final endFormatted = _formatTimeTo12Hour(endTime);
      return '$startFormatted - $endFormatted';
    } catch (e) {
      return displayTime; // Fallback to 24-hour format
    }
  }

  /// Helper method to convert 24-hour time to 12-hour format
  String _formatTimeTo12Hour(String time24) {
    final parts = time24.split(':');
    if (parts.length != 2) return time24;

    final hour = int.tryParse(parts[0]);
    final minute = parts[1];

    if (hour == null) return time24;

    if (hour == 0) {
      return '12:$minute AM';
    } else if (hour < 12) {
      return '$hour:$minute AM';
    } else if (hour == 12) {
      return '12:$minute PM';
    } else {
      return '${hour - 12}:$minute PM';
    }
  }

  /// Check if time slot is valid
  bool get isValid {
    return startTime.isNotEmpty && endTime.isNotEmpty;
  }

  @override
  String toString() {
    return 'TimeSlot(startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeSlot &&
        other.startTime == startTime &&
        other.endTime == endTime;
  }

  @override
  int get hashCode => startTime.hashCode ^ endTime.hashCode;
}

/// Salon operating hours model
class SalonHours {
  final String start;
  final String end;

  SalonHours({required this.start, required this.end});

  factory SalonHours.fromJson(Map<String, dynamic> json) {
    return SalonHours(
      start: json['start'] as String? ?? '',
      end: json['end'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'start': start, 'end': end};
  }

  /// Display formatted salon hours in 24-hour format
  String get displayHours {
    if (start.isEmpty || end.isEmpty) return 'Hours not available';
    return '$start - $end';
  }

  /// Display formatted salon hours in 12-hour format (user-friendly)
  String get displayHours12Hour {
    if (start.isEmpty || end.isEmpty) return 'Hours not available';

    try {
      final startFormatted = _formatTimeTo12Hour(start);
      final endFormatted = _formatTimeTo12Hour(end);
      return '$startFormatted - $endFormatted';
    } catch (e) {
      return displayHours; // Fallback to 24-hour format
    }
  }

  /// Helper method to convert 24-hour time to 12-hour format
  String _formatTimeTo12Hour(String time24) {
    final parts = time24.split(':');
    if (parts.length != 2) return time24;

    final hour = int.tryParse(parts[0]);
    final minute = parts[1];

    if (hour == null) return time24;

    if (hour == 0) {
      return '12:$minute AM';
    } else if (hour < 12) {
      return '$hour:$minute AM';
    } else if (hour == 12) {
      return '12:$minute PM';
    } else {
      return '${hour - 12}:$minute PM';
    }
  }

  /// Check if salon hours are valid
  bool get isValid {
    return start.isNotEmpty && end.isNotEmpty;
  }

  @override
  String toString() {
    return 'SalonHours(start: $start, end: $end)';
  }
}

/// Available slots data model
class AvailableSlotsData {
  final String date;
  final int totalDuration;
  final List<TimeSlot> availableSlots;
  final SalonHours salonHours;

  AvailableSlotsData({
    required this.date,
    required this.totalDuration,
    required this.availableSlots,
    required this.salonHours,
  });

  factory AvailableSlotsData.fromJson(Map<String, dynamic> json) {
    log('AvailableSlotsData: Parsing JSON: $json');

    final slotsJson = json['availableSlots'] as List<dynamic>? ?? [];
    final slots = slotsJson
        .map((slot) {
          if (slot is Map<String, dynamic>) {
            return TimeSlot.fromJson(slot);
          } else {
            log('AvailableSlotsData: Invalid slot format: $slot');
            return TimeSlot(startTime: '', endTime: '');
          }
        })
        .where((slot) => slot.isValid)
        .toList();

    final salonHoursJson = json['salonHours'] as Map<String, dynamic>? ?? {};

    return AvailableSlotsData(
      date: json['date'] as String? ?? '',
      totalDuration: json['totalDuration'] as int? ?? 0,
      availableSlots: slots,
      salonHours: SalonHours.fromJson(salonHoursJson),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'totalDuration': totalDuration,
      'availableSlots': availableSlots.map((slot) => slot.toJson()).toList(),
      'salonHours': salonHours.toJson(),
    };
  }

  /// Check if there are available slots
  bool get hasAvailableSlots {
    return availableSlots.isNotEmpty;
  }

  /// Get formatted date display
  String get displayDate {
    if (date.isEmpty) return 'Date not available';
    try {
      final dateTime = DateTime.parse(date);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return date;
    }
  }

  /// Get formatted total duration
  String get displayDuration {
    if (totalDuration <= 0) return 'Duration not available';
    final hours = totalDuration ~/ 60;
    final minutes = totalDuration % 60;

    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}m';
    }
  }

  @override
  String toString() {
    return 'AvailableSlotsData(date: $date, totalDuration: $totalDuration, availableSlots: ${availableSlots.length}, salonHours: $salonHours)';
  }
}

/// Available slots API response model
class AvailableSlotsResponse {
  final int code;
  final bool success;
  final AvailableSlotsData? data;
  final String message;

  AvailableSlotsResponse({
    required this.code,
    required this.success,
    this.data,
    required this.message,
  });

  factory AvailableSlotsResponse.fromJson(Map<String, dynamic> json) {
    log('AvailableSlotsResponse: Parsing JSON: $json');

    final dataJson = json['data'] as Map<String, dynamic>?;
    AvailableSlotsData? slotsData;

    if (dataJson != null) {
      try {
        slotsData = AvailableSlotsData.fromJson(dataJson);
      } catch (e) {
        log('AvailableSlotsResponse: Error parsing data: $e');
      }
    }

    return AvailableSlotsResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      data: slotsData,
      message: json['message'] as String? ?? '',
    );
  }

  factory AvailableSlotsResponse.fromString(String jsonString) {
    try {
      log('AvailableSlotsResponse: Parsing JSON string: $jsonString');
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return AvailableSlotsResponse.fromJson(json);
    } catch (e) {
      log('AvailableSlotsResponse: Error parsing JSON string: $e');
      return AvailableSlotsResponse(
        code: 0,
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'data': data?.toJson(),
      'message': message,
    };
  }

  /// Check if response has valid data
  bool get hasValidData {
    return success && data != null && data!.hasAvailableSlots;
  }

  /// Get user-friendly error message
  String get userFriendlyMessage {
    if (success && data != null) {
      if (!data!.hasAvailableSlots) {
        return 'No available time slots for the selected date';
      }
      return 'Available slots loaded successfully';
    }

    if (message.isNotEmpty) {
      return message;
    }

    switch (code) {
      case 400:
        return 'Invalid request. Please check your selection and try again.';
      case 404:
        return 'No available slots found for the selected date.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return 'Failed to load available slots. Please try again.';
    }
  }

  @override
  String toString() {
    return 'AvailableSlotsResponse(code: $code, success: $success, data: $data, message: $message)';
  }
}

/// Booking request model for available slots API
class AvailableSlotsRequest {
  final String saloonId;
  final String? barberId; // Optional - null when coming from general booking
  final List<String> serviceIds;
  final String date; // Format: "2025-07-15"

  AvailableSlotsRequest({
    required this.saloonId,
    this.barberId,
    required this.serviceIds,
    required this.date,
  });

  Map<String, dynamic> toJson() {
    final json = {'saloonId': saloonId, 'serviceIds': serviceIds, 'date': date};

    // Only include barberId if it's not null
    if (barberId != null && barberId!.isNotEmpty) {
      json['barberId'] = barberId!;
    }

    return json;
  }

  /// Validate request data
  bool get isValid {
    return saloonId.isNotEmpty && serviceIds.isNotEmpty && date.isNotEmpty;
  }

  /// Get formatted date for display
  String get displayDate {
    try {
      final dateTime = DateTime.parse(date);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return date;
    }
  }

  @override
  String toString() {
    return 'AvailableSlotsRequest(saloonId: $saloonId, barberId: $barberId, serviceIds: $serviceIds, date: $date)';
  }
}
