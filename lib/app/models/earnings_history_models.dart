import 'dart:developer';

/// Earnings History Response Model
class EarningsHistoryResponse {
  final int code;
  final bool success;
  final String message;
  final EarningsHistoryData? data;

  EarningsHistoryResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory EarningsHistoryResponse.fromJson(Map<String, dynamic> json) {
    return EarningsHistoryResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null
          ? EarningsHistoryData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }

  @override
  String toString() {
    return 'EarningsHistoryResponse(code: $code, success: $success, message: $message)';
  }
}

/// Earnings History Data Model
class EarningsHistoryData {
  final int total;
  final int completed;
  final int pending;
  final int rejected;
  final double totalEarning;
  final WithdrawalData withdrawalData;

  EarningsHistoryData({
    required this.total,
    required this.completed,
    required this.pending,
    required this.rejected,
    required this.totalEarning,
    required this.withdrawalData,
  });

  factory EarningsHistoryData.fromJson(Map<String, dynamic> json) {
    return EarningsHistoryData(
      total: json['total'] as int? ?? 0,
      completed: json['completed'] as int? ?? 0,
      pending: json['pending'] as int? ?? 0,
      rejected: json['rejected'] as int? ?? 0,
      totalEarning: _parseDouble(json['totalEarning']),
      withdrawalData: json['withdrawalData'] != null
          ? WithdrawalData.fromJson(json['withdrawalData'] as Map<String, dynamic>)
          : WithdrawalData.empty(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'completed': completed,
      'pending': pending,
      'rejected': rejected,
      'totalEarning': totalEarning,
      'withdrawalData': withdrawalData.toJson(),
    };
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Helper getters
  int get totalTransactions => total;
  double get completionRate => total > 0 ? (completed / total) * 100 : 0.0;
  double get pendingRate => total > 0 ? (pending / total) * 100 : 0.0;
  double get rejectionRate => total > 0 ? (rejected / total) * 100 : 0.0;

  @override
  String toString() {
    return 'EarningsHistoryData(total: $total, completed: $completed, pending: $pending, rejected: $rejected, totalEarning: $totalEarning)';
  }
}

/// Withdrawal Data Model
class WithdrawalData {
  final List<WithdrawalTransaction> pending;
  final List<WithdrawalTransaction> completed;
  final List<WithdrawalTransaction> rejected;

  WithdrawalData({
    required this.pending,
    required this.completed,
    required this.rejected,
  });

  factory WithdrawalData.fromJson(Map<String, dynamic> json) {
    return WithdrawalData(
      pending: _parseTransactionList(json['pending']),
      completed: _parseTransactionList(json['completed']),
      rejected: _parseTransactionList(json['rejected']),
    );
  }

  factory WithdrawalData.empty() {
    return WithdrawalData(
      pending: [],
      completed: [],
      rejected: [],
    );
  }

  static List<WithdrawalTransaction> _parseTransactionList(dynamic data) {
    if (data == null) return [];
    if (data is! List) return [];
    
    return data
        .map((item) => WithdrawalTransaction.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'pending': pending.map((transaction) => transaction.toJson()).toList(),
      'completed': completed.map((transaction) => transaction.toJson()).toList(),
      'rejected': rejected.map((transaction) => transaction.toJson()).toList(),
    };
  }

  // Helper getters
  List<WithdrawalTransaction> get allTransactions {
    final all = <WithdrawalTransaction>[];
    all.addAll(pending);
    all.addAll(completed);
    all.addAll(rejected);
    
    // Sort by withdrawal date (most recent first)
    all.sort((a, b) => b.withDrawalAt.compareTo(a.withDrawalAt));
    return all;
  }

  List<WithdrawalTransaction> getTransactionsByStatus(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.pending:
        return pending;
      case TransactionStatus.completed:
        return completed;
      case TransactionStatus.rejected:
        return rejected;
      case TransactionStatus.all:
        return allTransactions;
    }
  }

  @override
  String toString() {
    return 'WithdrawalData(pending: ${pending.length}, completed: ${completed.length}, rejected: ${rejected.length})';
  }
}

/// Withdrawal Transaction Model
class WithdrawalTransaction {
  final String id;
  final double withDrawalAmount;
  final double withdrawCharges;
  final String status;
  final DateTime withDrawalAt;

  WithdrawalTransaction({
    required this.id,
    required this.withDrawalAmount,
    required this.withdrawCharges,
    required this.status,
    required this.withDrawalAt,
  });

  factory WithdrawalTransaction.fromJson(Map<String, dynamic> json) {
    return WithdrawalTransaction(
      id: json['id'] as String? ?? '',
      withDrawalAmount: _parseDouble(json['withDrawalAmount']),
      withdrawCharges: _parseDouble(json['withdrawCharges']),
      status: json['status'] as String? ?? '',
      withDrawalAt: _parseDateTime(json['withDrawalAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'withDrawalAmount': withDrawalAmount,
      'withdrawCharges': withdrawCharges,
      'status': status,
      'withDrawalAt': withDrawalAt.toIso8601String(),
    };
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        log('Error parsing date: $value');
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  // Helper getters
  double get netAmount => withDrawalAmount - withdrawCharges;
  
  TransactionStatus get transactionStatus {
    switch (status.toLowerCase()) {
      case 'pending':
        return TransactionStatus.pending;
      case 'completed':
        return TransactionStatus.completed;
      case 'rejected':
        return TransactionStatus.rejected;
      default:
        return TransactionStatus.pending;
    }
  }

  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${withDrawalAt.day} ${months[withDrawalAt.month - 1]} ${withDrawalAt.year}';
  }

  String get formattedTime {
    return '${withDrawalAt.hour.toString().padLeft(2, '0')}:${withDrawalAt.minute.toString().padLeft(2, '0')}';
  }

  String get paymentMethod {
    // This would typically come from API, but for now we'll generate based on amount
    if (withDrawalAmount >= 10000) return 'RTGS';
    if (withDrawalAmount >= 2000) return 'NEFT';
    return 'Bank Transfer';
  }

  String get referenceNumber => 'TXN${id.toUpperCase()}';

  @override
  String toString() {
    return 'WithdrawalTransaction(id: $id, amount: $withDrawalAmount, status: $status, date: $formattedDate)';
  }
}

/// Transaction Status Enum
enum TransactionStatus {
  all('All'),
  completed('Complete'),
  pending('Pending'),
  rejected('Reject');

  const TransactionStatus(this.displayName);
  final String displayName;

  static TransactionStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'completed':
      case 'complete':
        return TransactionStatus.completed;
      case 'pending':
        return TransactionStatus.pending;
      case 'rejected':
      case 'reject':
        return TransactionStatus.rejected;
      default:
        return TransactionStatus.all;
    }
  }
}

/// Download Option Model
class DownloadOption {
  final String title;
  final String subtitle;
  final DateTime date;
  final double amount;

  DownloadOption({
    required this.title,
    required this.subtitle,
    required this.date,
    required this.amount,
  });

  String get formattedAmount => '₹${amount.toStringAsFixed(0)}';
  
  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
