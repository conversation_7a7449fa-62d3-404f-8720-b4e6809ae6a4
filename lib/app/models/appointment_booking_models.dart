import 'dart:developer';

/// Payment mode enum for appointment booking
enum PaymentMode {
  fullPayment('full_payment', 'Full Payment', 'Pay 100% now'),
  slotBooking('slot_booking', 'Slot Booking', 'Pay 5% now, rest at salon'),
  payAtSalon('pay_at_salon', 'Pay at Salon', 'Pay full amount at salon');

  const PaymentMode(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;
}

/// Appointment booking request model
class AppointmentBookingRequest {
  final String appointmentDate;
  final String startTime;
  final String endTime;
  final List<String> serviceIds;
  final String barberId;
  final String saloonId;
  final String paymentType;

  AppointmentBookingRequest({
    required this.appointmentDate,
    required this.startTime,
    required this.endTime,
    required this.serviceIds,
    required this.barberId,
    required this.saloonId,
    required this.paymentType,
  });

  Map<String, dynamic> toJson() {
    return {
      'appointmentDate': appointmentDate,
      'startTime': startTime,
      'endTime': endTime,
      'serviceIds': serviceIds,
      'barberId': barberId,
      'saloonId': saloonId,
      'paymentType': paymentType,
    };
  }

  @override
  String toString() {
    return 'AppointmentBookingRequest(date: $appointmentDate, barber: $barberId, services: ${serviceIds.length})';
  }
}

/// Appointment booking response model
class AppointmentBookingResponse {
  final int code;
  final bool success;
  final AppointmentBookingData? data;
  final String? message;

  AppointmentBookingResponse({
    required this.code,
    required this.success,
    this.data,
    this.message,
  });

  factory AppointmentBookingResponse.fromJson(Map<String, dynamic> json) {
    return AppointmentBookingResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      data: json['data'] != null
          ? AppointmentBookingData.fromJson(
              json['data'] as Map<String, dynamic>,
            )
          : null,
      message: json['message'] as String?,
    );
  }

  @override
  String toString() {
    return 'AppointmentBookingResponse(code: $code, success: $success, message: $message)';
  }
}

/// Appointment booking data model
class AppointmentBookingData {
  final String appointmentId;
  final String razorpayOrderId;
  final String razorpayKeyId;
  final double amount;
  final String currency;
  final double totalAmount;
  final double discountAmount;

  AppointmentBookingData({
    required this.appointmentId,
    required this.razorpayOrderId,
    required this.razorpayKeyId,
    required this.amount,
    required this.currency,
    required this.totalAmount,
    required this.discountAmount,
  });

  factory AppointmentBookingData.fromJson(Map<String, dynamic> json) {
    return AppointmentBookingData(
      appointmentId: json['appointmentId'] as String? ?? '',
      razorpayOrderId: json['razorpayOrderId'] as String? ?? '',
      razorpayKeyId: json['razorpayKeyId'] as String? ?? '',
      amount: _parseDouble(json['amount']),
      currency: json['currency'] as String? ?? 'INR',
      totalAmount: _parseDouble(json['totalAmount']),
      discountAmount: _parseDouble(json['discountAmount']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Helper getters
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';
  String get formattedTotalAmount => '₹${totalAmount.toStringAsFixed(2)}';
  String get formattedDiscountAmount => '₹${discountAmount.toStringAsFixed(2)}';

  bool get requiresPayment => amount > 0 && razorpayOrderId.isNotEmpty;

  @override
  String toString() {
    return 'AppointmentBookingData(id: $appointmentId, amount: $amount, total: $totalAmount)';
  }
}

/// Payment confirmation request model
class PaymentConfirmationRequest {
  final String appointmentId;
  final String razorpayOrderId;
  final String razorpayPaymentId;
  final String razorpaySignature;

  PaymentConfirmationRequest({
    required this.appointmentId,
    required this.razorpayOrderId,
    required this.razorpayPaymentId,
    required this.razorpaySignature,
  });

  Map<String, dynamic> toJson() {
    return {
      'appointmentId': appointmentId,
      'razorpayOrderId': razorpayOrderId,
      'razorpayPaymentId': razorpayPaymentId,
      'razorpaySignature': razorpaySignature,
    };
  }

  @override
  String toString() {
    return 'PaymentConfirmationRequest(appointmentId: $appointmentId, orderId: $razorpayOrderId)';
  }
}

/// Payment confirmation response model
class PaymentConfirmationResponse {
  final int code;
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;

  PaymentConfirmationResponse({
    required this.code,
    required this.success,
    this.message,
    this.data,
  });

  factory PaymentConfirmationResponse.fromJson(Map<String, dynamic> json) {
    return PaymentConfirmationResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String?,
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  @override
  String toString() {
    return 'PaymentConfirmationResponse(code: $code, success: $success, message: $message)';
  }
}

/// Razorpay payment details model
class RazorpayPaymentDetails {
  final String paymentId;
  final String orderId;
  final String signature;

  RazorpayPaymentDetails({
    required this.paymentId,
    required this.orderId,
    required this.signature,
  });

  factory RazorpayPaymentDetails.fromMap(Map<String, dynamic> response) {
    return RazorpayPaymentDetails(
      paymentId: response['razorpay_payment_id'] as String? ?? '',
      orderId: response['razorpay_order_id'] as String? ?? '',
      signature: response['razorpay_signature'] as String? ?? '',
    );
  }

  bool get isValid {
    return paymentId.isNotEmpty && orderId.isNotEmpty && signature.isNotEmpty;
  }

  @override
  String toString() {
    return 'RazorpayPaymentDetails(paymentId: $paymentId, orderId: $orderId)';
  }
}

/// Booking summary model for display
class BookingSummary {
  final String barberName;
  final List<ServiceSummary> services;
  final String appointmentDate;
  final String appointmentTime;
  final double totalAmount;
  final PaymentMode paymentMode;
  final double payableAmount;

  BookingSummary({
    required this.barberName,
    required this.services,
    required this.appointmentDate,
    required this.appointmentTime,
    required this.totalAmount,
    required this.paymentMode,
    required this.payableAmount,
  });

  String get formattedTotalAmount => '₹${totalAmount.toStringAsFixed(2)}';
  String get formattedPayableAmount => '₹${payableAmount.toStringAsFixed(2)}';

  double get remainingAmount => totalAmount - payableAmount;
  String get formattedRemainingAmount =>
      '₹${remainingAmount.toStringAsFixed(2)}';

  @override
  String toString() {
    return 'BookingSummary(barber: $barberName, services: ${services.length}, total: $totalAmount)';
  }
}

/// Service summary model for booking display
class ServiceSummary {
  final String id;
  final String name;
  final double price;
  final int duration;

  ServiceSummary({
    required this.id,
    required this.name,
    required this.price,
    required this.duration,
  });

  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedDuration => '${duration}min';

  @override
  String toString() {
    return 'ServiceSummary(name: $name, price: $price, duration: ${duration}min)';
  }
}
