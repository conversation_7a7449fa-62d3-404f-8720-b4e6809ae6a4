class NotificationModel {
  final String id;
  final String type;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  final String deliveryStatus;
  final bool isRead;
  final DateTime sentAt;
  final DateTime? deliveredAt;
  final DateTime? readAt;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.type,
    required this.title,
    required this.body,
    required this.data,
    required this.deliveryStatus,
    required this.isRead,
    required this.sentAt,
    this.deliveredAt,
    this.readAt,
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      data: json['data'] ?? {},
      deliveryStatus: json['deliveryStatus'] ?? '',
      isRead: json['isRead'] ?? false,
      sentAt: DateTime.parse(json['sentAt']),
      deliveredAt: json['deliveredAt'] != null 
          ? DateTime.parse(json['deliveredAt']) 
          : null,
      readAt: json['readAt'] != null 
          ? DateTime.parse(json['readAt']) 
          : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'body': body,
      'data': data,
      'deliveryStatus': deliveryStatus,
      'isRead': isRead,
      'sentAt': sentAt.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Helper method to get formatted date (e.g., "20 Dec 2025")
  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    return '${sentAt.day} ${months[sentAt.month - 1]} ${sentAt.year}';
  }

  // Helper method to get formatted time (e.g., "14:15")
  String get formattedTime {
    final hour = sentAt.hour.toString().padLeft(2, '0');
    final minute = sentAt.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // Helper method to get notification icon based on type
  String get notificationIcon {
    switch (type) {
      case 'APPOINTMENT_CONFIRMED':
        return '✅';
      case 'APPOINTMENT_CANCELLED':
        return '❌';
      case 'APPOINTMENT_REMINDER':
        return '⏰';
      case 'PAYMENT_SUCCESS':
        return '💳';
      case 'PAYMENT_FAILED':
        return '⚠️';
      default:
        return '📢';
    }
  }

  // Create a copy with updated read status
  NotificationModel copyWith({
    String? id,
    String? type,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    String? deliveryStatus,
    bool? isRead,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      isRead: isRead ?? this.isRead,
      sentAt: sentAt ?? this.sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class PaginationModel {
  final int page;
  final int limit;
  final int total;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  PaginationModel({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory PaginationModel.fromJson(Map<String, dynamic> json) {
    return PaginationModel(
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      total: json['total'] ?? 0,
      totalPages: json['totalPages'] ?? 1,
      hasNext: json['hasNext'] ?? false,
      hasPrev: json['hasPrev'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'total': total,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrev': hasPrev,
    };
  }
}

class NotificationResponse {
  final int code;
  final bool success;
  final List<NotificationModel> notifications;
  final PaginationModel pagination;

  NotificationResponse({
    required this.code,
    required this.success,
    required this.notifications,
    required this.pagination,
  });

  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? {};
    final notificationsList = data['notifications'] as List<dynamic>? ?? [];
    
    return NotificationResponse(
      code: json['code'] ?? 200,
      success: json['success'] ?? false,
      notifications: notificationsList
          .map((notification) => NotificationModel.fromJson(notification))
          .toList(),
      pagination: PaginationModel.fromJson(data['pagination'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'data': {
        'notifications': notifications.map((n) => n.toJson()).toList(),
        'pagination': pagination.toJson(),
      },
    };
  }
}

class MarkAsReadResponse {
  final int code;
  final bool success;
  final String message;

  MarkAsReadResponse({
    required this.code,
    required this.success,
    required this.message,
  });

  factory MarkAsReadResponse.fromJson(Map<String, dynamic> json) {
    return MarkAsReadResponse(
      code: json['code'] ?? 200,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
    };
  }
}
