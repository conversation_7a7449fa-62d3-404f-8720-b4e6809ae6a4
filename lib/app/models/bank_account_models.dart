/// Bank Account Request Model
class BankAccountRequest {
  final String accountHolderName;
  final String bankName;
  final String bankAccountNumber;
  final String bankIfscCode;
  final String linkedMobileNumber;
  final String upiId;
  final String preferredWithdrawalMethod;

  BankAccountRequest({
    required this.accountHolderName,
    required this.bankName,
    required this.bankAccountNumber,
    required this.bankIfscCode,
    required this.linkedMobileNumber,
    required this.upiId,
    required this.preferredWithdrawalMethod,
  });

  Map<String, dynamic> toJson() {
    return {
      'accountHolderName': accountHolderName,
      'bankName': bankName,
      'bankAccountNumber': bankAccountNumber,
      'bankIfscCode': bankIfscCode,
      'linkedMobileNumber': linkedMobileNumber,
      'upiId': upiId,
      'preferredWithdrawalMethod': preferredWithdrawalMethod,
    };
  }

  @override
  String toString() {
    return 'BankAccountRequest(accountHolderName: $accountHolderName, bankName: $bankName, preferredWithdrawalMethod: $preferredWithdrawalMethod)';
  }
}

/// Bank Account Response Model
class BankAccountResponse {
  final int code;
  final bool success;
  final String message;
  final BankAccountData? data;

  BankAccountResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory BankAccountResponse.fromJson(Map<String, dynamic> json) {
    return BankAccountResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null
          ? BankAccountData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }

  @override
  String toString() {
    return 'BankAccountResponse(code: $code, success: $success, message: $message)';
  }
}

/// Bank Account Data Model
class BankAccountData {
  final String? accountHolderName;
  final String? bankName;
  final String? bankAccountNumber;
  final String? bankIfscCode;
  final String? ifscCode;
  final String? linkedMobileNumber;
  final String? upiId;
  final bool upiVerified;
  final String preferredWithdrawalMethod;

  BankAccountData({
    this.accountHolderName,
    this.bankName,
    this.bankAccountNumber,
    this.bankIfscCode,
    this.ifscCode,
    this.linkedMobileNumber,
    this.upiId,
    required this.upiVerified,
    required this.preferredWithdrawalMethod,
  });

  factory BankAccountData.fromJson(Map<String, dynamic> json) {
    return BankAccountData(
      accountHolderName: json['accountHolderName'] as String?,
      bankName: json['bankName'] as String?,
      bankAccountNumber: json['bankAccountNumber'] as String?,
      bankIfscCode: json['bankIfscCode'] as String?,
      ifscCode: json['ifscCode'] as String?,
      linkedMobileNumber: json['linkedMobileNumber'] as String?,
      upiId: json['upiId'] as String?,
      upiVerified: json['upiVerified'] as bool? ?? false,
      preferredWithdrawalMethod:
          json['preferredWithdrawalMethod'] as String? ?? 'upi',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accountHolderName': accountHolderName,
      'bankName': bankName,
      'bankAccountNumber': bankAccountNumber,
      'bankIfscCode': bankIfscCode,
      'ifscCode': ifscCode,
      'linkedMobileNumber': linkedMobileNumber,
      'upiId': upiId,
      'upiVerified': upiVerified,
      'preferredWithdrawalMethod': preferredWithdrawalMethod,
    };
  }

  // Helper getters
  String get maskedAccountNumber {
    if (bankAccountNumber == null || bankAccountNumber!.length <= 4) {
      return bankAccountNumber ?? '';
    }
    final lastFour = bankAccountNumber!.substring(
      bankAccountNumber!.length - 4,
    );
    return '****$lastFour';
  }

  String get displayName {
    if (bankName != null && bankAccountNumber != null) {
      return '$bankName - $maskedAccountNumber';
    } else if (upiId != null && upiId!.isNotEmpty) {
      return upiId!;
    }
    return 'Bank Account';
  }

  // Check if bank account details are available
  bool get hasBankDetails {
    return accountHolderName != null &&
        bankName != null &&
        bankAccountNumber != null &&
        bankIfscCode != null;
  }

  // Check if UPI details are available
  bool get hasUpiDetails {
    return upiId != null && upiId!.isNotEmpty;
  }

  @override
  String toString() {
    return 'BankAccountData(accountHolderName: $accountHolderName, bankName: $bankName, upiId: $upiId, upiVerified: $upiVerified)';
  }
}

/// Bank Account Error Response Model
class BankAccountErrorResponse {
  final int code;
  final bool success;
  final String message;

  BankAccountErrorResponse({
    required this.code,
    required this.success,
    required this.message,
  });

  factory BankAccountErrorResponse.fromJson(Map<String, dynamic> json) {
    return BankAccountErrorResponse(
      code: json['code'] as int? ?? 400,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? 'An error occurred',
    );
  }

  Map<String, dynamic> toJson() {
    return {'code': code, 'success': success, 'message': message};
  }

  @override
  String toString() {
    return 'BankAccountErrorResponse(code: $code, success: $success, message: $message)';
  }
}

/// Withdrawal Method Enum
enum WithdrawalMethod {
  upi('upi'),
  bank('bank');

  const WithdrawalMethod(this.value);
  final String value;

  static WithdrawalMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'upi':
        return WithdrawalMethod.upi;
      case 'bank':
        return WithdrawalMethod.bank;
      default:
        return WithdrawalMethod.upi;
    }
  }

  String get displayName {
    switch (this) {
      case WithdrawalMethod.upi:
        return 'UPI';
      case WithdrawalMethod.bank:
        return 'Bank Transfer';
    }
  }
}

/// Bank Account Validation Helper
class BankAccountValidator {
  static bool isValidAccountNumber(String accountNumber) {
    // Remove spaces and check if it's numeric and between 9-18 digits
    final cleanNumber = accountNumber.replaceAll(' ', '');
    return RegExp(r'^\d{9,18}$').hasMatch(cleanNumber);
  }

  static bool isValidIFSC(String ifsc) {
    // IFSC format: 4 letters + 7 digits
    return RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$').hasMatch(ifsc.toUpperCase());
  }

  static bool isValidUPI(String upiId) {
    // UPI format: username@bankname
    return RegExp(r'^[\w\.-]+@[\w\.-]+$').hasMatch(upiId);
  }

  static bool isValidMobileNumber(String mobile) {
    // Indian mobile number format
    final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');
    return RegExp(r'^[6-9]\d{9}$').hasMatch(cleanMobile);
  }

  static String? validateAccountHolderName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Account holder name is required';
    }
    if (name.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(name.trim())) {
      return 'Name can only contain letters and spaces';
    }
    return null;
  }

  static String? validateBankName(String? bankName) {
    if (bankName == null || bankName.trim().isEmpty) {
      return 'Bank name is required';
    }
    if (bankName.trim().length < 2) {
      return 'Bank name must be at least 2 characters';
    }
    return null;
  }

  static String? validateAccountNumber(String? accountNumber) {
    if (accountNumber == null || accountNumber.trim().isEmpty) {
      return 'Account number is required';
    }
    if (!isValidAccountNumber(accountNumber)) {
      return 'Please enter a valid account number (9-18 digits)';
    }
    return null;
  }

  static String? validateIFSC(String? ifsc) {
    if (ifsc == null || ifsc.trim().isEmpty) {
      return 'IFSC code is required';
    }
    if (!isValidIFSC(ifsc)) {
      return 'Please enter a valid IFSC code';
    }
    return null;
  }

  static String? validateMobileNumber(String? mobile) {
    if (mobile == null || mobile.trim().isEmpty) {
      return 'Mobile number is required';
    }
    if (!isValidMobileNumber(mobile)) {
      return 'Please enter a valid 10-digit mobile number';
    }
    return null;
  }

  static String? validateUPI(String? upiId) {
    if (upiId == null || upiId.trim().isEmpty) {
      return 'UPI ID is required';
    }
    if (!isValidUPI(upiId)) {
      return 'Please enter a valid UPI ID (e.g., user@bank)';
    }
    return null;
  }
}
