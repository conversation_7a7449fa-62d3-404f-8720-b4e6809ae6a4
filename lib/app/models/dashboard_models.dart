import 'dart:convert';

/// Main dashboard response model
class DashboardResponse {
  final bool success;
  final DashboardData? data;
  final String message;
  final String timestamp;

  DashboardResponse({
    required this.success,
    this.data,
    required this.message,
    required this.timestamp,
  });

  factory DashboardResponse.fromJson(Map<String, dynamic> json) {
    return DashboardResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] != null
          ? DashboardData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
      message: json['message'] as String? ?? '',
      timestamp: json['timestamp'] as String? ?? '',
    );
  }

  factory DashboardResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return DashboardResponse.fromJson(json);
    } catch (e) {
      return DashboardResponse(
        success: false,
        data: null,
        message: 'Failed to parse response',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data?.toJson(),
      'message': message,
      'timestamp': timestamp,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }

  @override
  String toString() {
    return 'DashboardResponse(success: $success, data: $data, message: $message, timestamp: $timestamp)';
  }
}

/// Dashboard data containing all salon information
class DashboardData {
  final List<SalonModel> nearestSalons;
  final List<SalonModel> popularSalons;
  final HomeSalonModel? homeSaloon;

  DashboardData({
    required this.nearestSalons,
    required this.popularSalons,
    this.homeSaloon,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      nearestSalons: _parseSalonList(json['nearestSalons']),
      popularSalons: _parseSalonList(json['popularSalons']),
      homeSaloon: json['homeSaloon'] != null
          ? HomeSalonModel.fromJson(json['homeSaloon'] as Map<String, dynamic>)
          : null,
    );
  }

  static List<SalonModel> _parseSalonList(dynamic salonData) {
    if (salonData == null) return [];

    if (salonData is List) {
      return salonData
          .where((item) => item != null)
          .map((item) => SalonModel.fromJson(item as Map<String, dynamic>))
          .toList();
    }

    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'nearestSalons': nearestSalons.map((salon) => salon.toJson()).toList(),
      'popularSalons': popularSalons.map((salon) => salon.toJson()).toList(),
      'homeSaloon': homeSaloon?.toJson(),
    };
  }

  @override
  String toString() {
    return 'DashboardData(nearestSalons: ${nearestSalons.length}, popularSalons: ${popularSalons.length}, homeSaloon: $homeSaloon)';
  }
}

/// Basic salon model for nearest and popular salons
class SalonModel {
  final String id;
  final String name;
  final String image;
  final String shopNo;
  final String city;
  final double stars;
  final String? distance;
  final double? distanceInKm;

  SalonModel({
    required this.id,
    required this.name,
    required this.image,
    required this.shopNo,
    required this.city,
    required this.stars,
    this.distance,
    this.distanceInKm,
  });

  factory SalonModel.fromJson(Map<String, dynamic> json) {
    return SalonModel(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      image: json['image'] as String? ?? '',
      shopNo: json['shopNo'] as String? ?? '',
      city: json['city'] as String? ?? '',
      stars: _parseDouble(json['stars']),
      distance: json['distance'] as String?,
      distanceInKm: json['distanceInKm'] != null
          ? _parseDouble(json['distanceInKm'])
          : null,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'shopNo': shopNo,
      'city': city,
      'stars': stars,
      'distance': distance,
      'distanceInKm': distanceInKm,
    };
  }

  // Helper getters
  String get displayName => name.isNotEmpty ? name : 'Unknown Salon';
  String get displayLocation => city.isNotEmpty ? city : 'Unknown Location';
  String get displayShopNo => shopNo.isNotEmpty ? 'Shop #$shopNo' : '';
  String get displayDistance => distance?.isNotEmpty == true ? distance! : '';
  String get displayDistanceKm {
    if (distanceInKm != null) {
      return '${distanceInKm!.toStringAsFixed(1)} km';
    }
    return ''; // Only hide when distanceInKm is null
  }

  bool get hasImage => image.isNotEmpty;
  bool get hasDistance =>
      distance?.isNotEmpty == true ||
      (distanceInKm != null && distanceInKm! > 0);
  String get starsDisplay => stars > 0 ? stars.toStringAsFixed(1) : '0.0';

  @override
  String toString() {
    return 'SalonModel(id: $id, name: $name, city: $city, stars: $stars)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalonModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Extended salon model for home salon with additional details
class HomeSalonModel extends SalonModel {
  final String area;
  final String state;
  final String country;
  final String lastVisit;

  HomeSalonModel({
    required super.id,
    required super.name,
    required super.image,
    required super.shopNo,
    required super.city,
    required super.stars,
    super.distance,
    super.distanceInKm,
    required this.area,
    required this.state,
    required this.country,
    required this.lastVisit,
  });

  factory HomeSalonModel.fromJson(Map<String, dynamic> json) {
    return HomeSalonModel(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      image: json['image'] as String? ?? '',
      shopNo: json['shopNo'] as String? ?? '',
      city: json['city'] as String? ?? '',
      stars: SalonModel._parseDouble(json['stars']),
      distance: json['distance'] as String?,
      distanceInKm: json['distanceInKm'] != null
          ? SalonModel._parseDouble(json['distanceInKm'])
          : null,
      area: json['area'] as String? ?? '',
      state: json['state'] as String? ?? '',
      country: json['country'] as String? ?? '',
      lastVisit: json['lastVisit'] as String? ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final baseJson = super.toJson();
    baseJson.addAll({
      'area': area,
      'state': state,
      'country': country,
      'lastVisit': lastVisit,
    });
    return baseJson;
  }

  // Helper getters for home salon
  String get fullAddress {
    final addressParts = [
      area,
      city,
      state,
      country,
    ].where((part) => part.isNotEmpty).toList();
    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Address not available';
  }

  String get shortAddress {
    final addressParts = [area, city].where((part) => part.isNotEmpty).toList();
    return addressParts.isNotEmpty ? addressParts.join(', ') : city;
  }

  String get displayLastVisit =>
      lastVisit.isNotEmpty ? lastVisit : 'Never visited';

  @override
  String toString() {
    return 'HomeSalonModel(id: $id, name: $name, fullAddress: $fullAddress, lastVisit: $lastVisit)';
  }
}

/// Error response model for handling API errors
class DashboardErrorResponse {
  final bool success;
  final String message;
  final String? error;
  final int? statusCode;

  DashboardErrorResponse({
    required this.success,
    required this.message,
    this.error,
    this.statusCode,
  });

  factory DashboardErrorResponse.fromJson(Map<String, dynamic> json) {
    return DashboardErrorResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? 'Unknown error occurred',
      error: json['error'] as String?,
      statusCode: json['statusCode'] as int?,
    );
  }

  factory DashboardErrorResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return DashboardErrorResponse.fromJson(json);
    } catch (e) {
      return DashboardErrorResponse(
        success: false,
        message: 'Failed to parse error response',
        error: e.toString(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'statusCode': statusCode,
    };
  }

  @override
  String toString() {
    return 'DashboardErrorResponse(success: $success, message: $message, error: $error)';
  }
}
