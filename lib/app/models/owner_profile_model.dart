// Owner Profile Response Model
class OwnerProfileResponse {
  final bool success;
  final OwnerProfileData? data;
  final String? message;

  OwnerProfileResponse({required this.success, this.data, this.message});

  factory OwnerProfileResponse.fromJson(Map<String, dynamic> json) {
    return OwnerProfileResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] != null
          ? OwnerProfileData.fromJson(json['data'])
          : null,
      message: json['message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data?.toJson(), 'message': message};
  }
}

// Owner Profile Data Model
class OwnerProfileData {
  final String firstName;
  final String lastName;
  final String saloonName;
  final String salonDescription;
  final String email;
  final String phone;
  final String offDays;
  final String startTime;
  final String endTime;
  final String salonRegId;
  final String salonProfile;
  final String shopNo;
  final OwnerAddress? address;
  final List<SaloonImage> saloonImages;

  OwnerProfileData({
    required this.firstName,
    required this.lastName,
    required this.saloonName,
    required this.salonDescription,
    required this.email,
    required this.phone,
    required this.salonProfile,
    required this.offDays,
    required this.startTime,
    required this.endTime,
    required this.salonRegId,
    required this.shopNo,
    this.address,
    required this.saloonImages,
  });

  factory OwnerProfileData.fromJson(Map<String, dynamic> json) {
    return OwnerProfileData(
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      saloonName: json['saloonName'] as String? ?? '',
      salonDescription: json['salonDescription'] as String? ?? '',
      email: json['email'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      offDays: json['offDays'] as String? ?? '',
      startTime: json['startTime'] as String? ?? '',
      endTime: json['endTime'] as String? ?? '',
      salonRegId: json['salonRegId'] as String? ?? '',
      shopNo: json['shopNo'] as String? ?? '',
      address: json['address'] != null
          ? OwnerAddress.fromJson(json['address'])
          : null,
      saloonImages:
          (json['saloonImages'] as List<dynamic>?)
              ?.map((item) => SaloonImage.fromJson(item))
              .toList() ??
          [],
      salonProfile: json['saloonProfie'] as String ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'saloonName': saloonName,
      'salonDescription': salonDescription,
      'email': email,
      'phone': phone,
      'offDays': offDays,
      'startTime': startTime,
      'endTime': endTime,
      'salonRegId': salonRegId,
      'shopNo': shopNo,
      'address': address?.toJson(),
      'saloonImages': saloonImages.map((image) => image.toJson()).toList(),
    };
  }

  // Helper getters
  String get fullName => '$firstName $lastName'.trim();
  String get fullAddress {
    if (address == null) return '';
    final parts = [
      address!.area,
      address!.city,
      address!.state,
      address!.country,
    ].where((part) => part.isNotEmpty).toList();
    return parts.join(', ');
  }

  // Helper method to format working hours
  String get workingHours => '$startTime - $endTime';
}

// Owner Address Model
class OwnerAddress {
  final String id;
  final String? pincode;
  final String area;
  final String city;
  final String state;
  final String country;

  OwnerAddress({
    required this.id,
    this.pincode,
    required this.area,
    required this.city,
    required this.state,
    required this.country,
  });

  factory OwnerAddress.fromJson(Map<String, dynamic> json) {
    return OwnerAddress(
      id: json['id'] as String? ?? '',
      pincode: json['pincode'] as String?,
      area: json['area'] as String? ?? '',
      city: json['city'] as String? ?? '',
      state: json['state'] as String? ?? '',
      country: json['country'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pincode': pincode,
      'area': area,
      'city': city,
      'state': state,
      'country': country,
    };
  }
}

// Saloon Image Model
class SaloonImage {
  final String id;
  final String saloonId;
  final String imageUrl;
  final String? altText;
  final String createdAt;
  final String updatedAt;

  SaloonImage({
    required this.id,
    required this.saloonId,
    required this.imageUrl,
    this.altText,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SaloonImage.fromJson(Map<String, dynamic> json) {
    return SaloonImage(
      id: json['id'] as String? ?? '',
      saloonId: json['saloonId'] as String? ?? '',
      imageUrl: json['imageUrl'] as String? ?? '',
      altText: json['altText'] as String?,
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'saloonId': saloonId,
      'imageUrl': imageUrl,
      'altText': altText,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}

// Owner Profile Update Request Model
class OwnerProfileUpdateRequest {
  final String? firstName;
  final String? lastName;
  final String? salonName;
  final String? salonDescription;
  final String? salonPhone;
  final String? salonEmail;
  final String? area;
  final String? city;
  final String? state;
  final String? country;
  final String? pinCode;
  final String? salonRegId;
  final String? shopNo;
  final String? offDays;
  final String? startTime;
  final String? endTime;

  OwnerProfileUpdateRequest({
    this.firstName,
    this.lastName,
    this.salonName,
    this.salonDescription,
    this.salonPhone,
    this.salonEmail,
    this.area,
    this.city,
    this.state,
    this.country,
    this.pinCode,
    this.salonRegId,
    this.shopNo,
    this.offDays,
    this.startTime,
    this.endTime,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (firstName != null && firstName!.isNotEmpty) {
      data['firstName'] = firstName;
    }
    if (lastName != null && lastName!.isNotEmpty) {
      data['lastName'] = lastName;
    }
    if (salonName != null && salonName!.isNotEmpty) {
      data['salonName'] = salonName;
    }
    if (salonDescription != null && salonDescription!.isNotEmpty) {
      data['salonDescription'] = salonDescription;
    }
    if (salonPhone != null && salonPhone!.isNotEmpty) {
      data['salonPhone'] = salonPhone;
    }
    if (salonEmail != null && salonEmail!.isNotEmpty) {
      data['salonEmail'] = salonEmail;
    }
    if (area != null && area!.isNotEmpty) {
      data['area'] = area;
    }
    if (city != null && city!.isNotEmpty) {
      data['city'] = city;
    }
    if (state != null && state!.isNotEmpty) {
      data['state'] = state;
    }
    if (country != null && country!.isNotEmpty) {
      data['country'] = country;
    }
    if (pinCode != null && pinCode!.isNotEmpty) {
      data['pinCode'] = pinCode;
    }
    if (salonRegId != null && salonRegId!.isNotEmpty) {
      data['salonRegId'] = salonRegId;
    }
    if (shopNo != null && shopNo!.isNotEmpty) {
      data['shopNo'] = shopNo;
    }
    if (offDays != null && offDays!.isNotEmpty) {
      data['offDays'] = offDays;
    }
    if (startTime != null && startTime!.isNotEmpty) {
      data['startTime'] = startTime;
    }
    if (endTime != null && endTime!.isNotEmpty) {
      data['endTime'] = endTime;
    }

    return data;
  }

  @override
  String toString() {
    return 'OwnerProfileUpdateRequest(${toJson()})';
  }
}
