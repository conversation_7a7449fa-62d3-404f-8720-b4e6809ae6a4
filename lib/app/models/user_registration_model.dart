/// User Registration Request Model for /auth/profile/user endpoint
class UserRegistrationRequest {
  final String firstName;
  final String lastName;
  final String phone;
  final String email;
  final String? gender;
  final String? profileImage;
  final int? pincode;
  final String area;
  final String city;
  final String state;
  final String country;

  UserRegistrationRequest({
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    this.gender,
    this.profileImage,
    this.pincode,
    required this.area,
    required this.city,
    required this.state,
    required this.country,
  });

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{
      'firstName': firstName,
      'lastName': lastName,
      'phone': phone,
      'email': email,
      'area': area,
      'city': city,
      'state': state,
      'country': country,
    };

    // Add optional fields only if they have values
    if (gender != null && gender!.isNotEmpty) {
      map['gender'] = gender;
    }

    if (profileImage != null && profileImage!.isNotEmpty) {
      map['profileImage'] = profileImage;
    }

    if (pincode != null) {
      map['pincode'] = pincode;
    }

    return map;
  }

  factory UserRegistrationRequest.fromJson(Map<String, dynamic> json) {
    return UserRegistrationRequest(
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      email: json['email'] as String? ?? '',
      gender: json['gender'] as String?,
      profileImage: json['profileImage'] as String?,
      pincode: json['pincode'] as int?,
      area: json['area'] as String? ?? '',
      city: json['city'] as String? ?? '',
      state: json['state'] as String? ?? '',
      country: json['country'] as String? ?? '',
    );
  }

  @override
  String toString() {
    return 'UserRegistrationRequest(firstName: $firstName, lastName: $lastName, phone: $phone, email: $email, area: $area, city: $city, state: $state, country: $country)';
  }
}

/// Owner Registration Request Model for /auth/profile/owner endpoint
class OwnerRegistrationRequest {
  final String firstName;
  final String lastName;
  final String salonName;
  final String salonDescription;
  final String salonPhone;
  final String salonEmail;
  final String area;
  final String city;
  final String state;
  final String country;
  final String pinCode;
  final String salonRegid;
  final String shopNo;
  final String? logoUrl;
  final String? actualaddress;
  final double? lat;
  final double? long;

  OwnerRegistrationRequest({
    required this.firstName,
    required this.lastName,
    required this.salonName,
    required this.salonDescription,
    required this.salonPhone,
    required this.salonEmail,
    required this.area,
    required this.city,
    required this.state,
    required this.country,
    required this.pinCode,
    required this.salonRegid,
    required this.shopNo,
    this.logoUrl,
    this.actualaddress,
    this.lat,
    this.long,
  });

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{
      'firstName': firstName,
      'lastName': lastName,
      'salonName': salonName,
      'salonDescription': salonDescription,
      'salonPhone': salonPhone,
      'salonEmail': salonEmail,
      'area': area,
      'city': city,
      'state': state,
      'country': country,
      'pinCode': pinCode,
      'salonRegId': salonRegid,
      'shopNo': shopNo,
    };

    // Add logoUrl only if it's not null and not empty
    if (logoUrl != null && logoUrl!.isNotEmpty) {
      map['logoUrl'] = logoUrl;
    }

    // Add location fields if available
    if (actualaddress != null && actualaddress!.isNotEmpty) {
      map['actualAddress'] = actualaddress;
    }
    if (lat != null) {
      map['latitude'] = lat;
    }
    if (long != null) {
      map['longitude'] = long;
    }

    return map;
  }

  factory OwnerRegistrationRequest.fromJson(Map<String, dynamic> json) {
    return OwnerRegistrationRequest(
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      salonName: json['salonName'] as String? ?? '',
      salonDescription: json['salonDescription'] as String? ?? '',
      salonPhone: json['salonPhone'] as String? ?? '',
      salonEmail: json['salonEmail'] as String? ?? '',
      area: json['area'] as String? ?? '',
      city: json['city'] as String? ?? '',
      state: json['state'] as String? ?? '',
      country: json['country'] as String? ?? '',
      pinCode: json['pinCode'] as String? ?? '',
      salonRegid: json['salonRegId'] as String? ?? '',
      shopNo: json['shopNo'] as String? ?? '',
      logoUrl: json['logoUrl'] as String?,
      actualaddress:
          json['actualAddress'] as String? ?? json['actualaddress'] as String?,
      lat: json['latitude'] as double? ?? json['lat'] as double?,
      long: json['longitude'] as double? ?? json['long'] as double?,
    );
  }

  @override
  String toString() {
    return 'OwnerRegistrationRequest(firstName: $firstName, lastName: $lastName, salonName: $salonName, salonEmail: $salonEmail, area: $area, city: $city, state: $state, country: $country)';
  }
}

/// Registration Response Model (common for both user and owner)
class RegistrationResponse {
  final bool success;
  final String message;
  final String? userId;
  final String? saloonId;

  RegistrationResponse({
    required this.success,
    required this.message,
    this.userId,
    this.saloonId,
  });

  factory RegistrationResponse.fromJson(Map<String, dynamic> json) {
    return RegistrationResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      userId: json['userId'] as String?,
      saloonId: json['saloonId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'userId': userId,
      'saloonId': saloonId,
    };
  }

  @override
  String toString() {
    return 'RegistrationResponse(success: $success, message: $message, userId: $userId, saloonId: $saloonId)';
  }
}

/// Gender enum for user registration
enum Gender {
  male('male'),
  female('female'),
  other('other');

  const Gender(this.value);
  final String value;

  static Gender? fromString(String? value) {
    if (value == null || value.isEmpty) return null;

    switch (value.toLowerCase()) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      case 'other':
        return Gender.other;
      default:
        return null;
    }
  }
}

/// Validation helper for registration data
class RegistrationValidator {
  /// Validate user registration data
  static String? validateUserRegistration(UserRegistrationRequest request) {
    if (request.firstName.trim().isEmpty) {
      return 'First name is required';
    }

    if (request.lastName.trim().isEmpty) {
      return 'Last name is required';
    }

    if (request.phone.trim().length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    if (!_isValidEmail(request.email)) {
      return 'Invalid email format';
    }

    if (request.area.trim().isEmpty) {
      return 'Area is required';
    }

    if (request.city.trim().isEmpty) {
      return 'City is required';
    }

    if (request.state.trim().isEmpty) {
      return 'State is required';
    }

    if (request.country.trim().isEmpty) {
      return 'Country is required';
    }

    if (request.pincode != null) {
      if (request.pincode! < 100000 || request.pincode! > 999999) {
        return 'Invalid pincode format';
      }
    }

    return null; // Valid
  }

  /// Validate owner registration data
  static String? validateOwnerRegistration(OwnerRegistrationRequest request) {
    final userValidation = validateUserRegistration(
      UserRegistrationRequest(
        firstName: request.firstName,
        lastName: request.lastName,
        phone: request.salonPhone,
        email: request.salonEmail,
        area: request.area,
        city: request.city,
        state: request.state,
        country: request.country,
      ),
    );

    if (userValidation != null) return userValidation;

    if (request.salonName.trim().isEmpty) {
      return 'Salon name is required';
    }

    if (request.salonDescription.trim().isEmpty) {
      return 'Salon description is required';
    }

    if (request.salonRegid.trim().isEmpty) {
      return 'Salon registration ID is required';
    }

    return null; // Valid
  }

  /// Validate email format
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email.trim());
  }
}
