/// Owner Dashboard Response Model
class OwnerDashboardResponse {
  final bool success;
  final OwnerDashboardData? data;
  final String message;
  final String timestamp;

  OwnerDashboardResponse({
    required this.success,
    this.data,
    required this.message,
    required this.timestamp,
  });

  factory OwnerDashboardResponse.fromJson(Map<String, dynamic> json) {
    return OwnerDashboardResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] != null
          ? OwnerDashboardData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
      message: json['message'] as String? ?? '',
      timestamp: json['timestamp'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data?.toJson(),
      'message': message,
      'timestamp': timestamp,
    };
  }

  @override
  String toString() {
    return 'OwnerDashboardResponse(success: $success, data: $data, message: $message)';
  }
}

/// Owner Dashboard Data Model
class OwnerDashboardData {
  final double availableBalance;
  final double lockedBalance;
  final double totalEarning;
  final bool isLocked;
  final String? lockedUntil;
  final AppointmentData appointmentData;

  // Computed properties for backward compatibility
  double get weeklyEarnings => totalEarning;
  int get weeklyBookings => appointmentData.totalAppointments;
  double get bankBalance => availableBalance;
  double get earningsGrowth => 0.0; // Default value since not provided by API
  double get bookingsGrowth => 0.0; // Default value since not provided by API

  OwnerDashboardData({
    required this.availableBalance,
    required this.lockedBalance,
    required this.totalEarning,
    required this.isLocked,
    this.lockedUntil,
    required this.appointmentData,
  });

  factory OwnerDashboardData.fromJson(Map<String, dynamic> json) {
    return OwnerDashboardData(
      availableBalance: _parseDouble(json['availableBalance']),
      lockedBalance: _parseDouble(json['lockedBalance']),
      totalEarning: _parseDouble(json['totalEarning']),
      isLocked: json['isLocked'] as bool? ?? false,
      lockedUntil: json['lockedUntil'] as String?,
      appointmentData: json['appointmentData'] != null
          ? AppointmentData.fromJson(
              json['appointmentData'] as Map<String, dynamic>,
            )
          : AppointmentData(
              pending: [],
              completed: [],
              cancelled: [],
              confirmed: [],
            ),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'availableBalance': availableBalance,
      'lockedBalance': lockedBalance,
      'totalEarning': totalEarning,
      'isLocked': isLocked,
      'lockedUntil': lockedUntil,
      'appointmentData': appointmentData.toJson(),
    };
  }

  @override
  String toString() {
    return 'OwnerDashboardData(availableBalance: $availableBalance, totalEarning: $totalEarning, isLocked: $isLocked)';
  }
}

/// Appointment Data Model
class AppointmentData {
  final List<Appointment> pending;
  final List<Appointment> completed;
  final List<Appointment> cancelled;
  final List<Appointment> confirmed;

  AppointmentData({
    required this.pending,
    this.completed = const [],
    this.cancelled = const [],
    this.confirmed = const [],
  });

  factory AppointmentData.fromJson(Map<String, dynamic> json) {
    return AppointmentData(
      pending: _parseAppointmentList(json['pending']),
      completed: _parseAppointmentList(json['completed']),
      cancelled: _parseAppointmentList(json['cancelled']),
      confirmed: _parseAppointmentList(json['confirmed']),
    );
  }

  static List<Appointment> _parseAppointmentList(dynamic appointmentData) {
    if (appointmentData == null) return [];

    if (appointmentData is List) {
      return appointmentData
          .where((item) => item != null)
          .map((item) => Appointment.fromJson(item as Map<String, dynamic>))
          .toList();
    }

    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'pending': pending.map((appointment) => appointment.toJson()).toList(),
      'completed': completed
          .map((appointment) => appointment.toJson())
          .toList(),
      'cancelled': cancelled
          .map((appointment) => appointment.toJson())
          .toList(),
      'confirmed': confirmed
          .map((appointment) => appointment.toJson())
          .toList(),
    };
  }

  int get totalAppointments =>
      pending.length + completed.length + cancelled.length + confirmed.length;

  @override
  String toString() {
    return 'AppointmentData(pending: ${pending.length}, completed: ${completed.length}, cancelled: ${cancelled.length}, confirmed: ${confirmed.length})';
  }
}

/// Appointment Model
class Appointment {
  final String id;
  final String barberName;
  final String userName;
  final double amount;
  final String bookingType;
  final String status;
  final String startTime;
  final String endTime;
  final String appointmentDate;
  final String createdAt;

  // Computed properties for backward compatibility
  String get customerName => userName;
  String get customerPhone => '';
  String get services => 'Service'; // Default since not provided
  String get appointmentTime => startTime;
  double get totalAmount => amount;
  String get barberId => '';

  Appointment({
    required this.id,
    required this.barberName,
    required this.userName,
    required this.amount,
    required this.bookingType,
    required this.status,
    required this.startTime,
    required this.endTime,
    required this.appointmentDate,
    required this.createdAt,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id'] as String? ?? '',
      barberName: json['barberName'] as String? ?? '',
      userName: json['userName'] as String? ?? '',
      amount: _parseDouble(json['amount']),
      bookingType: json['bookingType'] as String? ?? '',
      status: json['status'] as String? ?? '',
      startTime: json['startTime'] as String? ?? '',
      endTime: json['endTime'] as String? ?? '',
      appointmentDate: json['appointmentDate'] as String? ?? '',
      createdAt: json['createdAt'] as String? ?? '',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'barberName': barberName,
      'userName': userName,
      'amount': amount,
      'bookingType': bookingType,
      'status': status,
      'startTime': startTime,
      'endTime': endTime,
      'appointmentDate': appointmentDate,
      'createdAt': createdAt,
    };
  }

  // Helper getters
  String get displayAmount => '₹${amount.toStringAsFixed(0)}';
  String get displayTime => startTime.isNotEmpty ? startTime : 'Time not set';
  String get displayDate =>
      appointmentDate.isNotEmpty ? appointmentDate : 'Date not set';
  String get displayServices => 'Service'; // Default since not provided by API
  String get displayCustomer =>
      userName.isNotEmpty ? userName : 'Unknown Customer';

  @override
  String toString() {
    return 'Appointment(id: $id, customer: $userName, date: $appointmentDate, status: $status)';
  }
}

/// Weekly Data Response Model
class WeeklyDataResponse {
  final bool success;
  final List<double> weeklyData;
  final String message;

  WeeklyDataResponse({
    required this.success,
    required this.weeklyData,
    required this.message,
  });

  factory WeeklyDataResponse.fromJson(Map<String, dynamic> json) {
    return WeeklyDataResponse(
      success: json['success'] as bool? ?? false,
      weeklyData: _parseDoubleList(json['data']),
      message: json['message'] as String? ?? '',
    );
  }

  static List<double> _parseDoubleList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.map((item) {
        if (item is double) return item;
        if (item is int) return item.toDouble();
        if (item is String) return double.tryParse(item) ?? 0.0;
        return 0.0;
      }).toList();
    }

    return [];
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': weeklyData, 'message': message};
  }

  @override
  String toString() {
    return 'WeeklyDataResponse(success: $success, dataPoints: ${weeklyData.length})';
  }
}

/// Monthly Data Response Model
class MonthlyDataResponse {
  final bool success;
  final List<double> monthlyData;
  final String message;

  MonthlyDataResponse({
    required this.success,
    required this.monthlyData,
    required this.message,
  });

  factory MonthlyDataResponse.fromJson(Map<String, dynamic> json) {
    return MonthlyDataResponse(
      success: json['success'] as bool? ?? false,
      monthlyData: _parseDoubleList(json['data']),
      message: json['message'] as String? ?? '',
    );
  }

  static List<double> _parseDoubleList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.map((item) {
        if (item is double) return item;
        if (item is int) return item.toDouble();
        if (item is String) return double.tryParse(item) ?? 0.0;
        return 0.0;
      }).toList();
    }

    return [];
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': monthlyData, 'message': message};
  }

  @override
  String toString() {
    return 'MonthlyDataResponse(success: $success, dataPoints: ${monthlyData.length})';
  }
}
