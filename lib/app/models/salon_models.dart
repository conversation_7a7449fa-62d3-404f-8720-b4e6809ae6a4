class Salon {
  final String id;
  final String name;
  final String address;
  final String image;
  final String profileImage;
  final double rating;
  final String distance;
  final List<String> services;
  final bool isOpen;
  final String phoneNumber;
  final String email;

  Salon({
    required this.id,
    required this.name,
    required this.address,
    required this.image,
    required this.profileImage,
    required this.rating,
    required this.distance,
    required this.services,
    required this.isOpen,
    required this.phoneNumber,
    required this.email,
  });

  factory Salon.fromJson(Map<String, dynamic> json) {
    return Salon(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      image: json['image'] ?? '',
      profileImage: json['profileImage'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      distance: json['distance'] ?? '',
      services: List<String>.from(json['services'] ?? []),
      isOpen: json['isOpen'] ?? false,
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'image': image,
      'profileImage': profileImage,
      'rating': rating,
      'distance': distance,
      'services': services,
      'isOpen': isOpen,
      'phoneNumber': phoneNumber,
      'email': email,
    };
  }
}

class HomeSalon {
  final String id;
  final String name;
  final String address;
  final String image;
  final String profileImage;
  final DateTime lastVisit;
  final String lastService;

  HomeSalon({
    required this.id,
    required this.name,
    required this.address,
    required this.image,
    required this.profileImage,
    required this.lastVisit,
    required this.lastService,
  });

  factory HomeSalon.fromJson(Map<String, dynamic> json) {
    return HomeSalon(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      image: json['image'] ?? '',
      profileImage: json['profileImage'] ?? '',
      lastVisit: DateTime.tryParse(json['lastVisit'] ?? '') ?? DateTime.now(),
      lastService: json['lastService'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'image': image,
      'profileImage': profileImage,
      'lastVisit': lastVisit.toIso8601String(),
      'lastService': lastService,
    };
  }
}
