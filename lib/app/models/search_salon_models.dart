import 'package:flutter/material.dart';

/// Response model for search salon API
class SearchSalonResponse {
  final int code;
  final bool success;
  final String message;
  final List<SearchSalon> data;

  SearchSalonResponse({
    required this.code,
    required this.success,
    required this.message,
    required this.data,
  });

  factory SearchSalonResponse.fromJson(Map<String, dynamic> json) {
    return SearchSalonResponse(
      code: json['code'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => SearchSalon.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data.map((salon) => salon.toJson()).toList(),
    };
  }
}

/// Individual search salon model
class SearchSalon {
  final String name;
  final String? image;
  final String shopNo;
  final String? area;
  final String? city;
  final String? state;
  final String? id;
  final double? rating;
  final int? reviewCount;

  SearchSalon({
    required this.name,
    this.image,
    required this.shopNo,
    this.area,
    this.city,
    this.state,
    this.id,
    this.rating,
    this.reviewCount,
  });

  factory SearchSalon.fromJson(Map<String, dynamic> json) {
    return SearchSalon(
      name: json['name']?.toString() ?? '',
      image: json['image']?.toString(),
      shopNo: json['shopNo']?.toString() ?? '',
      area: json['area']?.toString(),
      city: json['city']?.toString(),
      state: json['state']?.toString(),
      id: json['id']?.toString(),
      rating: (json['rating'] ?? json['stars'])?.toDouble(),
      reviewCount: json['reviewCount'] ?? json['totalReviews'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'image': image,
      'shopNo': shopNo,
      'area': area,
      'city': city,
      'state': state,
      'id': id,
      'rating': rating,
      'reviewCount': reviewCount,
    };
  }

  /// Helper method to get full address
  String get fullAddress {
    List<String> addressParts = [];

    if (shopNo.isNotEmpty) addressParts.add('Shop $shopNo');
    if (area != null && area!.isNotEmpty) addressParts.add(area!);
    if (city != null && city!.isNotEmpty) addressParts.add(city!);
    if (state != null && state!.isNotEmpty) addressParts.add(state!);

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Address not available';
  }

  /// Helper method to get short address (area, city)
  String get shortAddress {
    List<String> addressParts = [];

    if (area != null && area!.isNotEmpty) addressParts.add(area!);
    if (city != null && city!.isNotEmpty) addressParts.add(city!);

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Location not available';
  }

  /// Helper method to check if image is available
  bool get hasImage => image != null && image!.isNotEmpty;

  /// Helper method to get formatted rating
  String get formattedRating {
    if (rating == null) return 'No rating';
    return rating!.toStringAsFixed(1);
  }

  /// Helper method to get review text
  String get reviewText {
    if (reviewCount == null || reviewCount == 0) return 'No reviews';
    return reviewCount == 1 ? '1 review' : '$reviewCount reviews';
  }

  /// Helper method to check if salon has rating
  bool get hasRating => rating != null && rating! > 0;
}

/// Request model for search API
class SearchRequest {
  final String type;
  final String value;

  SearchRequest({required this.type, required this.value});

  Map<String, dynamic> toJson() {
    return {'type': type, 'value': value};
  }
}

/// Search filter options
enum SearchFilterType { all, salon, service, barber }

extension SearchFilterTypeExtension on SearchFilterType {
  String get displayName {
    switch (this) {
      case SearchFilterType.all:
        return 'All';
      case SearchFilterType.salon:
        return 'Salon';
      case SearchFilterType.service:
        return 'Service';
      case SearchFilterType.barber:
        return 'Barber';
    }
  }

  String get apiValue {
    switch (this) {
      case SearchFilterType.all:
        return 'saloon';
      case SearchFilterType.salon:
        return 'saloon';
      case SearchFilterType.service:
        return 'service';
      case SearchFilterType.barber:
        return 'barber';
    }
  }
}

/// Search history item
class SearchHistoryItem {
  final String query;
  final DateTime timestamp;
  final SearchFilterType filterType;

  SearchHistoryItem({
    required this.query,
    required this.timestamp,
    required this.filterType,
  });

  factory SearchHistoryItem.fromJson(Map<String, dynamic> json) {
    return SearchHistoryItem(
      query: json['query'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      filterType: SearchFilterType.values.firstWhere(
        (type) => type.displayName == json['filterType'],
        orElse: () => SearchFilterType.all,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'query': query,
      'timestamp': timestamp.toIso8601String(),
      'filterType': filterType.displayName,
    };
  }
}

/// Popular search suggestions
class SearchSuggestion {
  final String text;
  final SearchFilterType type;
  final IconData icon;

  SearchSuggestion({
    required this.text,
    required this.type,
    required this.icon,
  });
}
