import 'dart:convert';

/// Review model for barber reviews
class Review {
  final String name;
  final String profileImageUrl;
  final String comment;
  final double rating;

  Review({
    required this.name,
    required this.profileImageUrl,
    required this.comment,
    required this.rating,
  });
}

/// Barber service model for services offered by barbers
class BarberServiceModel {
  final String serviceName;
  final double price;
  final String? serviceimage;
  final int length;

  BarberServiceModel({
    required this.serviceName,
    required this.price,
    this.serviceimage,
    required this.length,
  });

  factory BarberServiceModel.fromJson(Map<String, dynamic> json) {
    return BarberServiceModel(
      serviceName: json['serviceName'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      serviceimage: json['serviceimage'],
      length: json['length'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceName': serviceName,
      'price': price,
      'serviceimage': serviceimage,
      'length': length,
    };
  }
}

/// Barber request model for API calls
class BarberRequest {
  final String firstname;
  final String lastname;
  final String phone;
  final String email;
  final String bio;
  final bool available;
  final List<String> services;
  final String? profileimage;

  BarberRequest({
    required this.firstname,
    required this.lastname,
    required this.phone,
    required this.email,
    required this.bio,
    required this.available,
    required this.services,
    this.profileimage,
  });

  Map<String, dynamic> toJson() {
    return {
      'firstname': firstname,
      'lastname': lastname,
      'phone': phone,
      'email': email,
      'bio': bio,
      'available': available,
      'services': services,
      if (profileimage != null && profileimage!.isNotEmpty)
        'profileimage': profileimage,
    };
  }

  @override
  String toString() {
    return 'BarberRequest(firstname: $firstname, lastname: $lastname, phone: $phone, email: $email, bio: $bio, available: $available, services: $services, profileimage: $profileimage)';
  }
}

/// Barber response model for create/update operations
class BarberResponse {
  final bool success;
  final String message;
  final BarberData? barber;

  BarberResponse({required this.success, required this.message, this.barber});

  factory BarberResponse.fromJson(Map<String, dynamic> json) {
    return BarberResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      barber: json['barber'] != null
          ? BarberData.fromJson(json['barber'] as Map<String, dynamic>)
          : null,
    );
  }

  factory BarberResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return BarberResponse.fromJson(json);
    } catch (e) {
      return BarberResponse(
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message, 'barber': barber?.toJson()};
  }

  @override
  String toString() {
    return 'BarberResponse(success: $success, message: $message, barber: $barber)';
  }
}

/// Barber data model
class BarberData {
  final String id;
  final String firstname;
  final String lastname;
  final String? phone;
  final String? email;
  final String bio;
  final bool available;
  final String? profileImage;
  final List<String> serviceIds;
  final String saloonId;
  final bool isDelete;
  final String createdAt;
  final String updatedAt;
  final int totalStars;
  final int totalReviews;

  BarberData({
    required this.id,
    required this.firstname,
    required this.lastname,
    this.phone,
    this.email,
    required this.bio,
    required this.available,
    this.profileImage,
    required this.serviceIds,
    required this.saloonId,
    required this.isDelete,
    required this.createdAt,
    required this.updatedAt,
    required this.totalStars,
    required this.totalReviews,
  });

  factory BarberData.fromJson(Map<String, dynamic> json) {
    return BarberData(
      id: json['id'] as String? ?? '',
      firstname: json['firstname'] as String? ?? '',
      lastname: json['lastname'] as String? ?? '',
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      bio: json['bio'] as String? ?? '',
      available: json['available'] as bool? ?? false,
      profileImage:
          json['profileimage']
              as String?, // Note: API uses 'profileimage' not 'profileImage'
      serviceIds:
          (json['serviceIds'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      saloonId:
          json['saloonid'] as String? ??
          '', // Note: API uses 'saloonid' not 'saloonId'
      isDelete: json['isDelete'] as bool? ?? false,
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
      totalStars: json['totalStars'] as int? ?? 0,
      totalReviews: json['totalReviews'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstname': firstname,
      'lastname': lastname,
      'phone': phone,
      'email': email,
      'bio': bio,
      'available': available,
      'profileImage': profileImage,
      'serviceIds': serviceIds,
      'saloonId': saloonId,
      'isDelete': isDelete,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'totalStars': totalStars,
      'totalReviews': totalReviews,
    };
  }

  // Helper getters
  String get fullName => '$firstname $lastname'.trim();
  String get displayName => fullName.isNotEmpty ? fullName : 'Barber';
  String get displayBio => bio.isNotEmpty ? bio : 'No bio available';
  String get displayPhone => phone?.isNotEmpty == true ? phone! : 'No phone';
  String get displayEmail => email?.isNotEmpty == true ? email! : 'No email';
  bool get hasImage => profileImage != null && profileImage!.isNotEmpty;
  String get availabilityStatus => available ? 'Online' : 'Offline';
  double get averageRating =>
      totalReviews > 0 ? totalStars / totalReviews : 0.0;

  @override
  String toString() {
    return 'BarberData(id: $id, name: $fullName, phone: $phone, available: $available)';
  }
}

/// Barbers list response model
class BarbersListResponse {
  final int code;
  final bool success;
  final String message;
  final List<BarberData> data;

  BarbersListResponse({
    required this.code,
    required this.success,
    required this.message,
    required this.data,
  });

  factory BarbersListResponse.fromJson(Map<String, dynamic> json) {
    return BarbersListResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => BarberData.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  factory BarbersListResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return BarbersListResponse.fromJson(json);
    } catch (e) {
      return BarbersListResponse(
        code: 0,
        success: false,
        message: 'Failed to parse response: $e',
        data: [],
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data.map((barber) => barber.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'BarbersListResponse(code: $code, success: $success, message: $message, count: ${data.length})';
  }
}

/// Delete barber response model
class DeleteBarberResponse {
  final int code;
  final bool success;
  final String message;

  DeleteBarberResponse({
    required this.code,
    required this.success,
    required this.message,
  });

  factory DeleteBarberResponse.fromJson(Map<String, dynamic> json) {
    return DeleteBarberResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
    );
  }

  factory DeleteBarberResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return DeleteBarberResponse.fromJson(json);
    } catch (e) {
      return DeleteBarberResponse(
        code: 0,
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {'code': code, 'success': success, 'message': message};
  }

  @override
  String toString() {
    return 'DeleteBarberResponse(code: $code, success: $success, message: $message)';
  }
}

/// Service exception for error handling
class BarberServiceException implements Exception {
  final String message;
  final int? statusCode;

  BarberServiceException(this.message, {this.statusCode});

  String get userFriendlyMessage {
    switch (statusCode) {
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'Barber not found.';
      case 422:
        return 'Invalid data provided. Please check your input.';
      case 500:
        return 'Server error occurred. Please try again later.';
      default:
        return message.isNotEmpty
            ? message
            : 'An error occurred. Please try again.';
    }
  }

  @override
  String toString() {
    return 'BarberServiceException(message: $message, statusCode: $statusCode)';
  }
}

/// Enhanced Barber model for new API structure
class BarberModel {
  final String? id;
  final String firstname;
  final String lastname;
  final String? bio;
  final String? profileimage;
  final bool available;
  final String? saloonid;
  final bool? isDelete;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final double rating;
  final List<Review> reviews;
  final List<String>? assignedServices;
  final List<BarberServiceModel>? services; // New field for services

  BarberModel({
    this.id,
    required this.firstname,
    required this.lastname,
    this.bio,
    this.profileimage,
    this.available = false,
    this.saloonid,
    this.isDelete,
    this.createdAt,
    this.updatedAt,
    this.rating = 4.5,
    this.reviews = const [],
    this.assignedServices,
    this.services,
  });

  // Computed properties for UI compatibility
  String get name => '$firstname $lastname';
  String get specialization => bio ?? 'Barber';
  String get imageUrl => profileimage ?? '';
  bool get isAvailable => available;
  String get availableTime =>
      available ? 'Available Today' : 'Not Available Today';

  // Convert JSON to Barber object (for API response)
  factory BarberModel.fromJson(Map<String, dynamic> json) {
    List<BarberServiceModel>? servicesList;
    if (json['services'] != null && json['services'] is List) {
      try {
        servicesList = (json['services'] as List)
            .map((service) => BarberServiceModel.fromJson(service))
            .toList();
      } catch (e) {
        // If service parsing fails, set to empty list
        servicesList = [];
      }
    }

    return BarberModel(
      id: json['id']?.toString() ?? '',
      firstname: json['firstname']?.toString() ?? '',
      lastname: json['lastname']?.toString() ?? '',
      bio: json['bio']?.toString(),
      profileimage: json['profileimage']?.toString(),
      available: json['available'] ?? false,
      saloonid: json['saloonid']?.toString(),
      isDelete: json['isDelete'] ?? false,
      createdAt: json['createdAt'] != null
          ? _parseDateTime(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? _parseDateTime(json['updatedAt'])
          : null,
      assignedServices: json['assignedServices'] != null
          ? List<String>.from(json['assignedServices'])
          : null,
      services: servicesList ?? [],
    );
  }

  // Helper method to safely parse DateTime
  static DateTime? _parseDateTime(dynamic dateValue) {
    try {
      if (dateValue is String) {
        return DateTime.parse(dateValue);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Convert Barber object to JSON (for API request)
  Map<String, dynamic> toJson() {
    return {
      'firstname': firstname,
      'lastname': lastname,
      'bio': bio,
      'available': available,
      if (profileimage != null) 'profileimage': profileimage,
      if (services != null)
        'services': services!.map((s) => s.toJson()).toList(),
    };
  }

  // For API request body (add barber)
  Map<String, dynamic> toCreateJson({String? phone, String? email}) {
    return {
      'firstname': firstname,
      'lastname': lastname,
      'phone': phone ?? '9876543210',
      'email':
          email ??
          '${firstname.toLowerCase()}.${lastname.toLowerCase()}@example.com',
      'bio': bio ?? 'Experienced barber',
      'available': available,
      'profileimage': profileimage ?? '',
      'assignedServices': assignedServices ?? [],
    };
  }

  BarberModel copyWith({
    String? id,
    String? firstname,
    String? lastname,
    String? bio,
    String? profileimage,
    bool? available,
    String? saloonid,
    bool? isDelete,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? rating,
    List<Review>? reviews,
    List<String>? assignedServices,
    List<BarberServiceModel>? services,
  }) {
    return BarberModel(
      id: id ?? this.id,
      firstname: firstname ?? this.firstname,
      lastname: lastname ?? this.lastname,
      bio: bio ?? this.bio,
      profileimage: profileimage ?? this.profileimage,
      available: available ?? this.available,
      saloonid: saloonid ?? this.saloonid,
      isDelete: isDelete ?? this.isDelete,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rating: rating ?? this.rating,
      reviews: reviews ?? this.reviews,
      assignedServices: assignedServices ?? this.assignedServices,
      services: services ?? this.services,
    );
  }

  @override
  String toString() {
    return 'BarberModel(id: $id, name: $name, available: $available)';
  }
}

/// Single barber response model for get barber by ID API
class SingleBarberResponse {
  final String message;
  final BarberServices services;

  SingleBarberResponse({required this.message, required this.services});

  factory SingleBarberResponse.fromJson(Map<String, dynamic> json) {
    return SingleBarberResponse(
      message: json['message'] as String? ?? '',
      services: BarberServices.fromJson(
        json['services'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  factory SingleBarberResponse.fromString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return SingleBarberResponse.fromJson(json);
    } catch (e) {
      return SingleBarberResponse(
        message: 'Failed to parse response: $e',
        services: BarberServices(barber: null, services: []),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {'message': message, 'services': services.toJson()};
  }

  @override
  String toString() {
    return 'SingleBarberResponse(message: $message, services: $services)';
  }
}

/// Barber services container for single barber response
class BarberServices {
  final BarberModel? barber;
  final List<BarberServiceModel> services;

  BarberServices({required this.barber, required this.services});

  factory BarberServices.fromJson(Map<String, dynamic> json) {
    BarberModel? barber;

    // The barber data is under key "0"
    if (json['0'] != null) {
      try {
        barber = BarberModel.fromJson(json['0'] as Map<String, dynamic>);
      } catch (e) {
        // If parsing fails, set to null
        barber = null;
      }
    }

    List<BarberServiceModel> servicesList = [];
    if (json['services'] != null && json['services'] is List) {
      try {
        servicesList = (json['services'] as List)
            .map((service) => BarberServiceModel.fromJson(service))
            .toList();
      } catch (e) {
        // If service parsing fails, set to empty list
        servicesList = [];
      }
    }

    return BarberServices(barber: barber, services: servicesList);
  }

  Map<String, dynamic> toJson() {
    return {
      '0': barber?.toJson(),
      'services': services.map((s) => s.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'BarberServices(barber: $barber, services: ${services.length} services)';
  }
}
