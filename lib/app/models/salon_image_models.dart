import 'dart:developer';

/// Salon Image Upload Request Model
class SalonImageUploadRequest {
  final List<String> imageIds;

  SalonImageUploadRequest({required this.imageIds});

  Map<String, dynamic> toJson() {
    return {'imageIds': imageIds};
  }

  @override
  String toString() {
    return 'SalonImageUploadRequest(imageIds: $imageIds)';
  }
}

/// Salon Image Upload Response Model
class SalonImageUploadResponse {
  final int code;
  final bool success;
  final String message;
  final List<SalonImageData>? data;

  SalonImageUploadResponse({
    required this.code,
    required this.success,
    required this.message,
    this.data,
  });

  factory SalonImageUploadResponse.fromJson(Map<String, dynamic> json) {
    return SalonImageUploadResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null
          ? (json['data'] as List)
                .map(
                  (item) =>
                      SalonImageData.fromJson(item as Map<String, dynamic>),
                )
                .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'message': message,
      'data': data?.map((item) => item.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'SalonImageUploadResponse(code: $code, success: $success, message: $message)';
  }
}

/// Salon Image Data Model
class SalonImageData {
  final String id;
  final String imageUrl;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  SalonImageData({
    required this.id,
    required this.imageUrl,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalonImageData.fromJson(Map<String, dynamic> json) {
    return SalonImageData(
      id: json['id'] as String? ?? '',
      imageUrl: json['imageUrl'] as String? ?? '',
      description: json['description'] as String?,
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'imageUrl': imageUrl,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        log('Error parsing date: $value');
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  @override
  String toString() {
    return 'SalonImageData(id: $id, imageUrl: $imageUrl)';
  }
}

/// Salon Image Delete Request Model
class SalonImageDeleteRequest {
  final String id;

  SalonImageDeleteRequest({required this.id});

  Map<String, dynamic> toJson() {
    return {'id': id};
  }

  @override
  String toString() {
    return 'SalonImageDeleteRequest(id: $id)';
  }
}

/// Salon Image Delete Response Model
class SalonImageDeleteResponse {
  final int code;
  final bool success;
  final String message;

  SalonImageDeleteResponse({
    required this.code,
    required this.success,
    required this.message,
  });

  factory SalonImageDeleteResponse.fromJson(Map<String, dynamic> json) {
    return SalonImageDeleteResponse(
      code: json['code'] as int? ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'code': code, 'success': success, 'message': message};
  }

  @override
  String toString() {
    return 'SalonImageDeleteResponse(code: $code, success: $success, message: $message)';
  }
}

/// Image Upload Progress Model
class ImageUploadProgress {
  final int currentIndex;
  final int totalImages;
  final double progress;
  final String? fileName;
  final bool isCompleted;

  ImageUploadProgress({
    required this.currentIndex,
    required this.totalImages,
    required this.progress,
    this.fileName,
    this.isCompleted = false,
  });

  double get overallProgress {
    if (totalImages == 0) return 0.0;
    return (currentIndex - 1 + progress) / totalImages;
  }

  String get progressText {
    if (isCompleted) return 'Upload Complete';
    return 'Uploading ${currentIndex}/${totalImages} (${(progress * 100).toInt()}%)';
  }

  @override
  String toString() {
    return 'ImageUploadProgress(current: $currentIndex, total: $totalImages, progress: ${(progress * 100).toInt()}%)';
  }
}

/// Image Picker Source Enum
enum ImagePickerSource {
  camera('Camera'),
  gallery('Gallery');

  const ImagePickerSource(this.displayName);
  final String displayName;
}

/// Image Validation Result
class ImageValidationResult {
  final bool isValid;
  final String? errorMessage;
  final int? fileSizeBytes;
  final String? mimeType;

  ImageValidationResult({
    required this.isValid,
    this.errorMessage,
    this.fileSizeBytes,
    this.mimeType,
  });

  factory ImageValidationResult.valid({int? fileSizeBytes, String? mimeType}) {
    return ImageValidationResult(
      isValid: true,
      fileSizeBytes: fileSizeBytes,
      mimeType: mimeType,
    );
  }

  factory ImageValidationResult.invalid(String errorMessage) {
    return ImageValidationResult(isValid: false, errorMessage: errorMessage);
  }

  @override
  String toString() {
    return 'ImageValidationResult(isValid: $isValid, error: $errorMessage)';
  }
}

/// Image Compression Settings
class ImageCompressionSettings {
  final int maxWidth;
  final int maxHeight;
  final int quality;
  final int maxFileSizeBytes;

  const ImageCompressionSettings({
    this.maxWidth = 1024,
    this.maxHeight = 1024,
    this.quality = 85,
    this.maxFileSizeBytes = 10 * 1024 * 1024, // 10MB
  });

  static const ImageCompressionSettings service = ImageCompressionSettings(
    maxWidth: 800,
    maxHeight: 600,
    quality: 80,
  );

  static const ImageCompressionSettings barber = ImageCompressionSettings(
    maxWidth: 512,
    maxHeight: 512,
    quality: 85,
  );

  static const ImageCompressionSettings salon = ImageCompressionSettings(
    maxWidth: 1024,
    maxHeight: 768,
    quality: 85,
  );

  @override
  String toString() {
    return 'ImageCompressionSettings(${maxWidth}x${maxHeight}, quality: $quality)';
  }
}
