import 'package:get/get.dart';
import 'app_routes.dart';
import '../views/splash/splash_screen.dart';
import '../views/auth/social_login_screen.dart';
import '../views/auth/email_login_screen.dart';
import '../views/auth/email_register_screen.dart';
import '../views/auth/otp_verification_screen.dart';
import '../views/user/user_bottom_navigation.dart';
import '../views/owner/owner_bottom_navigation.dart';
import '../views/profile/user_detail_form_screen.dart';
import '../views/profile/owner_detail_form_screen.dart';
import '../views/salon_detail_page.dart';
import '../views/booking_page.dart';
import '../views/owner/profile/owner_edit_profile_screen.dart';
import '../views/owner/bank/owner_add_bank_account_screen.dart';
import '../views/owner/earnings/owner_earnings_history_screen.dart';
import '../views/user/appointments/my_appointments_screen.dart';
import '../views/user/appointments/reschedule_appointment_screen.dart';
import '../views/location/location_selection_screen.dart';
import '../views/user/profile/edit_profile_screen.dart';
import '../views/user/notifications/notification_screen.dart';

class AppPages {
  static final routes = [
    // Splash
    GetPage(name: AppRoutes.splash, page: () => const SplashScreen()),

    // Authentication
    GetPage(name: AppRoutes.socialLogin, page: () => const SocialLoginScreen()),
    GetPage(name: AppRoutes.emailLogin, page: () => const EmailLoginScreen()),
    GetPage(
      name: AppRoutes.emailRegister,
      page: () => const EmailRegisterScreen(),
    ),
    GetPage(
      name: AppRoutes.otpVerification,
      page: () => const OtpVerificationScreen(),
    ),

    // Profile Forms
    GetPage(
      name: AppRoutes.userDetailForm,
      page: () => const UserDetailFormScreen(),
    ),
    GetPage(
      name: AppRoutes.ownerDetailForm,
      page: () => const OwnerDetailFormScreen(),
    ),

    // Main App Navigation
    GetPage(
      name: AppRoutes.userBottomNavigation,
      page: () => const UserBottomNavigation(),
    ),
    GetPage(
      name: AppRoutes.ownerBottomNavigation,
      page: () => const OwnerBottomNavigation(),
    ),

    // Salon Detail
    GetPage(name: AppRoutes.salonDetail, page: () => const SalonDetailPage()),

    // Booking
    GetPage(name: AppRoutes.booking, page: () => const BookingPage()),

    // Edit Profile
    GetPage(name: AppRoutes.editProfile, page: () => const EditProfileScreen()),

    // Appointments
    GetPage(
      name: AppRoutes.appointments,
      page: () => const MyAppointmentsScreen(),
    ),

    // Reschedule Appointment
    GetPage(
      name: AppRoutes.rescheduleAppointment,
      page: () => const RescheduleAppointmentScreen(),
    ),
    GetPage(
      name: AppRoutes.locationSelection,
      page: () => const LocationSelectionScreen(),
    ),

    // Owner Edit Profile
    GetPage(
      name: AppRoutes.ownerEditProfile,
      page: () => const OwnerEditProfileScreen(),
    ),

    // Owner Add Bank Account
    GetPage(
      name: AppRoutes.ownerAddBankAccount,
      page: () => const OwnerAddBankAccountScreen(),
    ),

    // Owner Earnings History
    GetPage(
      name: AppRoutes.ownerEarningsHistory,
      page: () => const OwnerEarningsHistoryScreen(),
    ),

    // Notifications
    GetPage(
      name: AppRoutes.notifications,
      page: () => const NotificationScreen(),
    ),
  ];
}
