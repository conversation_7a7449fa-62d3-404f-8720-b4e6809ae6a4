// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDPwbDkbJ3WMjinLuO3lxbsSMCIOjUIbfw',
    appId: '1:956727998289:web:fc7154e0d16110b7028570',
    messagingSenderId: '956727998289',
    projectId: 'avtopia-6b597',
    authDomain: 'avtopia-6b597.firebaseapp.com',
    databaseURL: 'https://avtopia-6b597-default-rtdb.firebaseio.com',
    storageBucket: 'avtopia-6b597.appspot.com',
    measurementId: 'G-PJP0Z2TLF6',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDCn2wq5Ow7tBXDKxbtupJMdfJS_J48dTI',
    appId: '1:956727998289:android:447cd92694e8f1ad028570',
    messagingSenderId: '956727998289',
    projectId: 'avtopia-6b597',
    databaseURL: 'https://avtopia-6b597-default-rtdb.firebaseio.com',
    storageBucket: 'avtopia-6b597.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDRegIxvRRQjcmLPKIj5Kwe4RWKFSetjd8',
    appId: '1:956727998289:ios:354f8fca7310999d028570',
    messagingSenderId: '956727998289',
    projectId: 'avtopia-6b597',
    databaseURL: 'https://avtopia-6b597-default-rtdb.firebaseio.com',
    storageBucket: 'avtopia-6b597.appspot.com',
    androidClientId: '956727998289-23qn6gfjgk7n0714te3lj7gsbv0cpff1.apps.googleusercontent.com',
    iosClientId: '956727998289-8vgtg7euvl6uhghvunufmgrrkp1h6tpu.apps.googleusercontent.com',
    iosBundleId: 'com.iktworkspvt.barbrandco',
  );

}