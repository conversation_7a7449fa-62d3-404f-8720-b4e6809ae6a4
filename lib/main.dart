import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'app/constants/app_theme.dart';
import 'app/constants/app_constants.dart';
import 'app/routes/app_routes.dart';
import 'app/routes/app_pages.dart';
import 'app/controllers/auth_controller.dart';
import 'app/services/shared_preferences_service.dart';
import 'app/services/auth_service.dart';
import 'app/services/push_notifications_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    log('🚀 Starting app initialization...');

    // Initialize Firebase with timeout
    log('🔥 Initializing Firebase...');
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ).timeout(
      const Duration(seconds: 30),
      onTimeout: () {
        log('❌ Firebase initialization timed out');
        throw Exception('Firebase initialization timed out');
      },
    );
    log('✅ Firebase initialized successfully');

    // Initialize SharedPreferences first
    log('💾 Initializing SharedPreferences...');
    await SharedPreferencesService.init();
    log('✅ SharedPreferences initialized successfully');

    // Initialize authentication controller
    log('🔐 Initializing Authentication Controller...');
    Get.put(AuthController());
    log('✅ Authentication Controller initialized successfully');

    log('🎉 App initialization completed successfully');
    runApp(const SaloonApp());

    // Initialize FCM after app starts (like in your reference)
    log('📱 Initializing FCM and notifications...');
    try {
      await PushNotificationsManager().init();
      PushNotificationsManager().notificationConfigure();

      // Initialize device info service
      await AuthService.initializeFcmAndDeviceInfo();

      log('✅ FCM and notifications initialized successfully');
    } catch (e) {
      log('⚠️ Warning: FCM initialization failed: $e');
      // Continue app startup even if FCM fails
    }

    // Note: Permissions will be requested when needed (just-in-time)
    // This provides better UX with native iOS permission dialogs
    log('✅ App startup completed - permissions will be requested when needed');
  } catch (e) {
    log('❌ Error during app initialization: $e');
    // Run app without Firebase if initialization fails
    runApp(const SaloonAppFallback());
  }
}

class SaloonApp extends StatelessWidget {
  const SaloonApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      debugShowCheckedModeBanner: false,
      initialRoute: AppRoutes.splash,
      getPages: AppPages.routes,
    );
  }
}

class SaloonAppFallback extends StatelessWidget {
  const SaloonAppFallback({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Firebase Initialization Failed',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Please check your internet connection and try again.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Restart the app
                  main();
                },
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
