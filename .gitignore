# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release


# Flutter/Dart/Pub related
.dart_tool/
.packages
.pub-cache/
build/
flutter_*.png

# IntelliJ related
*.iml
.idea/
*.ipr
*.iws

# VS Code related
.vscode/

# Android related
android/.gradle/
android/captures/
android/local.properties
android/.idea/

# iOS related
ios/Flutter/.generated/
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/.symlinks/
ios/Pods/
ios/Runner.xcworkspace/
ios/.dart_tool/
ios/Flutter/App.framework
ios/Flutter/engine/

# macOS
macos/Flutter/ephemeral/
macos/Flutter/Flutter.framework
macos/Pods/
macos/.symlinks/
macos/Flutter/App.framework

# Web
build/web/

# Linux
linux/Flutter/ephemeral/

# Windows
windows/Flutter/ephemeral/

# Other
*.log
*.lock
*.tmp
*.swp
*.DS_Store
*.class

# Firebase generated
.firebase/

# Environment files (if used)
.env
