import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:saloon_app/app/controllers/owner/owner_home_controller.dart';
import 'package:saloon_app/app/models/owner_dashboard_models.dart';
import 'package:saloon_app/app/services/dashboard_service.dart';

void main() {
  group('Owner Dashboard Tests', () {
    late OwnerHomeController controller;

    setUp(() {
      // Initialize GetX
      Get.testMode = true;
      controller = OwnerHomeController();
    });

    tearDown(() {
      Get.reset();
    });

    test('Controller initializes with default values', () {
      expect(controller.weeklyEarnings.value, equals(0.0));
      expect(controller.weeklyBookings.value, equals(0));
      expect(controller.bankBalance.value, equals(0.0));
      expect(controller.isLoadingStats.value, equals(false));
      expect(controller.hasError.value, equals(false));
    });

    test('Chart filter change updates correctly', () async {
      // Test initial filter
      expect(controller.chartTimeFilter.value, equals('Weekly'));

      // Change to monthly
      await controller.onChartFilterChanged('Monthly');
      expect(controller.chartTimeFilter.value, equals('Monthly'));

      // Change back to weekly
      await controller.onChartFilterChanged('Weekly');
      expect(controller.chartTimeFilter.value, equals('Weekly'));
    });

    test('Error handling sets error state correctly', () async {
      // Test that error state is properly managed
      expect(controller.hasError.value, equals(false));
      expect(controller.errorMessage.value, equals(''));

      // Test retry functionality exists
      expect(controller.retryLoadData, isA<Function>());

      // Test that controller has all required observable properties
      expect(controller.weeklyEarnings, isA<RxDouble>());
      expect(controller.weeklyBookings, isA<RxInt>());
      expect(controller.bankBalance, isA<RxDouble>());
      expect(controller.chartData, isA<RxList<double>>());
    });

    test('Currency formatting works correctly', () {
      expect(controller.formatCurrency(1000.0), equals('₹1000.00'));
      expect(controller.formatCurrency(1234.56), equals('₹1234.56'));
      expect(controller.formatCurrency(0.0), equals('₹0.00'));
    });
  });

  group('Owner Dashboard Models Tests', () {
    test('OwnerDashboardData parses JSON correctly', () {
      final json = {
        'availableBalance': 100000.0,
        'lockedBalance': 5000.0,
        'totalEarning': 15000.0,
        'isLocked': false,
        'lockedUntil': null,
        'appointmentData': {'pending': []},
      };

      final data = OwnerDashboardData.fromJson(json);

      expect(data.availableBalance, equals(100000.0));
      expect(data.lockedBalance, equals(5000.0));
      expect(data.totalEarning, equals(15000.0));
      expect(data.isLocked, equals(false));
      expect(data.lockedUntil, isNull);
      expect(data.weeklyEarnings, equals(15000.0)); // Computed property
      expect(data.bankBalance, equals(100000.0)); // Computed property
    });

    test('Appointment model parses JSON correctly', () {
      final json = {
        'id': '123',
        'barberName': 'Mike Smith',
        'userName': 'John Doe',
        'amount': 500.0,
        'bookingType': 'full_payment',
        'status': 'confirmed',
        'startTime': '10:30:00',
        'endTime': '11:30:00',
        'appointmentDate': '2025-07-19',
        'createdAt': '2025-07-17T21:20:35.976Z',
      };

      final appointment = Appointment.fromJson(json);

      expect(appointment.id, equals('123'));
      expect(appointment.userName, equals('John Doe'));
      expect(appointment.barberName, equals('Mike Smith'));
      expect(appointment.status, equals('confirmed'));
      expect(appointment.amount, equals(500.0));
      expect(appointment.displayAmount, equals('₹500'));
      expect(appointment.customerName, equals('John Doe')); // Computed property
      expect(appointment.totalAmount, equals(500.0)); // Computed property
    });

    test('WeeklyDataResponse parses JSON correctly', () {
      final json = {
        'success': true,
        'data': [1000.0, 1200.0, 800.0, 1500.0, 1100.0, 900.0, 1300.0],
        'message': 'Success',
      };

      final response = WeeklyDataResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.weeklyData.length, equals(7));
      expect(response.weeklyData.first, equals(1000.0));
      expect(response.message, equals('Success'));
    });
  });

  group('Dashboard Service Helper Methods Tests', () {
    test('Format currency works correctly', () {
      expect(DashboardService.formatCurrency(1000.0), equals('₹1000.00'));
      expect(DashboardService.formatCurrency(1234.56), equals('₹1234.56'));
      expect(DashboardService.formatCurrency(0.0), equals('₹0.00'));
    });

    test('Get total appointments works correctly', () {
      final data = OwnerDashboardData(
        availableBalance: 50000.0,
        lockedBalance: 0.0,
        totalEarning: 10000.0,
        isLocked: false,
        appointmentData: AppointmentData(
          pending: [
            Appointment(
              id: '1',
              barberName: 'Barber 1',
              userName: 'John',
              amount: 300.0,
              bookingType: 'full_payment',
              status: 'pending',
              startTime: '10:00:00',
              endTime: '11:00:00',
              appointmentDate: '2025-07-19',
              createdAt: '2025-07-17T21:20:35.976Z',
            ),
          ],
          completed: [
            Appointment(
              id: '2',
              barberName: 'Barber 2',
              userName: 'Jane',
              amount: 500.0,
              bookingType: 'full_payment',
              status: 'completed',
              startTime: '14:00:00',
              endTime: '15:00:00',
              appointmentDate: '2025-07-18',
              createdAt: '2025-07-16T21:20:35.976Z',
            ),
          ],
        ),
      );

      final total = DashboardService.getTotalAppointments(data);
      expect(total, equals(2));
    });
  });
}
